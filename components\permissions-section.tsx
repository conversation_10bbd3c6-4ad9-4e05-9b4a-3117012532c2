'use client';

import { useState, useEffect, useMemo } from 'react';
import { AppPermissions } from '@/lib/types';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { useStore } from '@/context/store';

interface PermissionsSectionProps {
  currentUserPermissions: AppPermissions;
  onPermissionsChange: (permissions: AppPermissions) => void;
  currentUserId?: number;
}

const allSections = [
  { id: 'dashboard', label: 'الرئيسية', icon: '📊' },
  { id: 'clients', label: 'العملاء', icon: '👥' },
  { id: 'warehouses', label: 'إدارة المخازن', icon: '🏬' },
  { id: 'inventory', label: 'المخزون', icon: '📦' },
  { id: 'supply', label: 'التوريد', icon: '🚚' },
  { id: 'sales', label: 'المبيعات', icon: '💰' },
  { id: 'requests', label: 'طلبات العملاء', icon: '📋' },
  { id: 'returns', label: 'المرتجعات', icon: '↩️' },
  { id: 'maintenance', label: 'الصيانة', icon: '🔧' },
  { id: 'maintenanceTransfer', label: 'نقل الصيانة', icon: '🔄' },
  { id: 'warehouseTransfer', label: 'نقل المستودعات', icon: '📦➡️' },
  { id: 'grading', label: 'الدرجات', icon: '⭐' },
  { id: 'track', label: 'التتبع', icon: '📍' },
  { id: 'stocktaking', label: 'الجرد', icon: '📊' },
  { id: 'acceptDevices', label: 'قبول الأجهزة', icon: '✅' },
  { id: 'messaging', label: 'الرسائل', icon: '💬' },
  { id: 'reports', label: 'التقارير', icon: '📈' },
  { id: 'users', label: 'المستخدمين', icon: '👤' },
  { id: 'settings', label: 'الإعدادات', icon: '⚙️' },
  { id: 'pricing', label: 'التسعير', icon: '💲' },
];

export function PermissionsSection({ currentUserPermissions, onPermissionsChange, currentUserId }: PermissionsSectionProps) {
  const { currentUser } = useStore();
  const [permissions, setPermissions] = useState<AppPermissions>(currentUserPermissions);
  const [selectedSections, setSelectedSections] = useState<string[]>([]);

  // فلترة الأقسام بناءً على صلاحيات المستخدم الحالي (الذي يقوم بإنشاء المستخدم الجديد)
  const availableSections = useMemo(() => {
    // إظهار جميع الأقسام للمدير دائماً
    if (!currentUser || !currentUser.permissions || typeof currentUser.permissions !== 'object' || 
        currentUser.username === 'admin' || currentUser.id === 1 || currentUser.name === 'مدير النظام') {
      return allSections;
    }
    
    // إظهار جميع الأقسام دائماً بغض النظر عن صلاحيات المستخدم الحالي
    // لكن سيتم تعطيل الأقسام التي لا يملك عليها صلاحيات
    return allSections;
  }, [currentUser]);

  // تحديث الصلاحيات عند تغيير المستخدم
  useEffect(() => {
    // التحقق من وجود الصلاحيات قبل المعالجة
    if (!currentUserPermissions || typeof currentUserPermissions !== 'object') {
      setPermissions({});
      setSelectedSections([]);
      return;
    }

    setPermissions(currentUserPermissions);

    // العثور على الأقسام المحددة (فقط من الأقسام المتاحة)
    const sections = Object.keys(currentUserPermissions).filter(key => {
      const perm = currentUserPermissions[key as keyof AppPermissions];
      const isAvailable = availableSections.some(section => section.id === key);
      return isAvailable && (perm?.view || perm?.create || perm?.edit || perm?.delete);
    });

    setSelectedSections(sections);
  }, [currentUserPermissions, currentUserId, availableSections]);

  const handleSectionToggle = (sectionId: string) => {
    const isCurrentlySelected = selectedSections.includes(sectionId);
    
    // التحقق من أن المستخدم الحالي يملك صلاحية على هذا القسم
    const hasCurrentUserAccess = currentUser?.permissions?.[sectionId as keyof AppPermissions];
    const canManageSection = hasCurrentUserAccess?.view || hasCurrentUserAccess?.create || 
                           hasCurrentUserAccess?.edit || hasCurrentUserAccess?.delete;
    
    // السماح للمدير بإضافة أي قسم دائماً
    const isAdmin = currentUser?.username === 'admin' || currentUser?.id === 1 || currentUser?.name === 'مدير النظام';
    
    // نعرض تنبيهًا فقط ولكن نسمح بالإضافة في جميع الحالات
    if (!isCurrentlySelected && !canManageSection && !isAdmin) {
      const confirmAdd = confirm('ليس لديك صلاحية على هذا القسم. هل ترغب في إضافته على أي حال؟');
      if (!confirmAdd) {
        return;
      }
    }

    if (isCurrentlySelected) {
      // إزالة القسم
      const newSections = selectedSections.filter(id => id !== sectionId);
      setSelectedSections(newSections);

      // إزالة الصلاحيات
      const newPermissions = { ...permissions };
      newPermissions[sectionId as keyof AppPermissions] = {
        view: false,
        create: false,
        edit: false,
        delete: false,
      };
      setPermissions(newPermissions);
      onPermissionsChange(newPermissions);
    } else {
      // إضافة القسم دائمًا بغض النظر عن صلاحيات المستخدم الحالي
      const newSections = [...selectedSections, sectionId];
      setSelectedSections(newSections);

      // إضافة صلاحية العرض افتراضياً
      const newPermissions = { ...permissions };
      newPermissions[sectionId as keyof AppPermissions] = {
        view: true,
        create: false,
        edit: false,
        delete: false,
      };
      setPermissions(newPermissions);
      onPermissionsChange(newPermissions);
    }
  };

  const handlePermissionChange = (sectionId: string, permissionType: 'view' | 'create' | 'edit' | 'delete', checked: boolean) => {
    const newPermissions = { ...permissions };
    newPermissions[sectionId as keyof AppPermissions] = {
      ...newPermissions[sectionId as keyof AppPermissions],
      [permissionType]: checked,
    };
    setPermissions(newPermissions);
    onPermissionsChange(newPermissions);
  };

  return (
    <div className="space-y-4">
      {/* رسالة توضيحية */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
        <div className="flex items-start gap-2">
          <span className="text-blue-600 text-lg">ℹ️</span>
          <div className="text-sm text-blue-800">
            <p className="font-medium">ملاحظة مهمة:</p>
            <p>تظهر جميع الأقسام في القائمة دائماً. يمكنك إضافة الأقسام التي تملك صلاحية عليها فقط.</p>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
        <div className="lg:col-span-1 space-y-2">
          <h4 className="font-medium text-gray-800 flex items-center gap-2">
            <span className="text-blue-600">🏢</span>
            الأقسام المتاحة ({availableSections.length})
          </h4>
        <div className="border rounded-lg p-3 space-y-1 max-h-80 overflow-y-auto bg-gray-50">
          {availableSections.length > 0 ? (
            availableSections.map((section) => {
              const isSelected = selectedSections.includes(section.id);
              const hasCurrentUserAccess = currentUser?.permissions?.[section.id as keyof AppPermissions];
              const canManageSection = hasCurrentUserAccess?.view || hasCurrentUserAccess?.create || 
                                     hasCurrentUserAccess?.edit || hasCurrentUserAccess?.delete;
              const isAdmin = currentUser?.username === 'admin' || currentUser?.id === 1 || currentUser?.name === 'مدير النظام';
              const isDisabled = !canManageSection && !isAdmin;
              
              return (
                <div
                  key={section.id}
                  className={`flex items-center gap-2 p-2 rounded-md transition-colors ${
                    isSelected
                      ? 'bg-blue-100 border border-blue-300 text-blue-900'
                      : isDisabled 
                        ? 'bg-gray-100 border border-gray-200 text-gray-400'
                        : 'hover:bg-white border border-transparent text-gray-700 hover:text-gray-900'
                  }`}
                >
                  <Checkbox
                    checked={isSelected}
                    onCheckedChange={() => handleSectionToggle(section.id)}
                    id={`section-checkbox-${section.id}`}
                    disabled={isDisabled}
                  />
                  <Label
                    htmlFor={`section-checkbox-${section.id}`}
                    className={`flex items-center gap-2 cursor-pointer text-sm flex-1 font-bold ${
                      isSelected
                        ? 'text-blue-900'
                        : isDisabled
                          ? 'text-gray-400 cursor-not-allowed'
                          : 'text-gray-800'
                    }`}
                  >
                    <span className="text-base">{section.icon}</span>
                    <span>{section.label}</span>
                    {isDisabled && <span className="text-xs mr-auto text-gray-400">(غير متاح)</span>}
                  </Label>
                </div>
              );
            })
          ) : (
            <div className="text-center text-gray-500 py-8 bg-white rounded-lg border-2 border-dashed border-gray-200">
              <div className="text-3xl mb-2">🔒</div>
              <p className="font-medium">لا توجد أقسام متاحة</p>
              <p className="text-sm mt-1">ليس لديك صلاحية لإدارة أي قسم</p>
            </div>
          )}
        </div>
      </div>
      
      <div className="lg:col-span-2 space-y-2">
        <h4 className="font-medium text-gray-800 flex items-center gap-2">
          <span className="text-green-600">🔐</span>
          مستويات الصلاحية
        </h4>
        <div className="border rounded-lg p-3 space-y-3 max-h-80 overflow-y-auto bg-gray-50">
          {selectedSections.length > 0 ? (
            selectedSections.map((sectionId) => {
              const section = allSections.find((s) => s.id === sectionId);
              const sectionPermissions = permissions[sectionId as keyof AppPermissions];
              
              return (
                <div key={sectionId} className="bg-gray-50 rounded-lg p-3 border border-gray-200 shadow-sm">
                  <h5 className="font-semibold mb-3 flex items-center gap-2 text-gray-800">
                    <span className="text-lg">{section?.icon}</span>
                    <span>{section?.label}</span>
                  </h5>
                  <div className="flex flex-wrap gap-2">
                    <label className="flex items-center gap-2 cursor-pointer px-3 py-2 rounded-md hover:bg-blue-50 transition-colors border border-transparent hover:border-blue-200">
                      <Checkbox
                        checked={Boolean(sectionPermissions?.view)}
                        onCheckedChange={(checked) => handlePermissionChange(sectionId, 'view', checked as boolean)}
                      />
                      <span className="text-sm font-medium text-gray-700">👁️ عرض</span>
                    </label>
                    <label className="flex items-center gap-2 cursor-pointer px-3 py-2 rounded-md hover:bg-green-50 transition-colors border border-transparent hover:border-green-200">
                      <Checkbox
                        checked={Boolean(sectionPermissions?.create)}
                        onCheckedChange={(checked) => handlePermissionChange(sectionId, 'create', checked as boolean)}
                      />
                      <span className="text-sm font-medium text-gray-700">➕ إنشاء</span>
                    </label>
                    <label className="flex items-center gap-2 cursor-pointer px-3 py-2 rounded-md hover:bg-yellow-50 transition-colors border border-transparent hover:border-yellow-200">
                      <Checkbox
                        checked={Boolean(sectionPermissions?.edit)}
                        onCheckedChange={(checked) => handlePermissionChange(sectionId, 'edit', checked as boolean)}
                      />
                      <span className="text-sm font-medium text-gray-700">✏️ تعديل</span>
                    </label>
                    <label className="flex items-center gap-2 cursor-pointer px-3 py-2 rounded-md hover:bg-red-50 transition-colors border border-transparent hover:border-red-200">
                      <Checkbox
                        checked={Boolean(sectionPermissions?.delete)}
                        onCheckedChange={(checked) => handlePermissionChange(sectionId, 'delete', checked as boolean)}
                      />
                      <span className="text-sm font-medium text-gray-700">🗑️ حذف</span>
                    </label>
                  </div>
                </div>
              );
            })
          ) : (
            <div className="text-center text-gray-500 py-12 bg-white rounded-lg border-2 border-dashed border-gray-200">
              <div className="text-4xl mb-2">📋</div>
              <p className="font-medium">لم يتم اختيار أي قسم</p>
              <p className="text-sm mt-1">الرجاء اختيار قسم من القائمة لعرض الصلاحيات</p>
            </div>
          )}
        </div>
        </div>
      </div>
    </div>
  );
}
