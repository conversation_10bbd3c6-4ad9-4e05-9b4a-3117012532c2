const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function migrateData() {
  console.log('🚀 بدء ترحيل البيانات من حقول JSON إلى الجداول الوسيطة...');

  try {
    // 1. ترحيل بيانات SupplyOrder
    console.log('📦 ترحيل بيانات أوامر التوريد...');
    const supplyOrders = await prisma.supplyOrder.findMany();
    
    for (const order of supplyOrders) {
      if (order.items_json && typeof order.items_json === 'object') {
        const items = Array.isArray(order.items_json) ? order.items_json : JSON.parse(order.items_json);
        
        for (const item of items) {
          await prisma.supplyOrderItem.create({
            data: {
              supplyOrderId: order.id,
              imei: item.imei || '',
              model: item.model || '',
              manufacturer: item.manufacturer || '',
              condition: item.condition || 'جديد'
            }
          });
        }
      }
    }

    // 2. ترحيل بيانات Sale
    console.log('💰 ترحيل بيانات المبيعات...');
    const sales = await prisma.sale.findMany();
    
    for (const sale of sales) {
      if (sale.items_json && typeof sale.items_json === 'object') {
        const items = Array.isArray(sale.items_json) ? sale.items_json : JSON.parse(sale.items_json);
        
        for (const item of items) {
          await prisma.saleItem.create({
            data: {
              saleId: sale.id,
              deviceId: item.deviceId || '',
              model: item.model || '',
              price: parseFloat(item.price) || 0,
              condition: item.condition || 'جديد'
            }
          });
        }
      }
    }

    // 3. ترحيل بيانات Return
    console.log('↩️ ترحيل بيانات المرتجعات...');
    const returns = await prisma.return.findMany();
    
    for (const returnRecord of returns) {
      if (returnRecord.items_json && typeof returnRecord.items_json === 'object') {
        const items = Array.isArray(returnRecord.items_json) ? returnRecord.items_json : JSON.parse(returnRecord.items_json);
        
        for (const item of items) {
          await prisma.returnItem.create({
            data: {
              returnId: returnRecord.id,
              deviceId: item.deviceId || '',
              model: item.model || '',
              returnReason: item.returnReason || '',
              replacementDeviceId: item.replacementDeviceId || null,
              isReplacement: item.isReplacement || false,
              originalDeviceId: item.originalDeviceId || null
            }
          });
        }
      }
    }

    // 4. ترحيل بيانات EvaluationOrder
    console.log('📊 ترحيل بيانات أوامر التقييم...');
    const evaluationOrders = await prisma.evaluationOrder.findMany();
    
    for (const order of evaluationOrders) {
      if (order.items_json && typeof order.items_json === 'object') {
        const items = Array.isArray(order.items_json) ? order.items_json : JSON.parse(order.items_json);
        
        for (const item of items) {
          await prisma.evaluationOrderItem.create({
            data: {
              evaluationOrderId: order.id,
              deviceId: item.deviceId || '',
              model: item.model || '',
              externalGrade: item.externalGrade || '',
              screenGrade: item.screenGrade || '',
              networkGrade: item.networkGrade || '',
              finalGrade: item.finalGrade || '',
              fault: item.fault || null,
              damageType: item.damageType || null
            }
          });
        }
      }
    }

    // 5. ترحيل بيانات MaintenanceOrder
    console.log('🔧 ترحيل بيانات أوامر الصيانة...');
    const maintenanceOrders = await prisma.maintenanceOrder.findMany();
    
    for (const order of maintenanceOrders) {
      if (order.items_json && typeof order.items_json === 'object') {
        const items = Array.isArray(order.items_json) ? order.items_json : JSON.parse(order.items_json);
        
        for (const item of items) {
          await prisma.maintenanceOrderItem.create({
            data: {
              maintenanceOrderId: order.id,
              deviceId: item.deviceId || '',
              model: item.model || '',
              fault: item.fault || null,
              notes: item.notes || null
            }
          });
        }
      }
    }

    // 6. ترحيل بيانات MaintenanceReceiptOrder
    console.log('📋 ترحيل بيانات إيصالات الصيانة...');
    const maintenanceReceipts = await prisma.maintenanceReceiptOrder.findMany();
    
    for (const receipt of maintenanceReceipts) {
      if (receipt.items_json && typeof receipt.items_json === 'object') {
        const items = Array.isArray(receipt.items_json) ? receipt.items_json : JSON.parse(receipt.items_json);
        
        for (const item of items) {
          await prisma.maintenanceReceiptOrderItem.create({
            data: {
              maintenanceReceiptOrderId: receipt.id,
              deviceId: item.deviceId || '',
              model: item.model || '',
              result: item.result || '',
              fault: item.fault || null,
              damage: item.damage || null,
              notes: item.notes || null
            }
          });
        }
      }
    }

    // 7. ترحيل بيانات DeliveryOrder
    console.log('🚚 ترحيل بيانات أوامر التسليم...');
    const deliveryOrders = await prisma.deliveryOrder.findMany();
    
    for (const order of deliveryOrders) {
      if (order.items_json && typeof order.items_json === 'object') {
        const items = Array.isArray(order.items_json) ? order.items_json : JSON.parse(order.items_json);
        
        for (const item of items) {
          await prisma.deliveryOrderItem.create({
            data: {
              deliveryOrderId: order.id,
              deviceId: item.deviceId || '',
              model: item.model || '',
              result: item.result || '',
              fault: item.fault || null,
              damage: item.damage || null,
              notes: item.notes || null
            }
          });
        }
      }
    }

    console.log('✅ تم ترحيل جميع البيانات بنجاح!');
    
  } catch (error) {
    console.error('❌ خطأ في ترحيل البيانات:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// تشغيل الترحيل
if (require.main === module) {
  migrateData()
    .then(() => {
      console.log('🎉 انتهى ترحيل البيانات بنجاح!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 فشل في ترحيل البيانات:', error);
      process.exit(1);
    });
}

module.exports = { migrateData };
