const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function verifyAllTables() {
  console.log('🔍 التحقق من جميع الجداول الوسيطة...\n');

  const tables = [
    { name: 'SupplyOrderItem', model: 'supplyOrderItem', description: 'عناصر أوامر التوريد' },
    { name: 'SaleItem', model: 'saleItem', description: 'عناصر المبيعات' },
    { name: 'ReturnItem', model: 'returnItem', description: 'عناصر المرتجعات' },
    { name: 'EvaluationOrderItem', model: 'evaluationOrderItem', description: 'عناصر أوامر التقييم' },
    { name: 'MaintenanceOrderItem', model: 'maintenanceOrderItem', description: 'عناصر أوامر الصيانة' },
    { name: 'DeliveryOrderItem', model: 'deliveryOrderItem', description: 'عناصر أوامر التسليم' },
    { name: 'MaintenanceReceiptOrderItem', model: 'maintenanceReceiptOrderItem', description: 'عناصر أوامر استلام الصيانة' }
  ];

  let allTablesExist = true;

  for (const table of tables) {
    try {
      const count = await prisma[table.model].count();
      console.log(`✅ ${table.name} (${table.description}): ${count} سجل`);
    } catch (error) {
      console.log(`❌ ${table.name} (${table.description}): غير موجود أو خطأ`);
      allTablesExist = false;
    }
  }

  console.log('\n📊 التحقق من الجداول الأصلية...');
  
  const mainTables = [
    { name: 'SupplyOrder', model: 'supplyOrder', description: 'أوامر التوريد' },
    { name: 'Sale', model: 'sale', description: 'المبيعات' },
    { name: 'Return', model: 'return', description: 'المرتجعات' },
    { name: 'EvaluationOrder', model: 'evaluationOrder', description: 'أوامر التقييم' },
    { name: 'MaintenanceOrder', model: 'maintenanceOrder', description: 'أوامر الصيانة' },
    { name: 'DeliveryOrder', model: 'deliveryOrder', description: 'أوامر التسليم' },
    { name: 'MaintenanceReceiptOrder', model: 'maintenanceReceiptOrder', description: 'أوامر استلام الصيانة' }
  ];

  for (const table of mainTables) {
    try {
      const count = await prisma[table.model].count();
      console.log(`✅ ${table.name} (${table.description}): ${count} سجل`);
    } catch (error) {
      console.log(`❌ ${table.name} (${table.description}): غير موجود أو خطأ`);
      allTablesExist = false;
    }
  }

  console.log('\n🔗 التحقق من العلاقات...');
  
  try {
    // اختبار العلاقات
    const supplyOrderWithItems = await prisma.supplyOrder.findFirst({
      include: { items: true }
    });
    console.log('✅ علاقة SupplyOrder -> SupplyOrderItem تعمل');

    const saleWithItems = await prisma.sale.findFirst({
      include: { items: true }
    });
    console.log('✅ علاقة Sale -> SaleItem تعمل');

    const returnWithItems = await prisma.return.findFirst({
      include: { items: true }
    });
    console.log('✅ علاقة Return -> ReturnItem تعمل');

    const evaluationWithItems = await prisma.evaluationOrder.findFirst({
      include: { items: true }
    });
    console.log('✅ علاقة EvaluationOrder -> EvaluationOrderItem تعمل');

    const maintenanceWithItems = await prisma.maintenanceOrder.findFirst({
      include: { items: true }
    });
    console.log('✅ علاقة MaintenanceOrder -> MaintenanceOrderItem تعمل');

    const deliveryWithItems = await prisma.deliveryOrder.findFirst({
      include: { items: true }
    });
    console.log('✅ علاقة DeliveryOrder -> DeliveryOrderItem تعمل');

    const receiptWithItems = await prisma.maintenanceReceiptOrder.findFirst({
      include: { items: true }
    });
    console.log('✅ علاقة MaintenanceReceiptOrder -> MaintenanceReceiptOrderItem تعمل');

  } catch (error) {
    console.log('❌ خطأ في اختبار العلاقات:', error.message);
    allTablesExist = false;
  }

  if (allTablesExist) {
    console.log('\n🎉 جميع الجداول والعلاقات تعمل بشكل صحيح!');
    console.log('✅ تم تطبيق إعادة الهيكلة على جميع الأقسام بنجاح');
  } else {
    console.log('\n⚠️ هناك مشاكل في بعض الجداول أو العلاقات');
  }

  await prisma.$disconnect();
  return allTablesExist;
}

if (require.main === module) {
  verifyAllTables()
    .then((success) => {
      process.exit(success ? 0 : 1);
    })
    .catch((error) => {
      console.error('💥 خطأ في التحقق:', error);
      process.exit(1);
    });
}

module.exports = { verifyAllTables };
