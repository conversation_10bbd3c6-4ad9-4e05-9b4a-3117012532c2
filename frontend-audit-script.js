const fs = require('fs');
const path = require('path');
const glob = require('glob');

async function auditFrontendPages() {
  const auditResults = {
    timestamp: new Date().toISOString(),
    pages: {},
    apiUsage: {},
    storeUsage: {},
    errors: [],
    warnings: [],
    recommendations: []
  };

  console.log('🔍 بدء فحص الواجهة الأمامية...\n');

  // 1. فحص الصفحات الرئيسية
  console.log('📄 فحص الصفحات الرئيسية...');
  
  const mainPages = [
    { name: 'Supply', path: 'app/(main)/supply/page.tsx' },
    { name: 'Sales', path: 'app/(main)/sales/page.tsx' },
    { name: 'Returns', path: 'app/(main)/returns/page.tsx' },
    { name: 'Maintenance', path: 'app/(main)/maintenance/page.tsx' },
    { name: 'Inventory', path: 'app/(main)/inventory/page.tsx' },
    { name: 'Track', path: 'app/(main)/track/page.tsx' },
    { name: 'Stocktaking', path: 'app/(main)/stocktaking/page.tsx' },
    { name: 'Requests', path: 'app/(main)/requests/page.tsx' },
    { name: 'Messaging', path: 'app/(main)/messaging/page.tsx' },
    { name: 'Grading', path: 'app/(main)/grading/page.tsx' },
    { name: 'Clients', path: 'app/(main)/clients/page.tsx' },
    { name: 'Warehouses', path: 'app/(main)/warehouses/page.tsx' }
  ];

  for (const page of mainPages) {
    try {
      if (fs.existsSync(page.path)) {
        const content = fs.readFileSync(page.path, 'utf8');
        const lines = content.split('\n').length;
        
        // فحص استخدام useStore
        const usesStore = content.includes('useStore');
        const usesApiCalls = content.includes('fetch(') || content.includes('apiClient');
        
        // فحص استخدام JSON.parse
        const usesJsonParse = content.includes('JSON.parse');
        
        // فحص استخدام localStorage
        const usesLocalStorage = content.includes('localStorage');
        
        auditResults.pages[page.name] = {
          exists: true,
          lines: lines,
          usesStore: usesStore,
          usesApiCalls: usesApiCalls,
          usesJsonParse: usesJsonParse,
          usesLocalStorage: usesLocalStorage,
          status: usesApiCalls ? 'محدث' : 'يحتاج تحديث'
        };
        
        const status = usesApiCalls ? '✅' : '❌';
        const storeStatus = usesStore ? '(يستخدم Store)' : '';
        console.log(`${status} ${page.name}: ${lines} سطر ${storeStatus}`);
        
        if (usesStore && !usesApiCalls) {
          auditResults.warnings.push(`${page.name}: لا يزال يستخدم Store بدلاً من APIs`);
        }
        
        if (usesJsonParse) {
          auditResults.warnings.push(`${page.name}: يستخدم JSON.parse (قد يحتاج تحديث)`);
        }
        
      } else {
        auditResults.pages[page.name] = {
          exists: false,
          error: 'الملف غير موجود'
        };
        auditResults.errors.push(`${page.name}: الملف غير موجود`);
        console.log(`❌ ${page.name}: الملف غير موجود`);
      }
    } catch (error) {
      auditResults.errors.push(`${page.name}: ${error.message}`);
      console.log(`❌ ${page.name}: خطأ - ${error.message}`);
    }
  }

  // 2. فحص APIs
  console.log('\n🌐 فحص APIs...');
  
  const apiRoutes = glob.sync('app/api/**/route.ts');
  
  for (const apiRoute of apiRoutes) {
    try {
      const content = fs.readFileSync(apiRoute, 'utf8');
      const apiName = apiRoute.replace('app/api/', '').replace('/route.ts', '');
      
      const hasGet = content.includes('export async function GET');
      const hasPost = content.includes('export async function POST');
      const hasPut = content.includes('export async function PUT');
      const hasDelete = content.includes('export async function DELETE');
      
      const usesPrisma = content.includes('prisma.');
      const hasAuth = content.includes('requireAuth');
      const hasTransaction = content.includes('executeInTransaction');
      
      auditResults.apiUsage[apiName] = {
        methods: {
          GET: hasGet,
          POST: hasPost,
          PUT: hasPut,
          DELETE: hasDelete
        },
        usesPrisma: usesPrisma,
        hasAuth: hasAuth,
        hasTransaction: hasTransaction
      };
      
      const methods = [hasGet && 'GET', hasPost && 'POST', hasPut && 'PUT', hasDelete && 'DELETE']
        .filter(Boolean).join(', ');
      
      console.log(`✅ ${apiName}: ${methods} ${usesPrisma ? '(Prisma)' : ''} ${hasAuth ? '(Auth)' : ''}`);
      
    } catch (error) {
      auditResults.errors.push(`API ${apiRoute}: ${error.message}`);
      console.log(`❌ API ${apiRoute}: خطأ - ${error.message}`);
    }
  }

  // 3. فحص استخدام Store
  console.log('\n🗄️ فحص استخدام Store...');
  
  try {
    const storeFile = 'context/store.tsx';
    if (fs.existsSync(storeFile)) {
      const content = fs.readFileSync(storeFile, 'utf8');
      
      // فحص الدوال المُصدرة
      const exportedFunctions = content.match(/const\s+(\w+)\s*=/g) || [];
      const apiCalls = content.match(/fetch\(/g) || [];
      
      auditResults.storeUsage = {
        exists: true,
        exportedFunctions: exportedFunctions.length,
        apiCalls: apiCalls.length,
        stillUsesLocalStorage: content.includes('localStorage'),
        stillUsesJsonData: content.includes('initialData')
      };
      
      console.log(`✅ Store: ${exportedFunctions.length} دالة، ${apiCalls.length} استدعاء API`);
      
      if (content.includes('localStorage')) {
        auditResults.warnings.push('Store: لا يزال يستخدم localStorage');
      }
      
    } else {
      auditResults.storeUsage = { exists: false };
      console.log('❌ Store: الملف غير موجود');
    }
  } catch (error) {
    auditResults.errors.push(`Store: ${error.message}`);
    console.log(`❌ Store: خطأ - ${error.message}`);
  }

  // 4. إنشاء التوصيات
  console.log('\n💡 إنشاء التوصيات...');
  
  const pagesUsingStore = Object.keys(auditResults.pages)
    .filter(page => auditResults.pages[page].usesStore && !auditResults.pages[page].usesApiCalls);
  
  const pagesUsingApi = Object.keys(auditResults.pages)
    .filter(page => auditResults.pages[page].usesApiCalls);
  
  auditResults.recommendations.push(`✅ ${pagesUsingApi.length} صفحة تستخدم APIs`);
  
  if (pagesUsingStore.length > 0) {
    auditResults.recommendations.push(`❌ ${pagesUsingStore.length} صفحة تحتاج تحديث لاستخدام APIs: ${pagesUsingStore.join(', ')}`);
  }
  
  const totalApis = Object.keys(auditResults.apiUsage).length;
  auditResults.recommendations.push(`📊 إجمالي APIs: ${totalApis}`);
  
  if (auditResults.errors.length === 0) {
    auditResults.recommendations.push('✅ لا توجد أخطاء في الواجهة الأمامية');
  }

  // حفظ التقرير
  const reportPath = path.join(__dirname, 'frontend-audit-report.json');
  fs.writeFileSync(reportPath, JSON.stringify(auditResults, null, 2));
  
  console.log(`\n✅ تم حفظ تقرير الواجهة الأمامية في: ${reportPath}`);
  
  return auditResults;
}

// تشغيل الفحص
auditFrontendPages()
  .then(results => {
    console.log('\n🎯 ملخص فحص الواجهة الأمامية:');
    console.log(`📄 الصفحات: ${Object.keys(results.pages).length}`);
    console.log(`🌐 APIs: ${Object.keys(results.apiUsage).length}`);
    console.log(`❌ الأخطاء: ${results.errors.length}`);
    console.log(`⚠️ التحذيرات: ${results.warnings.length}`);
    console.log(`💡 التوصيات: ${results.recommendations.length}`);
    
    if (results.recommendations.length > 0) {
      console.log('\n📋 التوصيات:');
      results.recommendations.forEach(rec => console.log(`  ${rec}`));
    }
  })
  .catch(console.error);
