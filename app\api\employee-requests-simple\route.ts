import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

export async function GET(request: NextRequest) {
  try {
    const employeeRequests = await prisma.employeeRequest.findMany({
      orderBy: { id: 'desc' }
    });

    return NextResponse.json(employeeRequests);
  } catch (error) {
    console.error('Failed to fetch employee requests:', error);
    return NextResponse.json({ error: 'Failed to fetch employee requests' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const newRequest = await request.json();

    // Basic validation
    if (!newRequest.requestType || !newRequest.priority || !newRequest.notes) {
      return NextResponse.json(
        { error: 'Request type, priority, and notes are required' },
        { status: 400 }
      );
    }

    // Generate unique request number
    const requestCount = await prisma.employeeRequest.count();
    const requestNumber = `REQ-${String(requestCount + 1).padStart(4, '0')}`;

    const employeeRequest = await prisma.employeeRequest.create({
      data: {
        requestNumber,
        requestType: newRequest.requestType,
        priority: newRequest.priority,
        notes: newRequest.notes,
        employeeName: newRequest.employeeName || 'موظف غير محدد',
        employeeId: newRequest.employeeId || 1,
        relatedOrderType: newRequest.relatedOrderType || null,
        relatedOrderId: newRequest.relatedOrderId || null,
        relatedOrderDisplayId: newRequest.relatedOrderDisplayId || null,
        status: 'قيد المراجعة',
        requestDate: new Date().toISOString(),
        adminNotes: null
      }
    });

    return NextResponse.json(employeeRequest, { status: 201 });
  } catch (error) {
    console.error('Failed to create employee request:', error);
    return NextResponse.json({ error: 'Failed to create employee request' }, { status: 500 });
  }
}

export async function PUT(request: NextRequest) {
  try {
    const updatedRequest = await request.json();

    if (!updatedRequest.id) {
      return NextResponse.json(
        { error: 'Request ID is required' },
        { status: 400 }
      );
    }

    const employeeRequest = await prisma.employeeRequest.update({
      where: { id: updatedRequest.id },
      data: {
        ...(updatedRequest.status && { status: updatedRequest.status }),
        ...(updatedRequest.adminNotes !== undefined && { adminNotes: updatedRequest.adminNotes }),
        ...(updatedRequest.requestType && { requestType: updatedRequest.requestType }),
        ...(updatedRequest.priority && { priority: updatedRequest.priority }),
        ...(updatedRequest.notes && { notes: updatedRequest.notes }),
        ...(updatedRequest.employeeName && { employeeName: updatedRequest.employeeName })
      }
    });

    return NextResponse.json(employeeRequest);
  } catch (error) {
    console.error('Failed to update employee request:', error);
    return NextResponse.json({ error: 'Failed to update employee request' }, { status: 500 });
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { id } = await request.json();

    if (!id) {
      return NextResponse.json(
        { error: 'Request ID is required' },
        { status: 400 }
      );
    }

    await prisma.employeeRequest.delete({
      where: { id }
    });

    return NextResponse.json({ message: 'Employee request deleted successfully' });
  } catch (error) {
    console.error('Failed to delete employee request:', error);
    return NextResponse.json({ error: 'Failed to delete employee request' }, { status: 500 });
  }
}
