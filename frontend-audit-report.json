{"timestamp": "2025-08-01T00:25:55.708Z", "pages": {"Supply": {"exists": true, "lines": 3124, "usesStore": true, "usesApiCalls": true, "usesJsonParse": true, "usesLocalStorage": true, "status": "<PERSON><PERSON><PERSON><PERSON>"}, "Sales": {"exists": true, "lines": 2587, "usesStore": true, "usesApiCalls": true, "usesJsonParse": true, "usesLocalStorage": true, "status": "<PERSON><PERSON><PERSON><PERSON>"}, "Returns": {"exists": true, "lines": 2437, "usesStore": true, "usesApiCalls": true, "usesJsonParse": true, "usesLocalStorage": true, "status": "<PERSON><PERSON><PERSON><PERSON>"}, "Maintenance": {"exists": true, "lines": 4028, "usesStore": true, "usesApiCalls": true, "usesJsonParse": true, "usesLocalStorage": true, "status": "<PERSON><PERSON><PERSON><PERSON>"}, "Inventory": {"exists": true, "lines": 2215, "usesStore": true, "usesApiCalls": false, "usesJsonParse": false, "usesLocalStorage": false, "status": "يحتاج تحديث"}, "Track": {"exists": true, "lines": 788, "usesStore": true, "usesApiCalls": false, "usesJsonParse": false, "usesLocalStorage": false, "status": "يحتاج تحديث"}, "Stocktaking": {"exists": true, "lines": 2343, "usesStore": true, "usesApiCalls": false, "usesJsonParse": true, "usesLocalStorage": true, "status": "يحتاج تحديث"}, "Requests": {"exists": true, "lines": 574, "usesStore": true, "usesApiCalls": false, "usesJsonParse": false, "usesLocalStorage": false, "status": "يحتاج تحديث"}, "Messaging": {"exists": true, "lines": 1220, "usesStore": true, "usesApiCalls": true, "usesJsonParse": false, "usesLocalStorage": false, "status": "<PERSON><PERSON><PERSON><PERSON>"}, "Grading": {"exists": true, "lines": 2136, "usesStore": true, "usesApiCalls": false, "usesJsonParse": true, "usesLocalStorage": true, "status": "يحتاج تحديث"}, "Clients": {"exists": true, "lines": 402, "usesStore": false, "usesApiCalls": true, "usesJsonParse": false, "usesLocalStorage": false, "status": "<PERSON><PERSON><PERSON><PERSON>"}, "Warehouses": {"exists": true, "lines": 429, "usesStore": false, "usesApiCalls": true, "usesJsonParse": false, "usesLocalStorage": false, "status": "<PERSON><PERSON><PERSON><PERSON>"}}, "apiUsage": {"app\\api\\warehouses-simple\\route.ts": {"methods": {"GET": true, "POST": true, "PUT": true, "DELETE": true}, "usesPrisma": true, "hasAuth": false, "hasTransaction": false}, "app\\api\\warehouses\\route.ts": {"methods": {"GET": true, "POST": true, "PUT": true, "DELETE": true}, "usesPrisma": true, "hasAuth": true, "hasTransaction": true}, "app\\api\\warehouse-transfers\\route.ts": {"methods": {"GET": true, "POST": true, "PUT": true, "DELETE": true}, "usesPrisma": true, "hasAuth": false, "hasTransaction": false}, "app\\api\\users\\route.ts": {"methods": {"GET": true, "POST": true, "PUT": true, "DELETE": true}, "usesPrisma": true, "hasAuth": true, "hasTransaction": true}, "app\\api\\users\\reset-password\\route.ts": {"methods": {"GET": false, "POST": true, "PUT": false, "DELETE": false}, "usesPrisma": false, "hasAuth": true, "hasTransaction": true}, "app\\api\\upload\\route.ts": {"methods": {"GET": true, "POST": true, "PUT": false, "DELETE": true}, "usesPrisma": false, "hasAuth": true, "hasTransaction": true}, "app\\api\\supply\\route.ts": {"methods": {"GET": true, "POST": true, "PUT": true, "DELETE": true}, "usesPrisma": true, "hasAuth": true, "hasTransaction": true}, "app\\api\\suppliers-simple\\route.ts": {"methods": {"GET": true, "POST": true, "PUT": true, "DELETE": true}, "usesPrisma": true, "hasAuth": false, "hasTransaction": false}, "app\\api\\suppliers\\route.ts": {"methods": {"GET": true, "POST": true, "PUT": true, "DELETE": true}, "usesPrisma": true, "hasAuth": true, "hasTransaction": true}, "app\\api\\stocktakes\\route.ts": {"methods": {"GET": true, "POST": true, "PUT": true, "DELETE": true}, "usesPrisma": true, "hasAuth": false, "hasTransaction": false}, "app\\api\\settings\\route.ts": {"methods": {"GET": true, "POST": false, "PUT": true, "DELETE": false}, "usesPrisma": true, "hasAuth": true, "hasTransaction": true}, "app\\api\\sales\\route.ts": {"methods": {"GET": true, "POST": true, "PUT": true, "DELETE": true}, "usesPrisma": true, "hasAuth": true, "hasTransaction": true}, "app\\api\\returns\\route.ts": {"methods": {"GET": true, "POST": true, "PUT": true, "DELETE": true}, "usesPrisma": true, "hasAuth": true, "hasTransaction": true}, "app\\api\\maintenance-receipts\\route.ts": {"methods": {"GET": true, "POST": true, "PUT": true, "DELETE": true}, "usesPrisma": true, "hasAuth": true, "hasTransaction": true}, "app\\api\\maintenance-orders\\route.ts": {"methods": {"GET": true, "POST": true, "PUT": true, "DELETE": true}, "usesPrisma": true, "hasAuth": true, "hasTransaction": true}, "app\\api\\maintenance-orders\\[id]\\route.ts": {"methods": {"GET": false, "POST": false, "PUT": false, "DELETE": true}, "usesPrisma": false, "hasAuth": true, "hasTransaction": true}, "app\\api\\maintenance-logs\\route.ts": {"methods": {"GET": true, "POST": true, "PUT": true, "DELETE": true}, "usesPrisma": true, "hasAuth": true, "hasTransaction": true}, "app\\api\\internal-messages\\route.ts": {"methods": {"GET": true, "POST": true, "PUT": true, "DELETE": false}, "usesPrisma": true, "hasAuth": true, "hasTransaction": true}, "app\\api\\evaluations\\route.ts": {"methods": {"GET": true, "POST": true, "PUT": true, "DELETE": true}, "usesPrisma": true, "hasAuth": true, "hasTransaction": true}, "app\\api\\evaluations\\cleanup\\route.ts": {"methods": {"GET": false, "POST": true, "PUT": false, "DELETE": true}, "usesPrisma": true, "hasAuth": true, "hasTransaction": true}, "app\\api\\employee-requests\\route.ts": {"methods": {"GET": true, "POST": true, "PUT": true, "DELETE": false}, "usesPrisma": true, "hasAuth": true, "hasTransaction": true}, "app\\api\\devices\\route.ts": {"methods": {"GET": true, "POST": true, "PUT": true, "DELETE": true}, "usesPrisma": true, "hasAuth": true, "hasTransaction": true}, "app\\api\\device-models\\route.ts": {"methods": {"GET": true, "POST": true, "PUT": false, "DELETE": false}, "usesPrisma": true, "hasAuth": true, "hasTransaction": false}, "app\\api\\delivery-orders\\route.ts": {"methods": {"GET": true, "POST": true, "PUT": true, "DELETE": true}, "usesPrisma": true, "hasAuth": true, "hasTransaction": true}, "app\\api\\delivery-orders\\[id]\\route.ts": {"methods": {"GET": false, "POST": false, "PUT": false, "DELETE": true}, "usesPrisma": false, "hasAuth": true, "hasTransaction": true}, "app\\api\\database\\switch\\route.ts": {"methods": {"GET": true, "POST": true, "PUT": false, "DELETE": false}, "usesPrisma": true, "hasAuth": true, "hasTransaction": true}, "app\\api\\database\\restore\\route.ts": {"methods": {"GET": true, "POST": true, "PUT": false, "DELETE": false}, "usesPrisma": true, "hasAuth": true, "hasTransaction": true}, "app\\api\\database\\list\\route.ts": {"methods": {"GET": true, "POST": false, "PUT": false, "DELETE": false}, "usesPrisma": false, "hasAuth": false, "hasTransaction": false}, "app\\api\\database\\delete\\route.ts": {"methods": {"GET": false, "POST": false, "PUT": false, "DELETE": true}, "usesPrisma": false, "hasAuth": true, "hasTransaction": true}, "app\\api\\database\\create\\route.ts": {"methods": {"GET": true, "POST": true, "PUT": false, "DELETE": false}, "usesPrisma": true, "hasAuth": true, "hasTransaction": true}, "app\\api\\database\\connections\\route.ts": {"methods": {"GET": true, "POST": true, "PUT": true, "DELETE": true}, "usesPrisma": true, "hasAuth": true, "hasTransaction": true}, "app\\api\\database\\backup\\route.ts": {"methods": {"GET": true, "POST": true, "PUT": false, "DELETE": true}, "usesPrisma": true, "hasAuth": true, "hasTransaction": true}, "app\\api\\clients-simple\\route.ts": {"methods": {"GET": true, "POST": true, "PUT": true, "DELETE": true}, "usesPrisma": true, "hasAuth": false, "hasTransaction": false}, "app\\api\\clients\\route.ts": {"methods": {"GET": true, "POST": true, "PUT": true, "DELETE": true}, "usesPrisma": true, "hasAuth": true, "hasTransaction": true}, "app\\api\\audit-logs\\route.ts": {"methods": {"GET": true, "POST": true, "PUT": false, "DELETE": false}, "usesPrisma": true, "hasAuth": true, "hasTransaction": true}, "app\\api\\attachments\\route.ts": {"methods": {"GET": true, "POST": true, "PUT": false, "DELETE": false}, "usesPrisma": false, "hasAuth": true, "hasTransaction": true}, "app\\api\\attachments\\delete\\route.ts": {"methods": {"GET": false, "POST": false, "PUT": false, "DELETE": true}, "usesPrisma": false, "hasAuth": true, "hasTransaction": true}}, "storeUsage": {"exists": true, "exportedFunctions": 354, "apiCalls": 0, "stillUsesLocalStorage": true, "stillUsesJsonData": false}, "errors": [], "warnings": ["Supply: يستخدم JSON.parse (قد يحتاج تحديث)", "Sales: يستخدم JSON.parse (قد يحتاج تحديث)", "Returns: يستخدم JSON.parse (قد يحتاج تحديث)", "Maintenance: يستخدم JSON.parse (قد يحتاج تحديث)", "Inventory: لا يزال يستخدم Store بدلاً من APIs", "Track: لا يزال يستخدم Store بدلاً من APIs", "Stocktaking: لا يزال يستخدم Store بدلاً من APIs", "Stocktaking: يستخدم JSON.parse (قد يحتاج تحديث)", "Requests: لا يزال يستخدم Store بدلاً من APIs", "Grading: لا يزال يستخدم Store بدلاً من APIs", "Grading: يستخدم JSON.parse (قد يحتاج تحديث)", "Store: لا يزال يستخدم localStorage"], "recommendations": ["✅ 7 صفحة تستخدم APIs", "❌ 5 صفحة تحتاج تحديث لاستخدام APIs: Inventory, Track, Stocktaking, Requests, Grading", "📊 إجمالي APIs: 37", "✅ لا توجد أخطاء في الواجهة الأمامية"]}