import { PrismaClient } from '@prisma/client'

const globalForPrisma = globalThis as unknown as {
  prisma: PrismaClient | undefined
}

export const prisma = globalForPrisma.prisma ?? new PrismaClient({
  // إعدادات محسنة للتعامل مع العمليات الكبيرة
  datasources: {
    db: {
      url: process.env.DATABASE_URL
    }
  },
  // تسجيل الاستعلامات البطيئة للمراقبة
  log: process.env.NODE_ENV === 'development' ? [
    {
      emit: 'event',
      level: 'query',
    },
    {
      emit: 'stdout',
      level: 'error',
    },
    {
      emit: 'stdout',
      level: 'info',
    },
    {
      emit: 'stdout',
      level: 'warn',
    },
  ] : ['error'],
})

// مراقبة الاستعلامات البطيئة في بيئة التطوير
if (process.env.NODE_ENV === 'development') {
  prisma.$on('query', (e) => {
    if (e.duration > 1000) { // أكثر من ثانية واحدة
      console.log('Slow query detected:', {
        query: e.query,
        duration: `${e.duration}ms`,
        params: e.params,
      });
    }
  });
}

if (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma
