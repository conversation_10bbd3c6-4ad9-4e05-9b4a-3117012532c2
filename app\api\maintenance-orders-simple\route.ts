import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

export async function GET(request: NextRequest) {
  try {
    const maintenanceOrders = await prisma.maintenanceOrder.findMany({
      include: {
        logs: true
      },
      orderBy: { id: 'desc' }
    });

    return NextResponse.json(maintenanceOrders);
  } catch (error) {
    console.error('Failed to fetch maintenance orders:', error);
    return NextResponse.json({ error: 'Failed to fetch maintenance orders' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const newMaintenanceOrder = await request.json();

    // Basic validation
    if (!newMaintenanceOrder.deviceId || !newMaintenanceOrder.issueDescription) {
      return NextResponse.json(
        { error: 'Device ID and issue description are required' },
        { status: 400 }
      );
    }

    const maintenanceOrder = await prisma.maintenanceOrder.create({
      data: {
        orderId: newMaintenanceOrder.orderId,
        deviceId: newMaintenanceOrder.deviceId,
        issueDescription: newMaintenanceOrder.issueDescription,
        priority: newMaintenanceOrder.priority || 'متوسط',
        status: newMaintenanceOrder.status || 'قيد الانتظار',
        assignedTo: newMaintenanceOrder.assignedTo || null,
        estimatedCost: newMaintenanceOrder.estimatedCost || 0,
        actualCost: newMaintenanceOrder.actualCost || 0,
        notes: newMaintenanceOrder.notes || null,
        startDate: newMaintenanceOrder.startDate ? new Date(newMaintenanceOrder.startDate) : null,
        completionDate: newMaintenanceOrder.completionDate ? new Date(newMaintenanceOrder.completionDate) : null
      },
      include: {
        logs: true
      }
    });

    return NextResponse.json(maintenanceOrder, { status: 201 });
  } catch (error) {
    console.error('Failed to create maintenance order:', error);
    return NextResponse.json({ error: 'Failed to create maintenance order' }, { status: 500 });
  }
}

export async function PUT(request: NextRequest) {
  try {
    const updatedMaintenanceOrder = await request.json();

    if (!updatedMaintenanceOrder.id) {
      return NextResponse.json(
        { error: 'Maintenance order ID is required' },
        { status: 400 }
      );
    }

    const maintenanceOrder = await prisma.maintenanceOrder.update({
      where: { id: updatedMaintenanceOrder.id },
      data: {
        ...(updatedMaintenanceOrder.deviceId && { deviceId: updatedMaintenanceOrder.deviceId }),
        ...(updatedMaintenanceOrder.issueDescription && { issueDescription: updatedMaintenanceOrder.issueDescription }),
        ...(updatedMaintenanceOrder.priority && { priority: updatedMaintenanceOrder.priority }),
        ...(updatedMaintenanceOrder.status && { status: updatedMaintenanceOrder.status }),
        ...(updatedMaintenanceOrder.assignedTo !== undefined && { assignedTo: updatedMaintenanceOrder.assignedTo }),
        ...(updatedMaintenanceOrder.estimatedCost !== undefined && { estimatedCost: updatedMaintenanceOrder.estimatedCost }),
        ...(updatedMaintenanceOrder.actualCost !== undefined && { actualCost: updatedMaintenanceOrder.actualCost }),
        ...(updatedMaintenanceOrder.notes !== undefined && { notes: updatedMaintenanceOrder.notes }),
        ...(updatedMaintenanceOrder.startDate && { startDate: new Date(updatedMaintenanceOrder.startDate) }),
        ...(updatedMaintenanceOrder.completionDate && { completionDate: new Date(updatedMaintenanceOrder.completionDate) })
      },
      include: {
        logs: true
      }
    });

    return NextResponse.json(maintenanceOrder);
  } catch (error) {
    console.error('Failed to update maintenance order:', error);
    return NextResponse.json({ error: 'Failed to update maintenance order' }, { status: 500 });
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { id } = await request.json();

    if (!id) {
      return NextResponse.json(
        { error: 'Maintenance order ID is required' },
        { status: 400 }
      );
    }

    // Delete maintenance logs first, then the order
    await prisma.maintenanceLog.deleteMany({
      where: { maintenanceOrderId: id }
    });

    await prisma.maintenanceOrder.delete({
      where: { id }
    });

    return NextResponse.json({ message: 'Maintenance order deleted successfully' });
  } catch (error) {
    console.error('Failed to delete maintenance order:', error);
    return NextResponse.json({ error: 'Failed to delete maintenance order' }, { status: 500 });
  }
}
