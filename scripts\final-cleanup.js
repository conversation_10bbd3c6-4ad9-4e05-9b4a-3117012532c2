const fs = require('fs');
const path = require('path');

function finalCleanup() {
  console.log('🧹 تنظيف نهائي لإزالة معالجة JSON المتبقية...\n');

  // ملفات API التي تحتاج تنظيف
  const apiFiles = [
    'app/api/returns/route.ts',
    'app/api/delivery-orders/route.ts',
    'app/api/evaluations/route.ts',
    'app/api/maintenance-receipts/route.ts'
  ];

  apiFiles.forEach(filePath => {
    if (!fs.existsSync(filePath)) {
      console.log(`⚠️ ${filePath}: الملف غير موجود`);
      return;
    }

    try {
      let content = fs.readFileSync(filePath, 'utf8');
      let hasChanges = false;

      // إزالة معالجة JSON من النتائج
      const oldProcessing = /\/\/ تحويل حقول JSON من strings إلى objects[\s\S]*?return NextResponse\.json\(processed[^)]+\);/g;
      if (oldProcessing.test(content)) {
        content = content.replace(oldProcessing, 'return NextResponse.json(orders);');
        hasChanges = true;
      }

      // إزالة معالجة JSON من map functions
      const mapProcessing = /\.map\(\([^)]+\)\s*=>\s*\(\{[\s\S]*?items:\s*[^.]*\.items\s*\?\s*\([\s\S]*?\)\s*:\s*\[\][\s\S]*?\}\)\)/g;
      if (mapProcessing.test(content)) {
        content = content.replace(mapProcessing, '');
        hasChanges = true;
      }

      // إزالة متغيرات processed
      const processedVars = /const processed[^=]*=[\s\S]*?;/g;
      if (processedVars.test(content)) {
        content = content.replace(processedVars, '');
        hasChanges = true;
      }

      // تصحيح return statements
      content = content.replace(/return NextResponse\.json\(orders\);/g, 'return NextResponse.json(orders || receipts || evaluations || deliveryOrders || maintenanceReceipts);');
      content = content.replace(/return NextResponse\.json\(orders \|\| receipts \|\| evaluations \|\| deliveryOrders \|\| maintenanceReceipts\);/g, 'return NextResponse.json(orders || receipts || evaluations || deliveryOrders || maintenanceReceipts || returns);');

      // تصحيح أسماء المتغيرات
      if (filePath.includes('returns')) {
        content = content.replace(/return NextResponse\.json\([^)]+\);/g, 'return NextResponse.json(returns);');
      } else if (filePath.includes('delivery')) {
        content = content.replace(/return NextResponse\.json\([^)]+\);/g, 'return NextResponse.json(deliveryOrders);');
      } else if (filePath.includes('evaluations')) {
        content = content.replace(/return NextResponse\.json\([^)]+\);/g, 'return NextResponse.json(evaluations);');
      } else if (filePath.includes('maintenance-receipts')) {
        content = content.replace(/return NextResponse\.json\([^)]+\);/g, 'return NextResponse.json(maintenanceReceipts);');
      }

      if (hasChanges) {
        fs.writeFileSync(filePath, content);
        console.log(`✅ تم تنظيف: ${filePath}`);
      } else {
        console.log(`✅ ${filePath}: نظيف بالفعل`);
      }

    } catch (error) {
      console.log(`❌ خطأ في تنظيف ${filePath}:`, error.message);
    }
  });

  // تنظيف ملفات الواجهة الأمامية
  console.log('\n📱 تنظيف ملفات الواجهة الأمامية...');

  const frontendFiles = [
    'app/(main)/supply/page.tsx',
    'app/(main)/sales/page.tsx',
    'app/(main)/returns/page.tsx'
  ];

  frontendFiles.forEach(filePath => {
    if (!fs.existsSync(filePath)) {
      console.log(`⚠️ ${filePath}: الملف غير موجود`);
      return;
    }

    try {
      let content = fs.readFileSync(filePath, 'utf8');
      let hasChanges = false;

      // تبسيط ensureItemsArray إذا كانت معقدة
      const complexEnsure = /const ensureItemsArray = \([^)]+\)[^{]*\{[\s\S]*?if \(typeof items === 'string'\)[\s\S]*?\}[\s\S]*?return \[\];[\s\S]*?\};/g;
      if (complexEnsure.test(content)) {
        content = content.replace(complexEnsure, `const ensureItemsArray = (items: any) => {
  if (Array.isArray(items)) {
    return items;
  }
  return [];
};`);
        hasChanges = true;
      }

      // إزالة معالجة JSON المعقدة
      const complexJson = /typeof\s+[^.]*\.items\s*===\s*['"]string['"][\s\S]*?JSON\.parse\([^)]+\)[\s\S]*?:/g;
      if (complexJson.test(content)) {
        content = content.replace(complexJson, 'Array.isArray(items) ? items :');
        hasChanges = true;
      }

      if (hasChanges) {
        fs.writeFileSync(filePath, content);
        console.log(`✅ تم تنظيف: ${filePath}`);
      } else {
        console.log(`✅ ${filePath}: نظيف بالفعل`);
      }

    } catch (error) {
      console.log(`❌ خطأ في تنظيف ${filePath}:`, error.message);
    }
  });

  console.log('\n🎉 انتهى التنظيف النهائي!');
}

if (require.main === module) {
  finalCleanup();
}

module.exports = { finalCleanup };
