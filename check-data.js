const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkData() {
  try {
    console.log('🔍 فحص البيانات في قاعدة البيانات...\n');

    // فحص الأجهزة
    const devices = await prisma.device.findMany();
    console.log(`📱 عدد الأجهزة: ${devices.length}`);
    if (devices.length > 0) {
      console.log('أول 3 أجهزة:');
      devices.slice(0, 3).forEach(device => {
        console.log(`  - ${device.id}: ${device.model} (${device.status})`);
      });
    }

    // فحص أوامر التوريد
    const supplyOrders = await prisma.supplyOrder.findMany({
      include: { items: true }
    });
    console.log(`\n📦 عدد أوامر التوريد: ${supplyOrders.length}`);
    if (supplyOrders.length > 0) {
      console.log('أول أمر توريد:');
      const firstOrder = supplyOrders[0];
      console.log(`  - رقم الأمر: ${firstOrder.orderId}`);
      console.log(`  - عدد العناصر: ${firstOrder.items.length}`);
      if (firstOrder.items.length > 0) {
        console.log('  - أول عنصر:', firstOrder.items[0]);
      }
    }

    // فحص موديلات الأجهزة
    const deviceModels = await prisma.deviceModel.findMany();
    console.log(`\n📋 عدد موديلات الأجهزة: ${deviceModels.length}`);
    if (deviceModels.length > 0) {
      console.log('أول 3 موديلات:');
      deviceModels.slice(0, 3).forEach(model => {
        console.log(`  - ${model.name} (المصنع: ${model.manufacturerId})`);
      });
    }

    // فحص المخازن
    const warehouses = await prisma.warehouse.findMany();
    console.log(`\n🏢 عدد المخازن: ${warehouses.length}`);
    if (warehouses.length > 0) {
      warehouses.forEach(warehouse => {
        console.log(`  - ${warehouse.name}: ${warehouse.location}`);
      });
    }

  } catch (error) {
    console.error('خطأ في فحص البيانات:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkData();
