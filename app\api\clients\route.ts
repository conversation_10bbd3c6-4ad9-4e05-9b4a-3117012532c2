import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { requireAuth, getUserFromRequest } from '@/lib/auth';
import { executeInTransaction, createAuditLogInTransaction, checkRelationsBeforeDelete } from '@/lib/transaction-utils';

export async function GET(request: NextRequest) {
  try {
    // التحقق من التفويض
    const authResult = await requireAuth(request, 'user');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    // استرجاع كل العملاء من قاعدة البيانات، مرتبة تنازلياً
    const clients = await prisma.client.findMany({
      orderBy: { id: 'desc' }
    });
    return NextResponse.json(clients);
  } catch (error) {
    console.error('Failed to fetch clients:', error);
    return NextResponse.json({ error: 'Failed to fetch clients' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    // التحقق من التفويض
    const authResult = await requireAuth(request, 'user');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const newClient = await request.json();

    // التحقق من البيانات المطلوبة
    if (!newClient.name) {
      return NextResponse.json(
        { error: 'Name is required' },
        { status: 400 }
      );
    }

    // تنفيذ العملية داخل معاملة
    const result = await executeInTransaction(async (tx) => {
      // التحقق من عدم وجود بريد إلكتروني مكرر (إذا تم توفيره)
      if (newClient.email && newClient.email.trim()) {
        const existingClient = await tx.client.findFirst({
          where: { 
            email: newClient.email
          }
        });

        if (existingClient) {
          throw new Error('Email already exists');
        }
      }

      // إنشاء العميل في قاعدة البيانات
      const client = await tx.client.create({
        data: {
          name: newClient.name,
          phone: newClient.phone || '',
          email: newClient.email || '',
        }
      });

      // إنشاء audit log
      await createAuditLogInTransaction(tx, {
        userId: authResult.user!.id,
        username: authResult.user!.username,
        operation: 'CREATE',
        details: `Created client: ${client.name}`,
        tableName: 'client',
        recordId: client.id
      });

      return client;
    });

    return NextResponse.json(result, { status: 201 });
  } catch (error) {
    console.error('Failed to create client:', error);

    if (error instanceof Error && error.message === 'Email already exists') {
      return NextResponse.json(
        { error: 'Email already exists' },
        { status: 409 }
      );
    }

    return NextResponse.json({ error: 'Failed to create client' }, { status: 500 });
  }
}

export async function PUT(request: NextRequest) {
  try {
    // التحقق من التفويض
    const authResult = await requireAuth(request, 'user');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const updatedClient = await request.json();

    // تنفيذ العملية داخل معاملة
    const result = await executeInTransaction(async (tx) => {
      // التحقق من وجود العميل
      const existingClient = await tx.client.findUnique({
        where: { id: updatedClient.id }
      });

      if (!existingClient) {
        throw new Error('Client not found');
      }

      // التحقق من عدم وجود بريد إلكتروني مكرر (باستثناء العميل الحالي)
      if (updatedClient.email && updatedClient.email.trim() && updatedClient.email !== existingClient.email) {
        const emailExists = await tx.client.findUnique({
          where: { email: updatedClient.email }
        });

        if (emailExists) {
          throw new Error('Email already exists');
        }
      }

      // تحديث العميل
      const client = await tx.client.update({
        where: { id: updatedClient.id },
        data: {
          name: updatedClient.name,
          phone: updatedClient.phone || '',
          email: updatedClient.email || '',
        }
      });

      // إنشاء audit log
      await createAuditLogInTransaction(tx, {
        userId: authResult.user!.id,
        username: authResult.user!.username,
        operation: 'UPDATE',
        details: `Updated client: ${client.name}`,
        tableName: 'client',
        recordId: client.id
      });

      return client;
    });

    return NextResponse.json(result);
  } catch (error) {
    console.error('Failed to update client:', error);

    if (error instanceof Error) {
      if (error.message === 'Client not found') {
        return NextResponse.json({ error: 'Client not found' }, { status: 404 });
      }
      if (error.message === 'Email already exists') {
        return NextResponse.json({ error: 'Email already exists' }, { status: 409 });
      }
    }

    return NextResponse.json({ error: 'Failed to update client' }, { status: 500 });
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // التحقق من التفويض - يتطلب صلاحيات إدارية للحذف
    const authResult = await requireAuth(request, 'manager');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const { id } = await request.json();

    // تنفيذ العملية داخل معاملة
    const result = await executeInTransaction(async (tx) => {
      // التحقق من وجود العميل
      const existingClient = await tx.client.findUnique({
        where: { id }
      });

      if (!existingClient) {
        throw new Error('Client not found');
      }

      // فحص العلاقات قبل الحذف
      const relationCheck = await checkRelationsBeforeDelete(tx, 'client', id);

      // فحص إضافي للمبيعات والمرتجعات
      const relatedSales = await tx.sale.findMany({
        where: { clientName: existingClient.name }
      });

      const relatedReturns = await tx.return.findMany({
        where: { clientName: existingClient.name }
      });

      const relatedOperations: string[] = [...relationCheck.relations];
      if (relatedSales.length > 0) {
        relatedOperations.push(`${relatedSales.length} فاتورة مبيعات`);
      }
      if (relatedReturns.length > 0) {
        relatedOperations.push(`${relatedReturns.length} أمر مرتجع`);
      }

      if (relatedOperations.length > 0) {
        throw new Error(`Cannot delete client: ${relatedOperations.join(', ')}`);
      }

      // حذف العميل
      await tx.client.delete({
        where: { id }
      });

      // إنشاء audit log
      await createAuditLogInTransaction(tx, {
        userId: authResult.user!.id,
        username: authResult.user!.username,
        operation: 'DELETE',
        details: `Deleted client: ${existingClient.name}`,
        tableName: 'client',
        recordId: id
      });

      return { message: 'Client deleted successfully' };
    });

    return NextResponse.json(result);
  } catch (error) {
    console.error('Failed to delete client:', error);

    if (error instanceof Error) {
      if (error.message === 'Client not found') {
        return NextResponse.json({ error: 'Client not found' }, { status: 404 });
      }
      if (error.message.startsWith('Cannot delete client:')) {
        const relatedOperations = error.message.replace('Cannot delete client: ', '').split(', ');
        return NextResponse.json({
          error: 'Cannot delete client',
          reason: 'يوجد عمليات مرتبطة بهذا العميل',
          relatedOperations
        }, { status: 409 });
      }
    }

    return NextResponse.json({ error: 'Failed to delete client' }, { status: 500 });
  }
}
