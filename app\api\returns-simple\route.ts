import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

export async function GET(request: NextRequest) {
  try {
    const returns = await prisma.return.findMany({
      include: {
        items: true
      },
      orderBy: { id: 'desc' }
    });

    return NextResponse.json(returns);
  } catch (error) {
    console.error('Failed to fetch returns:', error);
    return NextResponse.json({ error: 'Failed to fetch returns' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const newReturn = await request.json();

    // Basic validation
    if (!newReturn.clientId || !newReturn.items || newReturn.items.length === 0) {
      return NextResponse.json(
        { error: 'Client ID and items are required' },
        { status: 400 }
      );
    }

    const returnOrder = await prisma.return.create({
      data: {
        orderId: newReturn.orderId,
        clientId: newReturn.clientId,
        warehouseId: newReturn.warehouseId || null,
        totalAmount: newReturn.totalAmount || 0,
        reason: newReturn.reason || null,
        notes: newReturn.notes || null,
        returnDate: new Date(newReturn.returnDate || Date.now()),
        items: {
          create: newReturn.items.map((item: any) => ({
            deviceId: item.deviceId,
            returnPrice: item.returnPrice || 0,
            reason: item.reason || null,
            notes: item.notes || null
          }))
        }
      },
      include: {
        items: true
      }
    });

    return NextResponse.json(returnOrder, { status: 201 });
  } catch (error) {
    console.error('Failed to create return:', error);
    return NextResponse.json({ error: 'Failed to create return' }, { status: 500 });
  }
}

export async function PUT(request: NextRequest) {
  try {
    const updatedReturn = await request.json();

    if (!updatedReturn.id) {
      return NextResponse.json(
        { error: 'Return ID is required' },
        { status: 400 }
      );
    }

    const returnOrder = await prisma.return.update({
      where: { id: updatedReturn.id },
      data: {
        ...(updatedReturn.clientId && { clientId: updatedReturn.clientId }),
        ...(updatedReturn.warehouseId !== undefined && { warehouseId: updatedReturn.warehouseId }),
        ...(updatedReturn.totalAmount !== undefined && { totalAmount: updatedReturn.totalAmount }),
        ...(updatedReturn.reason !== undefined && { reason: updatedReturn.reason }),
        ...(updatedReturn.notes !== undefined && { notes: updatedReturn.notes }),
        ...(updatedReturn.returnDate && { returnDate: new Date(updatedReturn.returnDate) })
      },
      include: {
        items: true
      }
    });

    return NextResponse.json(returnOrder);
  } catch (error) {
    console.error('Failed to update return:', error);
    return NextResponse.json({ error: 'Failed to update return' }, { status: 500 });
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { id } = await request.json();

    if (!id) {
      return NextResponse.json(
        { error: 'Return ID is required' },
        { status: 400 }
      );
    }

    // Delete return items first, then the return
    await prisma.returnItem.deleteMany({
      where: { returnId: id }
    });

    await prisma.return.delete({
      where: { id }
    });

    return NextResponse.json({ message: 'Return deleted successfully' });
  } catch (error) {
    console.error('Failed to delete return:', error);
    return NextResponse.json({ error: 'Failed to delete return' }, { status: 500 });
  }
}
