import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { requireAuth } from '@/lib/auth';
import { executeInTransaction, createAuditLogInTransaction } from '@/lib/transaction-utils';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

export async function DELETE(request: NextRequest) {
  try {
    // التحقق من التفويض
    const authResult = await requireAuth(request, 'admin');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const { connectionId, databaseName } = await request.json();

    if (!connectionId || !databaseName) {
      return NextResponse.json(
        { error: 'Connection ID and database name are required' },
        { status: 400 }
      );
    }

    // التحقق من صحة اسم قاعدة البيانات (منع SQL injection)
    if (!/^[a-zA-Z0-9_]+$/.test(databaseName)) {
      return NextResponse.json(
        { error: 'Invalid database name' },
        { status: 400 }
      );
    }

    // التحقق من أنه لا يتم حذف قواعد البيانات المهمة
    const protectedDatabases = ['postgres', 'template0', 'template1'];
    if (protectedDatabases.includes(databaseName.toLowerCase()) {
      return NextResponse.json(
        { error: 'Cannot delete system database' },
        { status: 403 }
      );
    }

    // تنفيذ العملية داخل معاملة
    const result = await executeInTransaction(async (tx) => {
      // الحصول على معلومات الاتصال
      const connection = await tx.databaseConnection.findUnique({
        where: { id: connectionId }
      });

      if (!connection) {
        throw new Error('Connection not found');
      }

      // البحث عن قاعدة البيانات في السجلات المحلية
      const database = await tx.database.findFirst({
        where: {
          name: databaseName,
          connectionId: connectionId
        }
      });

      // فك تشفير كلمة المرور
      const password = connection.password;

      // تنفيذ أمر حذف قاعدة البيانات
      const dropCommand = `dropdb -h ${connection.host} -p ${connection.port} -U ${connection.username} ${databaseName}`;

      // تعيين متغير البيئة لكلمة المرور
      const env = { ...process.env, PGPASSWORD: password };

      await execAsync(dropCommand, { env });

      // حذف قاعدة البيانات من السجلات المحلية إذا كانت موجودة
      if (database) {
        await tx.database.delete({
          where: { id: database.id }
        });
      }

      // إنشاء audit log
      await createAuditLogInTransaction(tx, {
        userId: authResult.user!.id,
        username: authResult.user!.username,
        operation: 'DELETE',
        details: `Deleted database: ${databaseName} from connection: ${connection.name}`,
        tableName: 'database',
        recordId: database?.id.toString() || 'unknown'
      });

      return {
        message: 'Database deleted successfully',
        database: databaseName
      };
    });

    return NextResponse.json(result);
  } catch (error) {
    console.error('Delete database error:', error);

    // التحقق من نوع الخطأ لإعطاء رسالة واضحة
    if (error instanceof Error) {
      if (error.message.includes('Connection not found')) {
        return NextResponse.json({ error: 'Connection not found' }, { status: 404 });
      }
      if (error.message.includes('does not exist')) {
        return NextResponse.json(
          { error: 'Database does not exist' },
          { status: 404 }
        );
      }
      if (error.message.includes('being accessed by other users')) {
        return NextResponse.json(
          { error: 'Database is being used by other users' },
          { status: 409 }
        );
      }
      if (error.message.includes('permission denied')) {
        return NextResponse.json(
          { error: 'Permission denied to delete database' },
          { status: 403 }
        );
      }
    }

    return NextResponse.json(
      { error: 'Failed to delete database' },
      { status: 500 }
    );
  }