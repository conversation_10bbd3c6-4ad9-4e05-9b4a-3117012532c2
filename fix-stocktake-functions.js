// Script لتحديث وظائف الجرد
const fs = require('fs');

const filePath = 'context/store.tsx';
let content = fs.readFileSync(filePath, 'utf8');

console.log('🔄 تحديث وظائف الجرد...');

// 1. تحديث addStocktake
console.log('📝 تحديث addStocktake...');

const addStocktakeStart = 'const addStocktake = (';
const addStocktakeIndex = content.indexOf(addStocktakeStart);

if (addStocktakeIndex !== -1) {
  const nextFunctionStart = content.indexOf('const updateStocktake', addStocktakeIndex);
  const addStocktakeEndIndex = content.lastIndexOf('};', nextFunctionStart);
  
  const newAddStocktakeFunction = `const addStocktake = async (
    stocktakeData: Omit<
      StocktakeV1,
      "id" | "operationNumber" | "createdAt" | "lastModifiedAt"
    >,
  ) => {
    try {
      // إعداد البيانات الأساسية
      const operationNumber = \`ST-\${Date.now()}\`;
      const now = new Date().toISOString();

      // حالياً نحفظ محلياً حتى يتم إنشاء API
      // TODO: إنشاء /api/stocktakes endpoint
      
      const newId = Math.max(0, ...stocktakes.map((s) => s.id)) + 1;
      const newStocktake: StocktakeV1 = {
        ...stocktakeData,
        id: newId,
        operationNumber,
        createdAt: now,
        lastModifiedAt: now,
        items: [], // Initialize items array
        discrepancies: [], // Initialize discrepancies array
        totalExpected: 0,
        totalScanned: 0,
        totalMatching: 0,
        totalDiscrepancies: 0,
        status: 'في التقدم' as StocktakeStatus,
      };

      setStocktakes((prev) => [newStocktake, ...prev]);

      addActivity({
        type: "supply",
        description: \`تم إنشاء عملية جرد جديدة: \${operationNumber}\`,
      });
      
      return newStocktake;
    } catch (error) {
      console.error('Failed to add stocktake:', error);
      addActivity({
        type: "supply",
        description: \`⚠️ فشل في إنشاء عملية جرد: \${error instanceof Error ? error.message : String(error)}\`,
      });
      throw error;
    }
  };`;

  const beforeFunction = content.substring(0, addStocktakeIndex);
  const afterFunction = content.substring(addStocktakeEndIndex + 2);
  content = beforeFunction + newAddStocktakeFunction + '\n\n  ' + afterFunction;
  console.log('✅ تم تحديث addStocktake');
}

// 2. تحديث updateStocktake
console.log('📝 تحديث updateStocktake...');

const updateStocktakeStart = 'const updateStocktake = (stocktake: StocktakeV1) => {';
const updateStocktakeIndex = content.indexOf(updateStocktakeStart);

if (updateStocktakeIndex !== -1) {
  const nextFunctionStart = content.indexOf('const addStocktakeItem', updateStocktakeIndex);
  const updateStocktakeEndIndex = content.lastIndexOf('};', nextFunctionStart);
  
  const newUpdateStocktakeFunction = `const updateStocktake = async (stocktake: StocktakeV1) => {
    try {
      // تحديث الوقت المعدل
      const updatedStocktake = {
        ...stocktake,
        lastModifiedAt: new Date().toISOString(),
      };

      // حالياً نحفظ محلياً حتى يتم إنشاء API
      // TODO: إرسال إلى /api/stocktakes

      setStocktakes((prev) =>
        prev.map((s) => (s.id === updatedStocktake.id ? updatedStocktake : s)),
      );

      addActivity({
        type: "supply",
        description: \`تم تحديث عملية الجرد: \${updatedStocktake.operationNumber}\`,
      });
      
      return updatedStocktake;
    } catch (error) {
      console.error('Failed to update stocktake:', error);
      addActivity({
        type: "supply",
        description: \`⚠️ فشل في تحديث عملية الجرد: \${error instanceof Error ? error.message : String(error)}\`,
      });
      throw error;
    }
  };`;

  const beforeFunction = content.substring(0, updateStocktakeIndex);
  const afterFunction = content.substring(updateStocktakeEndIndex + 2);
  content = beforeFunction + newUpdateStocktakeFunction + '\n\n  ' + afterFunction;
  console.log('✅ تم تحديث updateStocktake');
}

fs.writeFileSync(filePath, content, 'utf8');

console.log('🎉 تم تحديث وظائف الجرد بنجاح!');
console.log('ملاحظة: الجرد يحتاج إنشاء API endpoint جديد لاحقاً');
