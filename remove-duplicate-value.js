const fs = require('fs');

console.log('🔧 إزالة value object المكرر...');

try {
  let content = fs.readFileSync('context/store.tsx', 'utf8');
  
  // البحث عن value object الثاني
  const firstValueIndex = content.indexOf('const value = {');
  const secondValueIndex = content.indexOf('const value = {', firstValueIndex + 1);
  
  if (secondValueIndex !== -1) {
    console.log('تم العثور على value object مكرر في المؤشر:', secondValueIndex);
    
    // البحث عن نهاية value object الثاني
    let braceCount = 0;
    let endIndex = -1;
    let searchStart = secondValueIndex + 'const value = {'.length;
    
    for (let i = searchStart; i < content.length; i++) {
      if (content[i] === '{') {
        braceCount++;
      } else if (content[i] === '}') {
        braceCount--;
        if (braceCount === -1) { // وصلنا لإغلاق value object
          endIndex = i + 1;
          // البحث عن نهاية السطر
          while (endIndex < content.length && content[endIndex] !== '\n') {
            endIndex++;
          }
          endIndex++; // تجاوز \n
          break;
        }
      }
    }
    
    if (endIndex !== -1) {
      console.log('سيتم حذف النص من المؤشر', secondValueIndex, 'إلى', endIndex);
      
      // حذف value object الثاني
      const beforeSecondValue = content.substring(0, secondValueIndex);
      const afterSecondValue = content.substring(endIndex);
      
      content = beforeSecondValue + afterSecondValue;
      
      // كتابة الملف
      fs.writeFileSync('context/store.tsx', content, 'utf8');
      
      console.log('✅ تم حذف value object المكرر بنجاح!');
    } else {
      console.error('❌ لم يتم العثور على نهاية value object المكرر');
    }
  } else {
    console.log('✅ لا يوجد value object مكرر');
  }
  
} catch (error) {
  console.error('❌ خطأ:', error.message);
}
