# دليل تحديث وظائف الصيانة والاستلام والتسليم

## الوظائف التي تم استخراجها بنجاح

- addMaintenanceOrder
- updateMaintenanceOrder
- deleteMaintenanceOrder
- addMaintenanceReceiptOrder
- updateMaintenanceReceiptOrder
- deleteMaintenanceReceiptOrder
- addDeliveryOrder
- updateDeliveryOrder
- deleteDeliveryOrder

## خطوات التحديث

1. افتح ملف `context/store.tsx`
2. لكل وظيفة من الوظائف المذكورة أعلاه:
   - ابحث عن الوظيفة في الملف
   - استبدلها بالوظيفة المقابلة من ملف `maintenance-updated-functions.ts`

## ملاحظات هامة

- تأكد من الحفاظ على التوقيعات والأنواع الأصلية للوظائف
- بعد التحديث، أعد تشغيل الخادم واختبر الوظائف الجديدة
- تم إنشاء نسخة احتياطية من ملف `store.tsx` في: C:\Users\<USER>\Downloads\111\13\context\store.tsx.backup.2025-07-23T01-29-50-861Z

## عند التحديث اليدوي

عند استبدال كل وظيفة، تأكد من:
1. نسخ الوظيفة كاملة من `maintenance-updated-functions.ts`
2. البحث عن الوظيفة المقابلة في `store.tsx`
3. استبدال الوظيفة القديمة بالوظيفة الجديدة بالكامل
4. الحفاظ على التنسيق والمسافات البادئة في الملف

## تحقق من النجاح

بعد تحديث جميع الوظائف، تحقق من:
1. إمكانية إضافة أوامر صيانة واستلام وتسليم جديدة
2. إمكانية تعديل الأوامر الموجودة
3. إمكانية حذف الأوامر
4. استمرار وجود البيانات بعد تحديث الصفحة أو إعادة تشغيل التطبيق
