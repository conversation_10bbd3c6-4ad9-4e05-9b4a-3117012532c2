const fs = require('fs');
const path = require('path');
const glob = require('glob');

function fixAllSyntaxIssues() {
  console.log('🔧 إصلاح جميع مشاكل الصيغة في المشروع...\n');

  // البحث عن جميع ملفات TypeScript/JavaScript
  const files = glob.sync('app/**/*.{ts,tsx,js,jsx}', { 
    ignore: ['node_modules/**', 'dist/**', '.next/**'] 
  });

  console.log(`📁 معالجة ${files.length} ملف...\n`);

  let totalFixed = 0;
  let filesFixed = 0;

  const fixPatterns = [
    {
      name: 'إصلاح أقواس مفقودة في Array.isArray',
      search: /\(Array\.isArray\(([^)]+)\)\s*&&\s*([^)]+)\.some\(\s*\([^)]+\)\s*=>\s*[^)]+\s*\);/g,
      replace: '(Array.isArray($1) && $2.some((item) => item.condition));',
      test: (content) => /\(Array\.isArray\([^)]+\)\s*&&[^)]+\.some\([^)]+\)\s*=>\s*[^)]+\s*\);/.test(content)
    },
    {
      name: 'إصلاح if statements مع أقواس مفقودة',
      search: /if\s*\(([^)]+)\)\s+return\s+/g,
      replace: 'if ($1) return ',
      test: (content) => /if\s*\([^)]+\)\s+return\s+/.test(content)
    },
    {
      name: 'إصلاح استخدام $1 بدلاً من المتغير',
      search: /\$1\.items/g,
      replace: 'order.items',
      test: (content) => /\$1\.items/.test(content)
    },
    {
      name: 'إصلاح أقواس غير متطابقة في some/filter',
      search: /\(\s*Array\.isArray\([^)]+\)\s*&&\s*[^)]+\.some\(\s*[^)]+\s*=>\s*[^)]+\s*\)\s*;/g,
      replace: (match) => {
        // إضافة القوس المفقود
        return match.replace(/\)\s*;$/, '));');
      },
      test: (content) => /\(\s*Array\.isArray\([^)]+\)\s*&&\s*[^)]+\.some\(\s*[^)]+\s*=>\s*[^)]+\s*\)\s*;/.test(content)
    }
  ];

  files.forEach(filePath => {
    try {
      let content = fs.readFileSync(filePath, 'utf8');
      let fileFixed = 0;
      let hasChanges = false;
      const fileName = path.relative('.', filePath);

      // إصلاح خاص للمشاكل الشائعة
      const originalContent = content;

      // 1. إصلاح الأقواس المفقودة في Array.isArray && some
      const arrayIsArraySomePattern = /\(Array\.isArray\(([^)]+)\)\s*&&\s*([^)]+)\.some\(\s*\([^)]+\)\s*=>\s*[^)]+\s*\);/g;
      content = content.replace(arrayIsArraySomePattern, (match, arrayVar, objectVar) => {
        return match.replace(/\)\s*;$/, '));');
      });

      // 2. إصلاح if statements مع return
      content = content.replace(/if\s*\(([^)]+)\)\s+return\s+/g, 'if ($1) return ');

      // 3. إصلاح استخدام $1
      content = content.replace(/\$1\./g, 'order.');

      // 4. إصلاح أقواس مضاعفة
      content = content.replace(/\)\)\)/g, '))');
      content = content.replace(/\(\(Array\.isArray/g, '(Array.isArray');

      // 5. إصلاح فواصل زائدة
      content = content.replace(/,\s*\n\s*\);/g, '\n    );');

      if (content !== originalContent) {
        hasChanges = true;
        fileFixed = 1;
      }

      // فحص إضافي للمشاكل الشائعة
      const issues = [];
      
      // فحص الأقواس غير المتطابقة
      const openParens = (content.match(/\(/g) || []).length;
      const closeParens = (content.match(/\)/g) || []).length;
      if (openParens !== closeParens) {
        issues.push(`أقواس غير متطابقة: ${openParens} فتح، ${closeParens} إغلاق`);
      }

      // فحص استخدام متغيرات غير معرفة
      if (content.includes('permissions') && !content.includes('const permissions =')) {
        // إضافة تعريف permissions
        const useEffectMatch = content.match(/useEffect\(\(\) => \{[\s\S]*?\}, \[\]\);/);
        if (useEffectMatch) {
          content = content.replace(
            useEffectMatch[0],
            useEffectMatch[0] + '\n\n  // Temporary permissions\n  const permissions = { create: true, edit: true, delete: true };'
          );
          hasChanges = true;
          fileFixed++;
        }
      }

      if (hasChanges) {
        fs.writeFileSync(filePath, content);
        console.log(`✅ ${fileName}: تم إصلاح ${fileFixed} مشكلة`);
        filesFixed++;
        totalFixed += fileFixed;
      } else if (issues.length === 0) {
        // console.log(`ℹ️ ${fileName}: نظيف`);
      } else {
        console.log(`⚠️ ${fileName}: ${issues.join(', ')}`);
      }

    } catch (error) {
      console.log(`❌ خطأ في معالجة ${filePath}: ${error.message}`);
    }
  });

  console.log(`\n📊 ملخص الإصلاح:`);
  console.log(`   - إجمالي الملفات المعالجة: ${files.length}`);
  console.log(`   - ملفات تم إصلاحها: ${filesFixed}`);
  console.log(`   - إجمالي المشاكل المُصلحة: ${totalFixed}`);

  if (totalFixed > 0) {
    console.log(`\n🎉 تم إصلاح ${totalFixed} مشكلة في ${filesFixed} ملف!`);
    console.log(`\n📝 يُنصح بإجراء اختبار شامل للتأكد من عمل جميع الصفحات`);
    return true;
  } else {
    console.log(`\nℹ️ لا توجد مشاكل صيغة تحتاج إصلاح`);
    return true;
  }
}

if (require.main === module) {
  const success = fixAllSyntaxIssues();
  process.exit(success ? 0 : 1);
}

module.exports = { fixAllSyntaxIssues };
