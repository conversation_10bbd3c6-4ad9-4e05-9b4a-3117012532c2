const fs = require('fs');
const path = require('path');

function verifyApiEndpoints() {
  console.log('🔍 التحقق من API endpoints...\n');

  const apiFiles = [
    { file: 'app/api/supply/route.ts', name: 'Supply Orders', description: 'أوامر التوريد' },
    { file: 'app/api/sales/route.ts', name: 'Sales', description: 'المبيعات' },
    { file: 'app/api/returns/route.ts', name: 'Returns', description: 'المرتجعات' },
    { file: 'app/api/evaluations/route.ts', name: 'Evaluations', description: 'أوامر التقييم' },
    { file: 'app/api/maintenance-orders/route.ts', name: 'Maintenance Orders', description: 'أوامر الصيانة' },
    { file: 'app/api/delivery-orders/route.ts', name: 'Delivery Orders', description: 'أوامر التسليم' },
    { file: 'app/api/maintenance-receipts/route.ts', name: 'Maintenance Receipts', description: 'أوامر استلام الصيانة' }
  ];

  let allApisUpdated = true;

  for (const api of apiFiles) {
    try {
      if (!fs.existsSync(api.file)) {
        console.log(`❌ ${api.name} (${api.description}): الملف غير موجود`);
        allApisUpdated = false;
        continue;
      }

      const content = fs.readFileSync(api.file, 'utf8');
      
      // التحقق من وجود include: { items: true }
      const hasInclude = content.includes('include: { items: true }') || content.includes('include: {\n        items: true');
      
      // التحقق من عدم وجود معالجة JSON القديمة
      const hasOldJsonProcessing = content.includes('JSON.parse(') && content.includes('.items');
      
      // التحقق من وجود إنشاء العناصر الوسيطة
      const hasItemCreation = content.includes('.create({') && content.includes('data: {');

      let status = '✅';
      let issues = [];

      if (!hasInclude) {
        issues.push('لا يحتوي على include للعناصر');
        status = '⚠️';
      }

      if (hasOldJsonProcessing) {
        issues.push('يحتوي على معالجة JSON قديمة');
        status = '⚠️';
      }

      if (!hasItemCreation) {
        issues.push('لا يحتوي على إنشاء العناصر الوسيطة');
        status = '⚠️';
      }

      if (issues.length > 0) {
        console.log(`${status} ${api.name} (${api.description}): ${issues.join(', ')}`);
        if (status === '⚠️') allApisUpdated = false;
      } else {
        console.log(`${status} ${api.name} (${api.description}): محدث بالكامل`);
      }

    } catch (error) {
      console.log(`❌ ${api.name} (${api.description}): خطأ في القراءة - ${error.message}`);
      allApisUpdated = false;
    }
  }

  console.log('\n📱 التحقق من ملفات الواجهة الأمامية...');

  const frontendFiles = [
    'app/(main)/supply/page.tsx',
    'app/(main)/sales/page.tsx', 
    'app/(main)/returns/page.tsx',
    'app/(main)/evaluations/page.tsx',
    'app/(main)/maintenance/page.tsx',
    'app/(main)/delivery/page.tsx'
  ];

  let frontendUpdated = true;

  for (const file of frontendFiles) {
    try {
      if (!fs.existsSync(file)) {
        console.log(`⚠️ ${path.basename(file)}: الملف غير موجود`);
        continue;
      }

      const content = fs.readFileSync(file, 'utf8');
      
      // التحقق من عدم وجود معالجة JSON المعقدة القديمة
      const hasComplexJsonProcessing = content.includes('typeof') && content.includes('JSON.parse') && content.includes('.items');
      
      if (hasComplexJsonProcessing) {
        console.log(`⚠️ ${path.basename(file)}: يحتوي على معالجة JSON معقدة قديمة`);
        frontendUpdated = false;
      } else {
        console.log(`✅ ${path.basename(file)}: محدث`);
      }

    } catch (error) {
      console.log(`❌ ${path.basename(file)}: خطأ في القراءة`);
      frontendUpdated = false;
    }
  }

  if (allApisUpdated && frontendUpdated) {
    console.log('\n🎉 جميع API endpoints والواجهات الأمامية محدثة بشكل صحيح!');
    console.log('✅ تم تطبيق إعادة الهيكلة على جميع الأقسام');
    return true;
  } else {
    console.log('\n⚠️ هناك بعض الملفات التي تحتاج مراجعة');
    return false;
  }
}

if (require.main === module) {
  const success = verifyApiEndpoints();
  process.exit(success ? 0 : 1);
}

module.exports = { verifyApiEndpoints };
