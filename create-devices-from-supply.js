const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function createDevicesFromSupply() {
  try {
    console.log('🔄 إنشاء أجهزة من أوامر التوريد...\n');

    // جلب أوامر التوريد مع العناصر
    const supplyOrders = await prisma.supplyOrder.findMany({
      include: { items: true }
    });

    console.log(`📦 عدد أوامر التوريد: ${supplyOrders.length}`);

    let devicesCreated = 0;
    let devicesSkipped = 0;

    for (const order of supplyOrders) {
      console.log(`\n📋 معالجة أمر التوريد: ${order.id}`);
      console.log(`   عدد العناصر: ${order.items.length}`);

      for (const item of order.items) {
        try {
          // التحقق من وجود الجهاز
          const existingDevice = await prisma.device.findUnique({
            where: { id: item.imei }
          });

          if (existingDevice) {
            console.log(`   ⏭️ الجهاز ${item.imei} موجود بالفعل`);
            devicesSkipped++;
            continue;
          }

          // إنشاء الجهاز
          const device = await prisma.device.create({
            data: {
              id: item.imei,
              model: `${item.manufacturer} ${item.model}`,
              status: 'متاح للبيع',
              condition: item.condition || 'جديد',
              warehouseId: order.warehouseId || null,
              storage: null,
              color: null,
              purchasePrice: item.purchasePrice || null,
              sellingPrice: item.sellingPrice || null,
              notes: `تم إنشاؤه من أمر التوريد ${order.id}`,
              replacementInfo: null
            }
          });

          console.log(`   ✅ تم إنشاء الجهاز: ${device.id} - ${device.model}`);
          devicesCreated++;

        } catch (error) {
          console.error(`   ❌ خطأ في إنشاء الجهاز ${item.imei}:`, error.message);
        }
      }
    }

    console.log(`\n📊 النتائج:`);
    console.log(`   ✅ أجهزة تم إنشاؤها: ${devicesCreated}`);
    console.log(`   ⏭️ أجهزة تم تخطيها: ${devicesSkipped}`);

    // التحقق من النتيجة النهائية
    const totalDevices = await prisma.device.count();
    console.log(`   📱 إجمالي الأجهزة في قاعدة البيانات: ${totalDevices}`);

  } catch (error) {
    console.error('❌ خطأ في إنشاء الأجهزة:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createDevicesFromSupply();
