# نظام المرفقات في المراسلات

## نظرة عامة
تم تطوير نظام جديد لإدارة المرفقات في المراسلات يعتمد على تخزين الملفات في نظام الملفات بدلاً من قاعدة البيانات.

## المميزات الجديدة

### 1. تخزين الملفات
- **المجلد:** `public/attachments/`
- **بدون حدود حجم:** يمكن رفع ملفات بأي حجم
- **أسماء فريدة:** كل ملف يحصل على اسم فريد لتجنب التضارب
- **أنواع مدعومة:** جميع أنواع الملفات (صور، PDF، مستندات، إلخ)

### 2. عرض الملفات
- **الصور:** تُعرض مباشرة في المحادثة مع خيارات العرض والتحميل
- **ملفات PDF:** يمكن فتحها في نافذة جديدة أو تحميلها
- **الملفات الأخرى:** تظهر مع أيقونة وخيار التحميل

### 3. إدارة الملفات
- **رفع تلقائي:** الملفات تُرفع تلقائياً عند الإرسال
- **عرض الحجم:** يظهر حجم كل ملف
- **تحميل مباشر:** رابط تحميل مباشر لكل ملف

## البنية التقنية

### API Endpoints
- `POST /api/attachments` - رفع ملف جديد
- `GET /api/attachments?file=filename` - جلب معلومات ملف
- `DELETE /api/attachments/delete` - حذف ملف

### قاعدة البيانات
تم إضافة حقول جديدة لجدول الرسائل:
- `attachmentUrl` - رابط الملف
- `attachmentFileName` - اسم الملف المحفوظ
- `attachmentSize` - حجم الملف
- `attachmentContent` - Base64 للصور الصغيرة فقط (للعرض السريع)

### أمان الملفات
- الملفات محفوظة في `public/attachments/`
- أسماء فريدة تمنع التضارب
- إمكانية الوصول المباشر عبر URL

## الاستخدام

### للمطورين
```typescript
// رفع ملف
const formData = new FormData();
formData.append('file', file);
const response = await fetch('/api/attachments', {
  method: 'POST',
  body: formData,
});

// حذف ملف
await fetch('/api/attachments/delete', {
  method: 'DELETE',
  body: JSON.stringify({ fileName: 'filename.ext' }),
});
```

### للمستخدمين
1. اختر ملف من جهازك
2. الملف يُرفع تلقائياً عند الإرسال
3. يمكن عرض/تحميل الملف من المحادثة

## الصيانة
- تنظيف الملفات القديمة يدوياً حسب الحاجة
- مراقبة مساحة التخزين
- نسخ احتياطي لمجلد المرفقات

## الترقية من النظام القديم
- الرسائل القديمة تحتفظ بـ Base64 في `attachmentContent`
- الرسائل الجديدة تستخدم `attachmentUrl`
- النظام يدعم كلا الطريقتين للتوافق العكسي
