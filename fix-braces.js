const fs = require('fs');

console.log('🔧 إصلاح توازن الأقواس في store.tsx...');

try {
  let content = fs.readFileSync('context/store.tsx', 'utf8');
  
  // العد الأولي للأقواس
  let openBraces = (content.match(/\{/g) || []).length;
  let closeBraces = (content.match(/\}/g) || []).length;
  
  console.log(`قبل الإصلاح: ${openBraces} أقواس فتح، ${closeBraces} أقواس إغلاق`);
  
  if (closeBraces > openBraces) {
    // هناك قوس إغلاق زائد، نحتاج لإزالته
    const difference = closeBraces - openBraces;
    console.log(`يجب إزالة ${difference} قوس إغلاق زائد`);
    
    // البحث عن الأقواس الزائدة في نهاية الملف
    const lines = content.split('\n');
    let removedBraces = 0;
    
    // البحث من النهاية للبداية
    for (let i = lines.length - 1; i >= 0 && removedBraces < difference; i--) {
      if (lines[i].trim() === '}' && removedBraces === 0) {
        // تحقق إذا كان هذا قوس زائد
        lines[i] = '';
        removedBraces++;
        console.log(`تم إزالة قوس من السطر ${i + 1}`);
      }
    }
    
    content = lines.join('\n');
  } else if (openBraces > closeBraces) {
    // هناك قوس فتح زائد، نحتاج لإضافة قوس إغلاق
    const difference = openBraces - closeBraces;
    console.log(`يجب إضافة ${difference} قوس إغلاق`);
    
    // إضافة الأقواس المطلوبة في النهاية
    for (let i = 0; i < difference; i++) {
      content += '\n}';
    }
  }
  
  // كتابة الملف المُصحح
  fs.writeFileSync('context/store.tsx', content, 'utf8');
  
  // إعادة فحص
  openBraces = (content.match(/\{/g) || []).length;
  closeBraces = (content.match(/\}/g) || []).length;
  
  console.log(`بعد الإصلاح: ${openBraces} أقواس فتح، ${closeBraces} أقواس إغلاق`);
  
  if (openBraces === closeBraces) {
    console.log('✅ تم إصلاح توازن الأقواس بنجاح!');
  } else {
    console.log('❌ لا تزال هناك مشكلة في توازن الأقواس');
  }
  
} catch (error) {
  console.error('❌ خطأ في إصلاح الملف:', error.message);
}
