# إصلاح مشكلة عدم حفظ أوامر الإرسال للصيانة

## المشكلة المحددة

صفحة **إرسال الأجهزة إلى الصيانة** في التطبيق لا تحتفظ بالبيانات بشكل دائم، بينما صفحة **استلام الأجهزة من الصيانة** تعمل بشكل صحيح.

## السبب

وظيفة `addMaintenanceOrder` في ملف `context/store.tsx` لا تستخدم API، بل تحفظ البيانات محلياً فقط.

## الحل

استبدال وظيفة `addMaintenanceOrder` في ملف `context/store.tsx` (حوالي السطر 867) بالكود التالي:

```typescript
const addMaintenanceOrder = async (
  order: Omit<MaintenanceOrder, "id" | "createdAt"> & {
    id?: number;
    status?: "wip" | "completed" | "draft";
  },
) => {
  try {
    // إذا لم يكن هناك رقم أمر، نقوم بإنشاء واحد
    if (!order.orderNumber) {
      const allExisting = [...maintenanceOrders];
      const useId =
        order.id && order.id > 0
          ? order.id
          : Math.max(0, ...allExisting.map((o) => o.id)) + 1;
      order.orderNumber = `MAINT-${useId}`;
    }
    
    // إرسال الأمر إلى API
    const response = await fetch('/api/maintenance-orders', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        ...order,
        status: order.status || 'wip',
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to create maintenance order');
    }

    // استقبال الأمر الذي تم إنشاؤه من API
    const newOrder = await response.json();

    // تحديث حالة التطبيق
    setMaintenanceOrders((prev) => [newOrder, ...prev]);

    // إضافة نشاط
    addActivity({
      type: "maintenance",
      description: `تم إنشاء أمر صيانة ${newOrder.orderNumber}.`,
    });
    
    return newOrder;
  } catch (error) {
    console.error('Failed to add maintenance order:', error);
    addActivity({
      type: "maintenance",
      description: `⚠️ فشل في إنشاء أمر صيانة: ${error instanceof Error ? error.message : String(error)}`,
    });
    throw error;
  }
};
```

## خطوات التطبيق

1. **افتح ملف `context/store.tsx`**
2. **ابحث عن وظيفة `addMaintenanceOrder` (حوالي السطر 867)**
3. **استبدل محتوى الوظيفة بالكود أعلاه**
4. **احفظ الملف**
5. **اختبر إنشاء أمر إرسال جديد للصيانة**

## النتيجة المتوقعة

بعد هذا التحديث:
- ✅ صفحة الإرسال إلى الصيانة ستحتفظ بالبيانات
- ✅ صفحة الاستلام من الصيانة ستستمر في العمل بشكل صحيح
- ✅ جميع أوامر الصيانة ستُحفظ في قاعدة البيانات
- ✅ البيانات ستبقى موجودة بعد إعادة تشغيل التطبيق

## ملاحظات إضافية

- تأكد من وجود نسخة احتياطية من الملف قبل التعديل
- يمكنك اختبار الصفحة بعد التحديث للتأكد من عملها
- إذا واجهت أي مشاكل، يمكنك الرجوع للنسخة الاحتياطية
