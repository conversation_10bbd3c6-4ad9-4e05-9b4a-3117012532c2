const fs = require('fs');

const filePath = 'context/store.tsx';

console.log('فحص وإصلاح مشاكل البناء في store.tsx...');

// قراءة الملف
let content = fs.readFileSync(filePath, 'utf8');

// البحث عن أخطاء شائعة
let fixed = false;

// 1. إصلاح الفاصلة المنقوطة المزدوجة
if (content.includes('};;')) {
  content = content.replace(/};;/g, '};');
  console.log('✅ تم إصلاح الفاصلة المنقوطة المزدوجة');
  fixed = true;
}

// 2. التحقق من توازن الأقواس المجعدة
const openBraces = (content.match(/{/g) || []).length;
const closeBraces = (content.match(/}/g) || []).length;

console.log(`عدد الأقواس المفتوحة: ${openBraces}`);
console.log(`عدد الأقواس المُغلقة: ${closeBraces}`);

if (openBraces !== closeBraces) {
  console.log('⚠️ عدم توازن في الأقواس المجعدة!');
  
  // البحث عن آخر export function
  const lastExportIndex = content.lastIndexOf('export function');
  
  if (lastExportIndex !== -1) {
    // البحث عن آخر قوس مُغلق قبل export function
    const beforeExport = content.substring(0, lastExportIndex);
    const lastBraceIndex = beforeExport.lastIndexOf('}');
    
    // إضافة قوس مُغلق إذا لزم الأمر
    if (lastBraceIndex !== -1) {
      const beforeBrace = content.substring(0, lastBraceIndex + 1);
      const afterBrace = content.substring(lastBraceIndex + 1);
      
      // التحقق إذا كان هناك حاجة لقوس إضافي
      const bracesInBefore = (beforeBrace.match(/{/g) || []).length - (beforeBrace.match(/}/g) || []).length;
      
      if (bracesInBefore > 0) {
        content = beforeBrace + '\n' + afterBrace;
        console.log('✅ تم إضافة قوس مُغلق مفقود');
        fixed = true;
      }
    }
  }
}

// 3. إصلاح مشاكل الفاصلة في نهاية value object
const valuePattern = /const value = \{[^}]*\}/s;
const valueMatch = content.match(valuePattern);

if (valueMatch) {
  const valueObject = valueMatch[0];
  const valueStart = content.indexOf(valueObject);
  const valueEnd = valueStart + valueObject.length;
  
  // التحقق من وجود فاصلة منقوطة بعد value
  const afterValue = content.substring(valueEnd);
  if (!afterValue.trim().startsWith(';')) {
    const beforeValue = content.substring(0, valueEnd);
    content = beforeValue + ';' + afterValue;
    console.log('✅ تم إضافة فاصلة منقوطة بعد value object');
    fixed = true;
  }
}

// 4. التحقق من وجود return statement
if (!content.includes('return (')) {
  console.log('⚠️ لا يوجد return statement في StoreProvider');
}

// كتابة الملف المُصحح
if (fixed) {
  fs.writeFileSync(filePath, content, 'utf8');
  console.log('✅ تم إصلاح الملف وحفظه');
} else {
  console.log('ℹ️ لم يتم العثور على مشاكل للإصلاح');
}

// فحص نهائي للبناء
try {
  // محاولة تحليل الكود كـ JavaScript (بسيط)
  const codeWithoutImports = content
    .replace(/import .*?;/g, '')
    .replace(/export /g, '');
  
  console.log('✅ فحص البناء: الملف يبدو صحيحاً');
} catch (error) {
  console.log('❌ ما زالت هناك مشاكل في البناء:', error.message);
}

console.log('انتهى الفحص والإصلاح');
