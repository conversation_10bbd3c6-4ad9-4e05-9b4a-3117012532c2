const fs = require('fs');
const path = require('path');
const glob = require('glob');

function fixPermissionsReferences() {
  console.log('🔧 إصلاح مراجع permissions في جميع الصفحات...\n');

  // البحث عن جميع ملفات الصفحات
  const pageFiles = glob.sync('app/(main)/*/page.tsx', { 
    ignore: ['node_modules/**', 'dist/**', '.next/**'] 
  });

  console.log(`📁 فحص ${pageFiles.length} صفحة...\n`);

  let totalFixed = 0;
  let filesFixed = 0;

  pageFiles.forEach(filePath => {
    try {
      let content = fs.readFileSync(filePath, 'utf8');
      let hasChanges = false;
      const pageName = path.basename(path.dirname(filePath));

      // البحث عن استخدامات permissions
      const permissionsUsage = content.match(/permissions\?\.(\w+)/g);
      
      if (permissionsUsage && !content.includes('const permissions = ')) {
        console.log(`📄 ${pageName}: وجدت ${permissionsUsage.length} استخدام لـ permissions`);
        
        // إضافة تعريف permissions مؤقت
        const permissionsDefinition = '\n  // Temporary permissions (remove when auth system is implemented)\n  const permissions = { create: true, edit: true, delete: true, view: true, viewAll: true };\n';
        
        // البحث عن مكان مناسب لإضافة التعريف
        const useEffectMatch = content.match(/useEffect\(\(\) => \{[\s\S]*?\}, \[\]\);/);
        if (useEffectMatch) {
          content = content.replace(
            useEffectMatch[0],
            useEffectMatch[0] + permissionsDefinition
          );
          hasChanges = true;
          totalFixed += permissionsUsage.length;
        } else {
          // إضافة بعد آخر useState
          const lastUseStateMatch = content.match(/const \[[\s\S]*?\] = useState\([\s\S]*?\);(?=\s*\n)/g);
          if (lastUseStateMatch) {
            const lastUseState = lastUseStateMatch[lastUseStateMatch.length - 1];
            content = content.replace(
              lastUseState,
              lastUseState + permissionsDefinition
            );
            hasChanges = true;
            totalFixed += permissionsUsage.length;
          }
        }
      }

      // إصلاح مراجع currentUser غير معرفة
      if (content.includes('currentUser') && !content.includes('useStore()') && !content.includes('const { currentUser }')) {
        // إضافة import useStore إذا لم يكن موجوداً
        if (!content.includes('useStore')) {
          content = content.replace(
            /import.*from.*@\/hooks\/use-toast.*/,
            `$&\nimport { useStore } from '@/context/store';`
          );
        }

        // إضافة currentUser من useStore
        if (!content.includes('const { currentUser }')) {
          const permissionsLine = content.match(/const permissions = .*/);
          if (permissionsLine) {
            content = content.replace(
              permissionsLine[0],
              `  // Temporary: get currentUser from store\n  const { currentUser } = useStore();\n\n${permissionsLine[0]}`
            );
            hasChanges = true;
          }
        }
      }

      if (hasChanges) {
        fs.writeFileSync(filePath, content);
        console.log(`   ✅ تم إصلاح ${pageName}`);
        filesFixed++;
      } else {
        console.log(`   ℹ️ ${pageName}: لا يحتاج إصلاح`);
      }

    } catch (error) {
      console.log(`   ❌ خطأ في معالجة ${filePath}: ${error.message}`);
    }
  });

  console.log(`\n📊 ملخص الإصلاح:`);
  console.log(`   - إجمالي الصفحات المفحوصة: ${pageFiles.length}`);
  console.log(`   - ملفات تم إصلاحها: ${filesFixed}`);
  console.log(`   - إجمالي المراجع المُصلحة: ${totalFixed}`);

  if (totalFixed > 0) {
    console.log(`\n🎉 تم إصلاح ${totalFixed} مرجع permissions في ${filesFixed} ملف!`);
    console.log(`\n📝 ملاحظة: هذه إصلاحات مؤقتة. يُنصح بتطبيق نظام مصادقة متكامل لاحقاً.`);
    return true;
  } else {
    console.log(`\nℹ️ لا توجد مراجع permissions تحتاج إصلاح`);
    return true;
  }
}

if (require.main === module) {
  const success = fixPermissionsReferences();
  process.exit(success ? 0 : 1);
}

module.exports = { fixPermissionsReferences };
