// Script شامل لتحديث وظائف إعدادات النظام والجرد والتخويل المخزني لاستخدام API
// نسخ احتياطية قبل التشغيل

const fs = require('fs');

const filePath = 'context/store.tsx';

// قراءة الملف
let content = fs.readFileSync(filePath, 'utf8');

console.log('🔄 بدء تحديث وظائف إعدادات النظام والجرد والتخويل المخزني...\n');

// ============ 1. تحديث updateSystemSettings ============
console.log('📝 تحديث وظيفة updateSystemSettings...');
const updateSystemSettingsStart = 'const updateSystemSettings = (settings: SystemSettings) => {';
const updateSystemSettingsIndex = content.indexOf(updateSystemSettingsStart);

if (updateSystemSettingsIndex !== -1) {
  // البحث عن نهاية الوظيفة
  const nextFunctionStart = content.indexOf('const addEmployeeRequest', updateSystemSettingsIndex);
  const updateSystemSettingsEndIndex = content.lastIndexOf('};', nextFunctionStart);
  
  const newUpdateSystemSettingsFunction = `const updateSystemSettings = async (settings: SystemSettings) => {
    try {
      // إرسال التحديث إلى API
      const response = await fetch('/api/settings', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(settings),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update system settings');
      }

      // استقبال الإعدادات المحدثة من API
      const updatedSettings = await response.json();

      // تحديث حالة التطبيق
      setSystemSettings(updatedSettings);

      addActivity({
        type: "supply",
        description: "تم تحديث إعدادات النظام بنجاح",
      });
      
      return updatedSettings;
    } catch (error) {
      console.error('Failed to update system settings:', error);
      addActivity({
        type: "supply",
        description: \`⚠️ فشل في تحديث إعدادات النظام: \${error instanceof Error ? error.message : String(error)}\`,
      });
      throw error;
    }
  };`;

  const beforeFunction = content.substring(0, updateSystemSettingsIndex);
  const afterFunction = content.substring(updateSystemSettingsEndIndex + 2);
  content = beforeFunction + newUpdateSystemSettingsFunction + '\n\n  ' + afterFunction;
  console.log('✅ تم تحديث updateSystemSettings بنجاح');
}

// ============ 2. تحديث addStocktake ============
console.log('📝 تحديث وظيفة addStocktake...');
const addStocktakeStart = 'const addStocktake = (';
const addStocktakeIndex = content.indexOf(addStocktakeStart);

if (addStocktakeIndex !== -1) {
  // البحث عن نهاية الوظيفة - البحث عن updateStocktake
  const nextFunctionStart = content.indexOf('const updateStocktake', addStocktakeIndex);
  const addStocktakeEndIndex = content.lastIndexOf('};', nextFunctionStart);
  
  const newAddStocktakeFunction = `const addStocktake = async (
    stocktakeData: Omit<
      StocktakeV1,
      "id" | "operationNumber" | "createdAt" | "lastModifiedAt"
    >,
  ) => {
    try {
      // إعداد البيانات الأساسية
      const operationNumber = \`ST-\${Date.now()}\`;
      const now = new Date().toISOString();

      const newStocktakeData = {
        ...stocktakeData,
        operationNumber,
        createdAt: now,
        lastModifiedAt: now,
        items: [], // Initialize items array
        discrepancies: [], // Initialize discrepancies array
        totalExpected: 0,
        totalScanned: 0,
        totalMatching: 0,
        totalDiscrepancies: 0,
        status: 'في التقدم' as StocktakeStatus,
      };

      // إنشاء API للجرد إذا لم يكن موجوداً (نحفظ محلياً في الوقت الحالي)
      // TODO: إنشاء /api/stocktakes endpoint
      
      // حالياً نحفظ محلياً حتى يتم إنشاء API
      const newId = Math.max(0, ...stocktakes.map((s) => s.id)) + 1;
      const newStocktake: StocktakeV1 = {
        ...newStocktakeData,
        id: newId,
      };

      setStocktakes((prev) => [newStocktake, ...prev]);

      addActivity({
        type: "supply",
        description: \`تم إنشاء عملية جرد جديدة: \${operationNumber}\`,
      });
      
      return newStocktake;
    } catch (error) {
      console.error('Failed to add stocktake:', error);
      addActivity({
        type: "supply",
        description: \`⚠️ فشل في إنشاء عملية جرد: \${error instanceof Error ? error.message : String(error)}\`,
      });
      throw error;
    }
  };`;

  const beforeFunction = content.substring(0, addStocktakeIndex);
  const afterFunction = content.substring(addStocktakeEndIndex + 2);
  content = beforeFunction + newAddStocktakeFunction + '\n\n  ' + afterFunction;
  console.log('✅ تم تحديث addStocktake بنجاح');
}

// ============ 3. تحديث updateStocktake ============
console.log('📝 تحديث وظيفة updateStocktake...');
const updateStocktakeStart = 'const updateStocktake = (stocktake: StocktakeV1) => {';
const updateStocktakeIndex = content.indexOf(updateStocktakeStart);

if (updateStocktakeIndex !== -1) {
  // البحث عن نهاية الوظيفة
  const nextFunctionStart = content.indexOf('const addStocktakeItem', updateStocktakeIndex);
  const updateStocktakeEndIndex = content.lastIndexOf('};', nextFunctionStart);
  
  const newUpdateStocktakeFunction = \`const updateStocktake = async (stocktake: StocktakeV1) => {
    try {
      // تحديث الوقت المعدل
      const updatedStocktake = {
        ...stocktake,
        lastModifiedAt: new Date().toISOString(),
      };

      // حالياً نحفظ محلياً حتى يتم إنشاء API
      // TODO: إرسال إلى /api/stocktakes

      setStocktakes((prev) =>
        prev.map((s) => (s.id === updatedStocktake.id ? updatedStocktake : s)),
      );

      addActivity({
        type: "supply",
        description: \\\`تم تحديث عملية الجرد: \\\${updatedStocktake.operationNumber}\\\`,
      });
      
      return updatedStocktake;
    } catch (error) {
      console.error('Failed to update stocktake:', error);
      addActivity({
        type: "supply",
        description: \\\`⚠️ فشل في تحديث عملية الجرد: \\\${error instanceof Error ? error.message : String(error)}\\\`,
      });
      throw error;
    }
  };\`;

  const beforeFunction = content.substring(0, updateStocktakeIndex);
  const afterFunction = content.substring(updateStocktakeEndIndex + 2);
  content = beforeFunction + newUpdateStocktakeFunction + '\n\n  ' + afterFunction;
  console.log('✅ تم تحديث updateStocktake بنجاح');
}

// ============ 4. تحديث addWarehouseTransfer ============
console.log('📝 تحديث وظيفة addWarehouseTransfer...');
const addWarehouseTransferStart = 'const addWarehouseTransfer = (';
const addWarehouseTransferIndex = content.indexOf(addWarehouseTransferStart);

if (addWarehouseTransferIndex !== -1) {
  // البحث عن نهاية الوظيفة
  let braceCount = 0;
  let currentIndex = addWarehouseTransferIndex;
  let foundStart = false;
  
  while (currentIndex < content.length) {
    if (content[currentIndex] === '{') {
      braceCount++;
      foundStart = true;
    } else if (content[currentIndex] === '}') {
      braceCount--;
      if (foundStart && braceCount === 0) {
        break;
      }
    }
    currentIndex++;
  }
  
  const addWarehouseTransferEndIndex = currentIndex;
  
  const newAddWarehouseTransferFunction = \`const addWarehouseTransfer = async (
    transfer: Omit<WarehouseTransfer, "id" | "transferNumber" | "createdAt">,
  ) => {
    try {
      // إعداد البيانات الأساسية
      const transferNumber = \\\`WT-\\\${Date.now()}\\\`;
      const newTransferData = {
        ...transfer,
        transferNumber,
        createdAt: new Date().toISOString(),
      };

      // حالياً نحفظ محلياً حتى يتم إنشاء API
      // TODO: إنشاء /api/warehouse-transfers endpoint
      
      const newId = Math.max(0, ...warehouseTransfers.map((t) => t.id)) + 1;
      const newTransfer: WarehouseTransfer = {
        ...newTransferData,
        id: newId,
      };

      setWarehouseTransfers((prev) => [newTransfer, ...prev]);

      // تحديث حالة الأجهزة
      transfer.items.forEach((item) => {
        updateDeviceStatus(item.deviceId, "محول");
      });

      addActivity({
        type: "supply",
        description: \\\`تم إنشاء تحويل مخزني جديد: \\\${transferNumber}\\\`,
      });
      
      return newTransfer;
    } catch (error) {
      console.error('Failed to add warehouse transfer:', error);
      addActivity({
        type: "supply",
        description: \\\`⚠️ فشل في إنشاء تحويل مخزني: \\\${error instanceof Error ? error.message : String(error)}\\\`,
      });
      throw error;
    }
  };\`;

  const beforeFunction = content.substring(0, addWarehouseTransferIndex);
  const afterFunction = content.substring(addWarehouseTransferEndIndex + 1);
  content = beforeFunction + newAddWarehouseTransferFunction + afterFunction;
  console.log('✅ تم تحديث addWarehouseTransfer بنجاح');
}

// كتابة الملف المحدث
fs.writeFileSync(filePath, content, 'utf8');

console.log('\n🎉 تم تحديث جميع وظائف إعدادات النظام والجرد والتخويل المخزني بنجاح!');
console.log('✅ updateSystemSettings - يستخدم API الآن');
console.log('✅ addStocktake - محسن مع خطة لاستخدام API');
console.log('✅ updateStocktake - محسن مع خطة لاستخدام API');
console.log('✅ addWarehouseTransfer - محسن مع خطة لاستخدام API');
console.log('⚠️  ملاحظة: بعض الوظائف تحتاج إنشاء API endpoints جديدة لتكتمل الوظائف');
console.log('الآن صفحات الإعدادات ستحتفظ بالبيانات بشكل أفضل.');
