// تحديث ملف store.tsx لاستخدام API
import { MaintenanceOrder, MaintenanceReceiptOrder, DeliveryOrder } from './lib/types';
import * as fs from 'fs';
import * as path from 'path';

// وظائف معالجة الأوامر من خلال API - تعريفات مؤقتة
async function addMaintenanceOrderAPI(order: MaintenanceOrder) {
  try {
    const response = await fetch('/api/maintenance-orders', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(order),
    });

    if (!response.ok) {
      throw new Error('Failed to create maintenance order');
    }

    return await response.json();
  } catch (error) {
    console.error('Failed to add maintenance order:', error);
    throw error;
  }
}

async function updateMaintenanceOrderAPI(order: MaintenanceOrder) {
  try {
    const response = await fetch('/api/maintenance-orders', {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(order),
    });

    if (!response.ok) {
      throw new Error('Failed to update maintenance order');
    }

    return await response.json();
  } catch (error) {
    console.error('Failed to update maintenance order:', error);
    throw error;
  }
}

async function deleteMaintenanceOrderAPI(id: number) {
  try {
    const response = await fetch('/api/maintenance-orders', {
      method: 'DELETE',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ id }),
    });

    if (!response.ok) {
      throw new Error('Failed to delete maintenance order');
    }

    return true;
  } catch (error) {
    console.error('Failed to delete maintenance order:', error);
    throw error;
  }
}

// وظائف استلام الصيانة
async function addMaintenanceReceiptOrderAPI(order: MaintenanceReceiptOrder) {
  try {
    const response = await fetch('/api/maintenance-receipts', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(order),
    });

    if (!response.ok) {
      throw new Error('Failed to create maintenance receipt order');
    }

    return await response.json();
  } catch (error) {
    console.error('Failed to add maintenance receipt order:', error);
    throw error;
  }
}

async function updateMaintenanceReceiptOrderAPI(order: MaintenanceReceiptOrder) {
  try {
    const response = await fetch('/api/maintenance-receipts', {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(order),
    });

    if (!response.ok) {
      throw new Error('Failed to update maintenance receipt order');
    }

    return await response.json();
  } catch (error) {
    console.error('Failed to update maintenance receipt order:', error);
    throw error;
  }
}

async function deleteMaintenanceReceiptOrderAPI(id: number) {
  try {
    const response = await fetch('/api/maintenance-receipts', {
      method: 'DELETE',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ id }),
    });

    if (!response.ok) {
      throw new Error('Failed to delete maintenance receipt order');
    }

    return true;
  } catch (error) {
    console.error('Failed to delete maintenance receipt order:', error);
    throw error;
  }
}

// وظائف تسليم أوامر الصيانة
async function addDeliveryOrderAPI(order: DeliveryOrder) {
  try {
    const response = await fetch('/api/delivery-orders', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(order),
    });

    if (!response.ok) {
      throw new Error('Failed to create delivery order');
    }

    return await response.json();
  } catch (error) {
    console.error('Failed to add delivery order:', error);
    throw error;
  }
}

async function updateDeliveryOrderAPI(order: DeliveryOrder) {
  try {
    const response = await fetch('/api/delivery-orders', {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(order),
    });

    if (!response.ok) {
      throw new Error('Failed to update delivery order');
    }

    return await response.json();
  } catch (error) {
    console.error('Failed to update delivery order:', error);
    throw error;
  }
}

async function deleteDeliveryOrderAPI(id: number) {
  try {
    const response = await fetch('/api/delivery-orders', {
      method: 'DELETE',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ id }),
    });

    if (!response.ok) {
      throw new Error('Failed to delete delivery order');
    }

    return true;
  } catch (error) {
    console.error('Failed to delete delivery order:', error);
    throw error;
  }
}

// تحديث ملف store.tsx
export async function updateStoreFile() {
  const storeFilePath = path.join(__dirname, 'context', 'store.tsx');
  
  // قراءة محتوى الملف
  let content = fs.readFileSync(storeFilePath, 'utf8');
  
  // إنشاء نسخة احتياطية
  fs.writeFileSync(`${storeFilePath}.backup-${Date.now()}`, content);
  
  // تحديث وظائف الصيانة
  content = updateMaintenanceOrderFunctions(content);
  
  // تحديث وظائف استلام الصيانة
  content = updateMaintenanceReceiptFunctions(content);
  
  // تحديث وظائف تسليم الصيانة
  content = updateDeliveryOrderFunctions(content);
  
  // كتابة المحتوى المحدث
  fs.writeFileSync(storeFilePath, content);
  
  console.log('تم تحديث الملف بنجاح!');
}

// تحديث وظائف أوامر الصيانة
function updateMaintenanceOrderFunctions(content: string): string {
  // وظيفة إضافة أمر صيانة
  content = content.replace(
    /const addMaintenanceOrder = async[^]*?(?=const updateMaintenanceOrder)/s,
    `const addMaintenanceOrder = async (
    order: Omit<MaintenanceOrder, "id" | "createdAt"> & {
      id?: number;
      status?: "wip" | "completed" | "draft";
    },
  ) => {
    try {
      // إذا لم يكن هناك رقم أمر، نقوم بإنشاء واحد
      if (!order.orderNumber) {
        const allExisting = [...maintenanceOrders];
        const useId =
          order.id && order.id > 0
            ? order.id
            : Math.max(0, ...allExisting.map((o) => o.id)) + 1;
        order.orderNumber = \`MAINT-\${useId}\`;
      }
      
      // إرسال الأمر إلى API
      const response = await fetch('/api/maintenance-orders', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...order,
          status: order.status || 'wip',
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create maintenance order');
      }

      // استقبال الأمر الذي تم إنشاؤه من API
      const newOrder = await response.json();

      // تحديث حالة التطبيق
      setMaintenanceOrders((prev) => [newOrder, ...prev]);

      // إضافة نشاط
      addActivity({
        type: "maintenance",
        description: \`تم إنشاء أمر صيانة \${newOrder.orderNumber}.\`,
      });
      
      return newOrder;
    } catch (error) {
      console.error('Failed to add maintenance order:', error);
      addActivity({
        type: "maintenance",
        description: \`⚠️ فشل في إنشاء أمر صيانة: \${error instanceof Error ? error.message : String(error)}\`,
      });
      throw error;
    }
  };

  `
  );
  
  // وظيفة تحديث أمر صيانة
  content = content.replace(
    /const updateMaintenanceOrder = [^]*?(?=const deleteMaintenanceOrder)/s,
    `const updateMaintenanceOrder = async (updatedOrder: MaintenanceOrder) => {
    try {
      // إرسال الأمر إلى API
      const response = await fetch('/api/maintenance-orders', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(updatedOrder),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update maintenance order');
      }

      // استقبال الأمر المحدّث من API
      const savedOrder = await response.json();

      // تحديث حالة التطبيق
      setMaintenanceOrders((prev) =>
        prev.map((o) =>
          o.id === savedOrder.id
            ? savedOrder
            : o
        ),
      );

      // تحديث حالة الأجهزة
      const originalOrder = maintenanceOrders.find(
        (o) => o.id === updatedOrder.id,
      );
      if (originalOrder) {
        const originalDeviceIds = new Set(originalOrder.items.map((i) => i.id));
        const updatedDeviceIds = new Set(updatedOrder.items.map((i) => i.id));

        const removedDeviceIds = [...originalDeviceIds].filter(
          (id) => !updatedDeviceIds.has(id),
        );
        setDevices((prev) =>
          prev.map((device) =>
            removedDeviceIds.includes(device.id)
              ? { ...device, status: "تحتاج صيانة" }
              : device,
          ),
        );
      }

      updatedOrder.items.forEach((item) => {
        updateDeviceStatus(item.id, "قيد الإصلاح");
      });

      // إضافة نشاط
      addActivity({
        type: "maintenance",
        description: \`تم تحديث أمر الصيانة \${updatedOrder.orderNumber}\`,
      });
      
      return savedOrder;
    } catch (error) {
      console.error('Failed to update maintenance order:', error);
      addActivity({
        type: "maintenance",
        description: \`⚠️ فشل في تحديث أمر صيانة: \${error instanceof Error ? error.message : String(error)}\`,
      });
      throw error;
    }
  };

  `
  );
  
  // وظيفة حذف أمر صيانة
  content = content.replace(
    /const deleteMaintenanceOrder = [^]*?(?=const addMaintenanceReceiptOrder)/s,
    `const deleteMaintenanceOrder = async (orderId: number) => {
    try {
      const orderToDelete = maintenanceOrders.find((o) => o.id === orderId);
      if (!orderToDelete) return;

      const relationCheck = checkMaintenanceOrderRelations(orderId);
      if (!relationCheck.canDelete) {
        throw new Error(
          \`لا يمكن حذف أمر الصيانة: \${relationCheck.reason}\${relationCheck.relatedOperations ? "\\nالعمليات المرتبطة: " + relationCheck.relatedOperations.join(", ") : ""}\`,
        );
      }

      // إرسال طلب الحذف إلى API
      const response = await fetch('/api/maintenance-orders', {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ id: orderId }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete maintenance order');
      }

      // تحديث حالة التطبيق
      setMaintenanceOrders((prev) => prev.filter((o) => o.id !== orderId));

      // إضافة نشاط
      addActivity({
        type: "maintenance",
        description: \`تم حذف أمر الصيانة \${orderToDelete.orderNumber}\`,
      });
    } catch (error) {
      console.error('Failed to delete maintenance order:', error);
      addActivity({
        type: "maintenance",
        description: \`⚠️ فشل في حذف أمر صيانة: \${error instanceof Error ? error.message : String(error)}\`,
      });
      throw error;
    }
  };

  `
  );
  
  return content;
}

// تحديث وظائف أوامر الاستلام
function updateMaintenanceReceiptFunctions(content: string): string {
  // وظيفة إضافة أمر استلام
  content = content.replace(
    /const addMaintenanceReceiptOrder = [^]*?(?=const updateMaintenanceReceiptOrder)/s,
    `const addMaintenanceReceiptOrder = async (
    order: Omit<MaintenanceReceiptOrder, "id" | "createdAt">,
  ) => {
    try {
      // إذا لم يكن هناك رقم للاستلام، نقوم بإنشاء واحد
      if (!order.receiptNumber) {
        const maxId = maintenanceReceiptOrders.reduce(
          (max, o) => (o.id > max ? o.id : max),
          0
        );
        order.receiptNumber = \`MREC-\${maxId + 1}\`;
      }
      
      // إرسال الأمر إلى API
      const response = await fetch('/api/maintenance-receipts', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(order),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create maintenance receipt order');
      }

      // استقبال الأمر الذي تم إنشاؤه من API
      const newOrder = await response.json();

      // تحديث حالة التطبيق
      setMaintenanceReceiptOrders((prev) => [newOrder, ...prev]);

      // إضافة نشاط
      addActivity({
        type: "maintenance",
        description: \`تم إنشاء أمر استلام جديد \${newOrder.receiptNumber} يحتوي على \${newOrder.items.length} جهاز\`,
      });
      
      return newOrder;
    } catch (error) {
      console.error('Failed to add maintenance receipt order:', error);
      addActivity({
        type: "maintenance",
        description: \`⚠️ فشل في إنشاء أمر استلام صيانة: \${error instanceof Error ? error.message : String(error)}\`,
      });
      throw error;
    }
  };

  `
  );
  
  // وظيفة تحديث أمر استلام
  content = content.replace(
    /const updateMaintenanceReceiptOrder = [^]*?(?=const deleteMaintenanceReceiptOrder)/s,
    `const updateMaintenanceReceiptOrder = async (
    updatedOrder: MaintenanceReceiptOrder,
  ) => {
    try {
      // إرسال الأمر إلى API
      const response = await fetch('/api/maintenance-receipts', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(updatedOrder),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update maintenance receipt order');
      }

      // استقبال الأمر المحدّث من API
      const savedOrder = await response.json();

      // تحديث حالة التطبيق
      setMaintenanceReceiptOrders((prev) =>
        prev.map((o) =>
          o.id === savedOrder.id
            ? savedOrder
            : o
        ),
      );

      // إضافة نشاط
      addActivity({
        type: "maintenance",
        description: \`تم تحديث أمر الاستلام \${updatedOrder.receiptNumber}\`,
      });
      
      return savedOrder;
    } catch (error) {
      console.error('Failed to update maintenance receipt order:', error);
      addActivity({
        type: "maintenance",
        description: \`⚠️ فشل في تحديث أمر استلام صيانة: \${error instanceof Error ? error.message : String(error)}\`,
      });
      throw error;
    }
  };

  `
  );
  
  // وظيفة حذف أمر استلام
  content = content.replace(
    /const deleteMaintenanceReceiptOrder = [^]*?(?=const addDeliveryOrder)/s,
    `const deleteMaintenanceReceiptOrder = async (orderId: number) => {
    try {
      const orderToDelete = maintenanceReceiptOrders.find((o) => o.id === orderId);
      if (!orderToDelete) return;

      // إرسال طلب الحذف إلى API
      const response = await fetch('/api/maintenance-receipts', {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ id: orderId }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete maintenance receipt order');
      }

      // تحديث حالة التطبيق
      setMaintenanceReceiptOrders((prev) => prev.filter((o) => o.id !== orderId));

      // إضافة نشاط
      addActivity({
        type: "maintenance",
        description: \`تم حذف أمر الاستلام \${orderToDelete.receiptNumber}\`,
      });
    } catch (error) {
      console.error('Failed to delete maintenance receipt order:', error);
      addActivity({
        type: "maintenance",
        description: \`⚠️ فشل في حذف أمر استلام صيانة: \${error instanceof Error ? error.message : String(error)}\`,
      });
      throw error;
    }
  };

  `
  );
  
  return content;
}

// تحديث وظائف أوامر التسليم
function updateDeliveryOrderFunctions(content: string): string {
  // وظيفة إضافة أمر تسليم
  content = content.replace(
    /const addDeliveryOrder = [^]*?(?=const updateDeliveryOrder)/s,
    `const addDeliveryOrder = async (
    order: Omit<DeliveryOrder, "id" | "createdAt">,
  ) => {
    try {
      // إذا لم يكن هناك رقم للتسليم، نقوم بإنشاء واحد
      if (!order.deliveryNumber) {
        const maxId = deliveryOrders.reduce(
          (max, o) => (o.id > max ? o.id : max),
          0
        );
        order.deliveryNumber = \`DEL-\${maxId + 1}\`;
      }
      
      // إرسال الأمر إلى API
      const response = await fetch('/api/delivery-orders', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(order),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create delivery order');
      }

      // استقبال الأمر الذي تم إنشاؤه من API
      const newOrder = await response.json();

      // تحديث حالة التطبيق
      setDeliveryOrders((prev) => [newOrder, ...prev]);

      // إضافة نشاط
      addActivity({
        type: "maintenance",
        description: \`تم إنشاء أمر تسليم جديد \${newOrder.deliveryNumber} يحتوي على \${newOrder.items.length} جهاز\`,
      });
      
      return newOrder;
    } catch (error) {
      console.error('Failed to add delivery order:', error);
      addActivity({
        type: "maintenance",
        description: \`⚠️ فشل في إنشاء أمر تسليم: \${error instanceof Error ? error.message : String(error)}\`,
      });
      throw error;
    }
  };

  `
  );
  
  // وظيفة تحديث أمر تسليم
  content = content.replace(
    /const updateDeliveryOrder = [^]*?(?=const deleteDeliveryOrder)/s,
    `const updateDeliveryOrder = async (updatedOrder: DeliveryOrder) => {
    try {
      // إرسال الأمر إلى API
      const response = await fetch('/api/delivery-orders', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(updatedOrder),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update delivery order');
      }

      // استقبال الأمر المحدّث من API
      const savedOrder = await response.json();

      // تحديث حالة التطبيق
      setDeliveryOrders((prev) =>
        prev.map((o) =>
          o.id === savedOrder.id
            ? savedOrder
            : o
        ),
      );

      // إضافة نشاط
      addActivity({
        type: "maintenance",
        description: \`تم تحديث أمر التسليم \${updatedOrder.deliveryNumber}\`,
      });
      
      return savedOrder;
    } catch (error) {
      console.error('Failed to update delivery order:', error);
      addActivity({
        type: "maintenance",
        description: \`⚠️ فشل في تحديث أمر تسليم: \${error instanceof Error ? error.message : String(error)}\`,
      });
      throw error;
    }
  };

  `
  );
  
  // وظيفة حذف أمر تسليم
  content = content.replace(
    /const deleteDeliveryOrder = [^]*?(?=const fetchData)/s,
    `const deleteDeliveryOrder = async (orderId: number) => {
    try {
      const orderToDelete = deliveryOrders.find((o) => o.id === orderId);
      if (!orderToDelete) return;

      // إرسال طلب الحذف إلى API
      const response = await fetch('/api/delivery-orders', {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ id: orderId }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete delivery order');
      }

      // تحديث حالة التطبيق
      setDeliveryOrders((prev) => prev.filter((o) => o.id !== orderId));

      // إضافة نشاط
      addActivity({
        type: "maintenance",
        description: \`تم حذف أمر التسليم \${orderToDelete.deliveryNumber}\`,
      });
    } catch (error) {
      console.error('Failed to delete delivery order:', error);
      addActivity({
        type: "maintenance",
        description: \`⚠️ فشل في حذف أمر تسليم: \${error instanceof Error ? error.message : String(error)}\`,
      });
      throw error;
    }
  };

  `
  );
  
  return content;
}

// تنفيذ التحديث عند استدعاء الملف مباشرة
if (require.main === module) {
  updateStoreFile()
    .then(() => {
      console.log('تم تحديث ملف store.tsx بنجاح!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('حدث خطأ أثناء تحديث الملف:', error);
      process.exit(1);
    });
}