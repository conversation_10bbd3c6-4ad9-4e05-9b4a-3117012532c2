# دليل استخدام Prisma لحفظ البيانات بشكل مستمر في التطبيق

## المقدمة

قمنا بتحويل نظام التطبيق من الاعتماد على الذاكرة المؤقتة (localStorage) إلى نظام قاعدة بيانات SQLite باستخدام Prisma ORM لضمان استمرارية البيانات بين جلسات التشغيل. هذا الدليل يشرح الطريقة التي تم تطبيقها والخطوات اللازمة لتطبيقها في أقسام أخرى من التطبيق.

## الخطوات الأساسية لإضافة وظيفة حفظ البيانات لقسم جديد

### 1. تعريف نموذج البيانات في Prisma

أول خطوة هي إضافة النموذج (Model) في ملف schema.prisma:

```prisma
model اسم_النموذج {
  id             Int      @id @default(autoincrement())
  // الحقول الأساسية للنموذج
  
  // الحقول التي تحتاج إلى تخزين كائنات JSON
  items          Json     // سيتم تخزين العناصر كـ JSON
  
  // الحقول الاختيارية
  notes          String?
  
  // التاريخ - يتم إنشاؤه تلقائيا
  createdAt      DateTime @default(now())
}
```

مثال من مشروعنا للمبيعات:

```prisma
model Sale {
  id             Int      @id @default(autoincrement())
  soNumber       String   @unique
  opNumber       String
  date           String
  clientName     String
  warehouseName  String
  items          Json     // سيتم تخزين العناصر كـ JSON
  notes          String?
  warrantyPeriod String
  employeeName   String
  createdAt      DateTime @default(now())
  attachments    String?
}
```

### 2. تحديث قاعدة البيانات

بعد إضافة النموذج، يجب تحديث قاعدة البيانات باستخدام:

```
npx prisma db push
npx prisma generate
```

هذا سيؤدي إلى إنشاء الجداول في قاعدة البيانات وتوليد أكواد TypeScript للتعامل مع النماذج.

### 3. إنشاء API Endpoints للقسم الجديد

نحتاج لإنشاء 4 وظائف رئيسية: GET, POST, PUT, DELETE

مثال لإنشاء مسار API جديد في المجلد route.ts:

```typescript
import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

export async function GET() {
  try {
    // استرجاع كل العناصر من قاعدة البيانات، مرتبة تنازلياً
    const items = await prisma.اسم_النموذج.findMany({
      orderBy: { id: 'desc' }
    });
    return NextResponse.json(items);
  } catch (error) {
    console.error('Failed to fetch items:', error);
    return NextResponse.json({ error: 'Failed to fetch items' }, { status: 500 });
  }
}

export async function POST(request: Request) {
  try {
    const newItem = await request.json();
    
    // البحث عن أعلى معرف لإنشاء رقم تسلسلي جديد
    const maxIdRecord = await prisma.اسم_النموذج.findFirst({
      orderBy: { id: 'desc' }
    });
    
    const newId = (maxIdRecord?.id || 0) + 1;
    
    // إنشاء العنصر في قاعدة البيانات
    const item = await prisma.اسم_النموذج.create({
      data: {
        رقم_تسلسلي: `PREFIX-${newId}`,
        // باقي البيانات
        // ...
        // لحقول JSON يجب التأكد من تحويلها بشكل صحيح
        عناصر_json: newItem.عناصر_json,
        // للحقول الاختيارية، إضافة قيمة افتراضية إذا كانت غير موجودة
        ملاحظات: newItem.ملاحظات || '',
      }
    });
    
    // تحديثات أخرى مرتبطة بالعنصر (مثل تحديث حالة الأجهزة في حالة المبيعات)
    
    return NextResponse.json(item, { status: 201 });
  } catch (error) {
    console.error('Failed to create item:', error);
    return NextResponse.json({ error: 'Failed to create item' }, { status: 500 });
  }
}

export async function PUT(request: Request) {
  try {
    const updatedItem = await request.json();
    
    // التحقق من وجود العنصر
    const existingItem = await prisma.اسم_النموذج.findUnique({
      where: { id: updatedItem.id }
    });
    
    if (!existingItem) {
      return NextResponse.json({ error: 'Item not found' }, { status: 404 });
    }
    
    // تحديث العنصر
    const item = await prisma.اسم_النموذج.update({
      where: { id: updatedItem.id },
      data: {
        // باقي البيانات
        // ...
        // لحقول JSON يجب التأكد من تحويلها بشكل صحيح
        عناصر_json: updatedItem.عناصر_json,
        // للحقول الاختيارية، إضافة قيمة افتراضية إذا كانت غير موجودة
        ملاحظات: updatedItem.ملاحظات || '',
      }
    });
    
    return NextResponse.json(item);
  } catch (error) {
    console.error('Failed to update item:', error);
    return NextResponse.json({ error: 'Failed to update item' }, { status: 500 });
  }
}

export async function DELETE(request: Request) {
  try {
    const { id } = await request.json();
    
    // التحقق من وجود العنصر
    const existingItem = await prisma.اسم_النموذج.findUnique({
      where: { id }
    });
    
    if (!existingItem) {
      return NextResponse.json({ error: 'Item not found' }, { status: 404 });
    }
    
    // تحديثات مرتبطة قبل الحذف (مثل تحديث حالة الأجهزة في حالة المبيعات)
    
    // حذف العنصر
    await prisma.اسم_النموذج.delete({
      where: { id }
    });
    
    return NextResponse.json({ message: 'Item deleted successfully' });
  } catch (error) {
    console.error('Failed to delete item:', error);
    return NextResponse.json({ error: 'Failed to delete item' }, { status: 500 });
  }
}
```

### 4. تحديث المخزن المركزي (Context Store)

يجب تعديل ملف store.tsx لاستخدام API بدلاً من التخزين المؤقت:

#### إضافة حالة ووظائف تحميل البيانات:

```typescript
// إضافة حالة لتخزين البيانات
const [items, setItems] = useState<Item[]>([]);

// تحميل البيانات من API عند التهيئة
useEffect(() => {
  if (typeof window !== 'undefined') {
    loadDataFromAPIs();
  }
}, []);

// دالة لتحميل البيانات
const loadDataFromAPIs = async () => {
  try {
    setIsLoading(true);
    
    // تحميل العناصر
    try {
      console.log("جاري تحميل البيانات...");
      const response = await fetch('/api/اسم_القسم');
      if (response.ok) {
        const data = await response.json();
        if (Array.isArray(data) && data.length > 0) {
          setItems(data);
          console.log(`تم تحميل ${data.length} عنصر`);
        } else {
          console.warn('لم يتم العثور على عناصر');
        }
      } else {
        console.warn('فشل في تحميل البيانات');
      }
    } catch (error) {
      console.warn('فشل في تحميل البيانات:', error);
    }
    
    setIsLoading(false);
  } catch (error) {
    console.error('فشل في تحميل بيانات التطبيق:', error);
    setIsLoading(false);
  }
};
```

#### تحديث وظائف الإضافة والتحديث والحذف:

```typescript
// إضافة عنصر جديد
const addItem = async (item: Omit<Item, 'id' | 'رقم_تسلسلي'>) => {
  try {
    const response = await fetch('/api/اسم_القسم', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(item),
    });

    if (!response.ok) {
      throw new Error('Failed to create item');
    }

    const newItem = await response.json();
    setItems((prev) => [newItem, ...prev].sort((a, b) => b.id - a.id));

    // تحديثات أخرى مرتبطة بالعنصر (مثل تحديث حالة الأجهزة في حالة المبيعات)

    addActivity({
      type: 'نوع_النشاط',
      description: `تم إنشاء عنصر جديد`,
    });
  } catch (error) {
    console.error('Failed to add item:', error);
    throw error;
  }
};

// تحديث عنصر موجود
const updateItem = async (updatedItem: Item) => {
  try {
    const response = await fetch('/api/اسم_القسم', {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(updatedItem),
    });

    if (!response.ok) {
      throw new Error('Failed to update item');
    }

    const item = await response.json();
    setItems((prev) => prev.map((i) => (i.id === item.id ? item : i)));

    addActivity({
      type: 'نوع_النشاط',
      description: `تم تحديث العنصر`,
    });
  } catch (error) {
    console.error('Failed to update item:', error);
    throw error;
  }
};

// حذف عنصر
const deleteItem = async (itemId: number) => {
  try {
    const response = await fetch('/api/اسم_القسم', {
      method: 'DELETE',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ id: itemId }),
    });

    if (!response.ok) {
      throw new Error('Failed to delete item');
    }

    setItems((prev) => prev.filter((i) => i.id !== itemId));

    addActivity({
      type: 'نوع_النشاط',
      description: `تم حذف العنصر`,
    });
  } catch (error) {
    console.error('Failed to delete item:', error);
    throw error;
  }
};
```

## نصائح مهمة للتطبيق

1. **التعامل مع حقول JSON**: تأكد من أن الحقول التي تحتاج إلى تخزين كائنات معقدة مثل المصفوفات يتم تعريفها كـ `Json` في Prisma.

2. **تحويل البيانات**: في بعض الحالات، قد تحتاج إلى تحويل البيانات بين النموذج وواجهة المستخدم، مثل تحويل المرفقات:
   ```typescript
   attachments: newItem.attachments ? JSON.stringify(newItem.attachments) : null
   ```

3. **إنشاء أرقام تسلسلية**: استخدم دائمًا أعلى معرف موجود + 1 لإنشاء أرقام تسلسلية فريدة:
   ```typescript
   const maxIdRecord = await prisma.اسم_النموذج.findFirst({
     orderBy: { id: 'desc' }
   });
   const newId = (maxIdRecord?.id || 0) + 1;
   ```

4. **التعامل مع الأخطاء**: استخدم try/catch وسجل الأخطاء بشكل واضح لتسهيل التصحيح:
   ```typescript
   try {
     // العمليات
   } catch (error) {
     console.error('رسالة خطأ واضحة:', error);
     throw error;  // رمي الخطأ للتعامل معه في الواجهة
   }
   ```

5. **الترتيب**: استخدم الترتيب التنازلي حسب المعرف لعرض العناصر الأحدث أولاً:
   ```typescript
   orderBy: { id: 'desc' }
   ```

6. **مؤشرات التحميل**: استخدم حالة `isLoading` لإظهار مؤشر التحميل في واجهة المستخدم:
   ```typescript
   setIsLoading(true);
   // العمليات
   setIsLoading(false);
   ```

## مثال: المبيعات

لفهم التطبيق العملي، يمكننا مراجعة كيفية تنفيذ المبيعات:

### 1. نموذج Prisma للمبيعات

```prisma
model Sale {
  id             Int      @id @default(autoincrement())
  soNumber       String   @unique
  opNumber       String
  date           String
  clientName     String
  warehouseName  String
  items          Json     // سيتم تخزين العناصر كـ JSON
  notes          String?
  warrantyPeriod String
  employeeName   String
  createdAt      DateTime @default(now())
  attachments    String?
}
```

### 2. API للمبيعات

```typescript
// app/api/sales/route.ts
export async function POST(request: Request) {
  try {
    const newSale = await request.json();
    
    // Find the highest id to generate a new soNumber
    const maxIdRecord = await prisma.sale.findFirst({
      orderBy: { id: 'desc' }
    });
    
    const newId = (maxIdRecord?.id || 0) + 1;
    
    // Create the sale in the database
    const sale = await prisma.sale.create({
      data: {
        soNumber: `SO-${newId}`,
        opNumber: newSale.opNumber,
        date: newSale.date,
        clientName: newSale.clientName,
        warehouseName: newSale.warehouseName,
        items: newSale.items, // JSON field
        notes: newSale.notes || '',
        warrantyPeriod: newSale.warrantyPeriod,
        employeeName: newSale.employeeName,
        attachments: newSale.attachments ? JSON.stringify(newSale.attachments) : null
      }
    });
    
    // Update device statuses
    if (newSale.items && Array.isArray(newSale.items)) {
      for (const item of newSale.items) {
        try {
          await prisma.device.update({
            where: { id: item.deviceId },
            data: { status: 'مباع' }
          });
        } catch (deviceError) {
          console.error(`Failed to update device ${item.deviceId}:`, deviceError);
        }
      }
    }
    
    return NextResponse.json(sale, { status: 201 });
  } catch (error) {
    console.error('Failed to create sale:', error);
    return NextResponse.json({ error: 'Failed to create sale' }, { status: 500 });
  }
}
```

### 3. وظيفة إضافة المبيعات في المخزن المركزي:

```typescript
const addSale = async (sale: Omit<Sale, 'id' | 'soNumber' | 'createdAt'>) => {
  try {
    const response = await fetch('/api/sales', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(sale),
    });

    if (!response.ok) {
      throw new Error('Failed to create sale');
    }

    const newSale = await response.json();
    setSales((prev) => [newSale, ...prev].sort((a, b) => b.id - a.id));

    // Update device statuses
    newSale.items.forEach((item: any) => {
      updateDeviceStatus(item.deviceId, 'مباع');
    });

    addActivity({
      type: 'sale',
      description: `تم بيع ${newSale.items.length} أجهزة للعميل ${newSale.clientName}`,
    });
  } catch (error) {
    console.error('Failed to add sale:', error);
    throw error;
  }
};
```

## خلاصة

1. **التصميم**: حدد نموذج البيانات ثم أضفه في Prisma schema
2. **قاعدة البيانات**: قم بتحديث قاعدة البيانات باستخدام أوامر Prisma
3. **API**: أنشئ وظائف API للعمليات الأساسية (GET, POST, PUT, DELETE)
4. **المخزن المركزي**: حدّث المخزن المركزي لاستخدام API بدلاً من التخزين المحلي
5. **الواجهة**: تأكد من أن واجهة المستخدم تستخدم الوظائف المحدثة وتعرض مؤشرات التحميل المناسبة

باتباع هذه الطريقة، يمكنك تحويل أي قسم في التطبيق للعمل بشكل متكامل مع قاعدة بيانات Prisma وضمان استمرارية البيانات بين جلسات التشغيل.
