// Script للتحديث السريع لوظيفة addMaintenanceOrder في context/store.tsx
// نسخ احتياطية قبل التشغيل

const fs = require('fs');
const path = require('path');

const filePath = 'context/store.tsx';

// قراءة الملف
let content = fs.readFileSync(filePath, 'utf8');

// البحث عن بداية الوظيفة
const functionStart = 'const addMaintenanceOrder = async (';
const functionStartIndex = content.indexOf(functionStart);

if (functionStartIndex === -1) {
  console.log('لا يمكن العثور على وظيفة addMaintenanceOrder');
  process.exit(1);
}

// البحث عن نهاية الوظيفة - أول }; بعد const updateMaintenanceOrder
const nextFunctionStart = content.indexOf('const updateMaintenanceOrder', functionStartIndex);
const functionEndIndex = content.lastIndexOf('};', nextFunctionStart);

if (functionEndIndex === -1) {
  console.log('لا يمكن العثور على نهاية الوظيفة');
  process.exit(1);
}

// الوظيفة الجديدة
const newFunction = `const addMaintenanceOrder = async (
    order: Omit<MaintenanceOrder, "id" | "createdAt"> & {
      id?: number;
      status?: "wip" | "completed" | "draft";
    },
  ) => {
    try {
      // إذا لم يكن هناك رقم أمر، نقوم بإنشاء واحد
      if (!order.orderNumber) {
        const allExisting = [...maintenanceOrders];
        const useId =
          order.id && order.id > 0
            ? order.id
            : Math.max(0, ...allExisting.map((o) => o.id)) + 1;
        order.orderNumber = \`MAINT-\${useId}\`;
      }
      
      // إرسال الأمر إلى API
      const response = await fetch('/api/maintenance-orders', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...order,
          status: order.status || 'wip',
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create maintenance order');
      }

      // استقبال الأمر الذي تم إنشاؤه من API
      const newOrder = await response.json();

      // تحديث حالة التطبيق
      setMaintenanceOrders((prev) => [newOrder, ...prev]);

      // إضافة نشاط
      addActivity({
        type: "maintenance",
        description: \`تم إنشاء أمر صيانة \${newOrder.orderNumber}.\`,
      });
      
      return newOrder;
    } catch (error) {
      console.error('Failed to add maintenance order:', error);
      addActivity({
        type: "maintenance",
        description: \`⚠️ فشل في إنشاء أمر صيانة: \${error instanceof Error ? error.message : String(error)}\`,
      });
      throw error;
    }
  };`;

// استبدال الوظيفة
const beforeFunction = content.substring(0, functionStartIndex);
const afterFunction = content.substring(functionEndIndex + 2);

const newContent = beforeFunction + newFunction + '\n\n  ' + afterFunction;

// كتابة الملف الجديد
fs.writeFileSync(filePath, newContent, 'utf8');

console.log('تم تحديث وظيفة addMaintenanceOrder بنجاح!');
console.log('الآن صفحة إرسال الصيانة ستحتفظ بالبيانات في قاعدة البيانات.');
