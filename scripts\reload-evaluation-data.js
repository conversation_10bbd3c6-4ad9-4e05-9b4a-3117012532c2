// سكريبت لإعادة تحميل بيانات أوامر التقييم من قاعدة البيانات
// يمكن تشغيله من المتصفح في وحدة التحكم

async function reloadEvaluationData() {
  try {
    console.log('🔄 بدء إعادة تحميل بيانات أوامر التقييم...');
    
    // تحميل أوامر التقييم من API
    const response = await fetch('/api/evaluations');
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    const evaluations = await response.json();
    
    console.log('✅ تم تحميل أوامر التقييم بنجاح:', evaluations.length);
    console.log('📊 أوامر التقييم:', evaluations);
    
    // عرض إحصائيات
    const orderIds = evaluations.map(e => e.orderId);
    const uniqueOrderIds = [...new Set(orderIds)];
    const duplicates = orderIds.length - uniqueOrderIds.length;
    
    console.log(`📈 إحصائيات:
    - إجمالي الأوامر: ${evaluations.length}
    - أرقام فريدة: ${uniqueOrderIds.length}
    - أوامر مكررة: ${duplicates}
    `);
    
    if (duplicates > 0) {
      console.warn('⚠️ تم العثور على أوامر مكررة! يُنصح بتنظيف قاعدة البيانات.');
      
      // عرض الأوامر المكررة
      const duplicateGroups = {};
      orderIds.forEach(orderId => {
        duplicateGroups[orderId] = (duplicateGroups[orderId] || 0) + 1;
      });
      
      const duplicateOrderIds = Object.keys(duplicateGroups).filter(
        orderId => duplicateGroups[orderId] > 1
      );
      
      console.log('🔍 الأوامر المكررة:', duplicateOrderIds);
    }
    
    return evaluations;
    
  } catch (error) {
    console.error('❌ خطأ في تحميل بيانات أوامر التقييم:', error);
    throw error;
  }
}

async function cleanupDuplicateEvaluations() {
  try {
    console.log('🧹 بدء تنظيف الأوامر المكررة...');
    
    const response = await fetch('/api/evaluations/cleanup', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ action: 'remove_duplicates' })
    });
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    const result = await response.json();
    
    console.log('✅ تم تنظيف الأوامر المكررة بنجاح:', result);
    
    return result;
    
  } catch (error) {
    console.error('❌ خطأ في تنظيف الأوامر المكررة:', error);
    throw error;
  }
}

async function listDuplicateEvaluations() {
  try {
    console.log('🔍 البحث عن الأوامر المكررة...');
    
    const response = await fetch('/api/evaluations/cleanup', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ action: 'list_duplicates' })
    });
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    const result = await response.json();
    
    console.log('📋 الأوامر المكررة:', result);
    
    return result;
    
  } catch (error) {
    console.error('❌ خطأ في البحث عن الأوامر المكررة:', error);
    throw error;
  }
}

// تصدير الدوال للاستخدام في وحدة التحكم
if (typeof window !== 'undefined') {
  window.reloadEvaluationData = reloadEvaluationData;
  window.cleanupDuplicateEvaluations = cleanupDuplicateEvaluations;
  window.listDuplicateEvaluations = listDuplicateEvaluations;
  
  console.log(`
🛠️ أدوات إدارة أوامر التقييم متاحة:

1. reloadEvaluationData() - إعادة تحميل البيانات
2. listDuplicateEvaluations() - عرض الأوامر المكررة  
3. cleanupDuplicateEvaluations() - تنظيف الأوامر المكررة

مثال:
await reloadEvaluationData();
await listDuplicateEvaluations();
await cleanupDuplicateEvaluations();
  `);
}

// تشغيل تلقائي عند تحميل السكريبت
if (typeof window !== 'undefined') {
  reloadEvaluationData().catch(console.error);
}
