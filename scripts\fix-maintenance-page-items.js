const fs = require('fs');

function fixMaintenancePageItems() {
  console.log('🔧 إصلاح مشاكل items في صفحة maintenance...');

  const filePath = 'app/(main)/maintenance/page.tsx';
  
  if (!fs.existsSync(filePath)) {
    console.log('❌ الملف غير موجود');
    return false;
  }

  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let hasChanges = false;

    // إصلاح جميع استخدامات o.items.some
    const itemsSomePattern = /(\w+)\.items\.some\(/g;
    const matches = content.match(itemsSomePattern);
    if (matches) {
      content = content.replace(itemsSomePattern, (match, varName) => {
        return `Array.isArray(${varName}.items) && ${varName}.items.some(`;
      });
      hasChanges = true;
      console.log(`✅ تم إصلاح ${matches.length} استخدام لـ .items.some`);
    }

    // إصلاح جميع استخدامات o.items.find
    const itemsFindPattern = /(\w+)\.items\.find\(/g;
    const findMatches = content.match(itemsFindPattern);
    if (findMatches) {
      content = content.replace(itemsFindPattern, (match, varName) => {
        return `Array.isArray(${varName}.items) && ${varName}.items.find(`;
      });
      hasChanges = true;
      console.log(`✅ تم إصلاح ${findMatches.length} استخدام لـ .items.find`);
    }

    // إصلاح جميع استخدامات o.items.filter
    const itemsFilterPattern = /(\w+)\.items\.filter\(/g;
    const filterMatches = content.match(itemsFilterPattern);
    if (filterMatches) {
      content = content.replace(itemsFilterPattern, (match, varName) => {
        return `Array.isArray(${varName}.items) && ${varName}.items.filter(`;
      });
      hasChanges = true;
      console.log(`✅ تم إصلاح ${filterMatches.length} استخدام لـ .items.filter`);
    }

    // إصلاح الحالات المعقدة التي تحتاج معالجة خاصة
    const complexPatterns = [
      {
        search: /Array\.isArray\((\w+)\.items\) && Array\.isArray\((\w+)\.items\) && (\w+)\.items\.some\(/g,
        replace: 'Array.isArray($1.items) && $1.items.some('
      },
      {
        search: /Array\.isArray\((\w+)\.items\) && Array\.isArray\((\w+)\.items\) && (\w+)\.items\.find\(/g,
        replace: 'Array.isArray($1.items) && $1.items.find('
      }
    ];

    complexPatterns.forEach(pattern => {
      if (pattern.search.test(content)) {
        content = content.replace(pattern.search, pattern.replace);
        hasChanges = true;
        console.log('✅ تم إصلاح نمط معقد');
      }
    });

    if (hasChanges) {
      fs.writeFileSync(filePath, content);
      console.log('✅ تم حفظ التغييرات');
      return true;
    } else {
      console.log('ℹ️ لا توجد تغييرات مطلوبة');
      return true;
    }

  } catch (error) {
    console.error('❌ خطأ في إصلاح الملف:', error.message);
    return false;
  }
}

if (require.main === module) {
  const success = fixMaintenancePageItems();
  process.exit(success ? 0 : 1);
}

module.exports = { fixMaintenancePageItems };
