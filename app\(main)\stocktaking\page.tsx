
// Required API endpoints:
// - /api/devices
// - /api/warehouses
// - /api/stocktakes
'use client';

import { useState, useMemo, useRef, useEffect } from 'react';
// Updated to use APIs
import { useToast } from '@/hooks/use-toast';
import './enhanced-styles.css';
import type {
  Device,
  SystemSettings,
  StocktakeDraft,
  StocktakeHistory,
  Warehouse,
} from '@/lib/types';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from '@/components/ui/dialog';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import {
  ClipboardList,
  Upload,
  FileDown,
  Printer,
  FileSpreadsheet,
  Check,
  X,
  AlertTriangle,
  PackageSearch,
  Save,
  FolderOpen,
  Trash,
  Trash2,
  ClipboardPaste,
  PlusCircle,
  Eye,
  Package,
  ShoppingCart,
  Wrench,
  Search,
  ChevronsUpDown,
  Edit,
  Calendar,
  User,
  Building,
  Smartphone,
  BarChart3,
  TrendingUp,
  Clock,
  CheckCircle2,
  XCircle,
  AlertCircle,
  Info,
  Layers,
  ScanLine,
} from 'lucide-react';
import { DarkModeToggle } from './DarkModeToggle';
import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable';
import * as XLSX from 'xlsx';
import {
  Command,
  CommandDialog,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import { cn } from '@/lib/utils';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';

type StocktakeResultList = {
  deviceId: string;
  model: string;
  status: string;
  warehouseName?: string;
  note?: string;
};

type StocktakeResults = {
  expectedCount: number;
  matching: StocktakeResultList[];
  missing: StocktakeResultList[];
  extra: StocktakeResultList[];
  soldButFound: StocktakeResultList[];
  inMaintenance: StocktakeResultList[];
};

const initialResultsState: StocktakeResults = {
  expectedCount: 0,
  matching: [],
  missing: [],
  extra: [],
  soldButFound: [],
  inMaintenance: [],
};

type ResultDetails = {
  title: string;
  items: StocktakeResultList[];
};

// Component for the second-level details dialog (List of Devices)
const DeviceListDialog = ({
  isOpen,
  onClose,
  details,
}: {
  isOpen: boolean;
  onClose: () => void;
  details: ResultDetails | null;
}) => {
    // State management
  const [isLoading, setIsLoading] = useState(true);
  const [devices, setDevices] = useState([]);
  const [warehouses, setWarehouses] = useState([]);

  // TODO: Add API functions here

  const handleExport = (format: 'pdf' | 'excel') => {
    if (!details) return;

    if (format === 'pdf') {
      const doc = new jsPDF();
      doc.setR2L(true);
      doc.setFont('Amiri-Regular');
      doc.text(details.title, 105, 15, { align: 'center' });
      autoTable(doc, {
        startY: 25,
        head: [['الملاحظة', 'الحالة', 'الموديل', 'الرقم التسلسلي']],
        body: (Array.isArray(details.items) ? details.items : []).map((item) => [
          item.note || '-',
          item.status,
          item.model,
          item.deviceId,
        ]),
        styles: { font: 'Amiri-Regular', halign: 'right' },
      });
      doc.save(`${details.title.replace(/\s+/g, '_')}.pdf`);
    } else {
      const dataToExport = (Array.isArray(details.items) ? details.items : []).map((item) => ({
        'الرقم التسلسلي': item.deviceId,
        الموديل: item.model,
        الحالة: item.status,
        المخزن: item.warehouseName,
        الملاحظة: item.note,
      }));
      const worksheet = XLSX.utils.json_to_sheet(dataToExport);
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Devices');
      XLSX.writeFile(
        workbook,
        `${details.title.replace(/\s+/g, '_')}.xlsx`
    );
    }
  };

  const handlePrint = () => {
    if (!details) return;
    const doc = new jsPDF();
    doc.setR2L(true);
    doc.setFont('Amiri-Regular');
    doc.text(details.title, 105, 15, { align: 'center' });
    autoTable(doc, {
      startY: 25,
      head: [['الملاحظة', 'الحالة', 'الموديل', 'الرقم التسلسلي']],
      body: (Array.isArray(details.items) ? details.items : []).map((item) => [
        item.note || '-',
        item.status,
        item.model,
        item.deviceId,
      ]),
      styles: { font: 'Amiri-Regular', halign: 'right' },
      headStyles: { halign: 'center', fillColor: [44, 51, 51] },
    });
    doc.autoPrint();
    doc.output('dataurlnewwindow');
  };

  if (!details) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="enhanced-dialog sm:max-w-4xl">
        <DialogHeader className="enhanced-dialog-header">
          <DialogTitle className="enhanced-dialog-title flex items-center space-x-2 space-x-reverse">
            <Package className="h-5 w-5 text-primary" />
            <span>{details.title}</span>
          </DialogTitle>
          <DialogDescription className="enhanced-dialog-description">
            قائمة تفصيلية بالأجهزة مع معلوماتها الكاملة
          </DialogDescription>
        </DialogHeader>
        <div className="enhanced-scroll-area p-4">
          <Table className="enhanced-modal-table">
            <TableHeader>
              <TableRow>
                <TableHead className="w-16">#</TableHead>
                <TableHead>الرقم التسلسلي</TableHead>
                <TableHead>الموديل</TableHead>
                <TableHead>الحالة</TableHead>
                <TableHead>المخزن</TableHead>
                <TableHead>ملاحظة</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {(Array.isArray(details.items) ? details.items : []).map((item, index) => (
                <TableRow key={item.deviceId}>
                  <TableCell className="font-medium text-center text-gray-500">
                    {index + 1}
                  </TableCell>
                  <TableCell className="font-mono" dir="ltr">{item.deviceId}</TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2 space-x-reverse">
                      <Smartphone className="h-4 w-4 text-gray-500" />
                      <span>{item.model}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline" className="enhanced-badge">
                      {item.status}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2 space-x-reverse">
                      <Building className="h-4 w-4 text-gray-500" />
                      <span>{item.warehouseName || '-'}</span>
                    </div>
                  </TableCell>
                  <TableCell className="text-sm text-gray-600">
                    {item.note || '-'}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
        <DialogFooter className="justify-between">
          <div className="flex gap-2">
            <Button variant="outline" onClick={() => handleExport('pdf')}>
              <FileDown className="ml-2 h-4 w-4" /> PDF
            </Button>
            <Button variant="outline" onClick={() => handleExport('excel')}>
              <FileSpreadsheet className="ml-2 h-4 w-4" /> Excel
            </Button>
            <Button variant="outline" onClick={handlePrint}>
              <Printer className="ml-2 h-4 w-4" /> طباعة
            </Button>
          </div>
          <DialogClose asChild>
            <Button variant="outline">إغلاق</Button>
          </DialogClose>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

// Component for the first-level details dialog (Grouped by Model)
const ResultSummaryDialog = ({
  isOpen,
  onClose,
  details,
}: {
  isOpen: boolean;
  onClose: () => void;
  details: ResultDetails | null;
}) => {
  const [deviceListDetails, setDeviceListDetails] = useState<{
    title: string;
    items: StocktakeResultList[];
  } | null>(null);

  // systemSettings will be passed as prop from parent component

  const groupedData = useMemo(() => {
    if (!details) return [];
    return Object.values(
      details.items.reduce(
        (acc, item) => {
          const key = `${item.model}-${item.warehouseName || 'N/A'}`;
          if (!acc[key]) {
            acc[key] = {
              model: item.model,
              warehouseName: item.warehouseName,
              count: 0,
              items: [],
            };
          }
          acc[key].count++;
          acc[key].items.push(item);
          return acc;
        },
        {} as Record<
          string,
          {
            model: string;
            warehouseName?: string;
            count: number;
            items: StocktakeResultList[];
          }
        >,
      )
    );
  }, [details]);

  const handleExport = (format: 'pdf' | 'excel') => {
    if (!details) return;

    if (format === 'pdf') {
      const doc = new jsPDF();
      doc.setR2L(true);
      doc.setFont('Amiri-Regular');
      doc.text(details.title, 105, 15, { align: 'center' });
      autoTable(doc, {
        startY: 25,
        head: [['الموديل', 'المخزن', 'العدد']],
        body: groupedData.map((item) => [
          item.model,
          item.warehouseName || 'غير محدد',
          item.count,
        ]),
        styles: { font: 'Amiri-Regular', halign: 'right' },
        headStyles: { halign: 'center', fillColor: [44, 51, 51] },
      });
      doc.save(`${details.title.replace(/\s+/g, '_')}_summary.pdf`);
    } else {
      const dataToExport = groupedData.map((item) => ({
        الموديل: item.model,
        المخزن: item.warehouseName || 'غير محدد',
        العدد: item.count,
      }));
      const worksheet = XLSX.utils.json_to_sheet(dataToExport);
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Summary');
      XLSX.writeFile(
        workbook,
        `${details.title.replace(/\s+/g, '_')}_summary.xlsx`
    );
    }
  };

  const handlePrint = () => {
    if (!details) return;
    const doc = new jsPDF();
    doc.setR2L(true);
    doc.setFont('Amiri-Regular');
    doc.text(details.title, 105, 15, { align: 'center' });
    autoTable(doc, {
      startY: 25,
      head: [['الموديل', 'المخزن', 'العدد']],
      body: groupedData.map((item) => [
        item.model,
        item.warehouseName || 'غير محدد',
        item.count,
      ]),
      styles: { font: 'Amiri-Regular', halign: 'right' },
      headStyles: { halign: 'center', fillColor: [44, 51, 51] },
    });
    doc.autoPrint();
    doc.output('dataurlnewwindow');
  };

  if (!details) return null;

  return (
    <>
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="enhanced-dialog sm:max-w-4xl">
          <DialogHeader className="enhanced-dialog-header">
            <DialogTitle className="enhanced-dialog-title flex items-center space-x-2 space-x-reverse">
              <BarChart3 className="h-5 w-5 text-primary" />
              <span>{details.title}</span>
            </DialogTitle>
            <DialogDescription className="enhanced-dialog-description">
              ملخص الأجهزة مجمعة حسب الموديل والمخزن مع إمكانية عرض التفاصيل
            </DialogDescription>
          </DialogHeader>
          <div className="enhanced-scroll-area p-4">
            <Table className="enhanced-modal-table">
              <TableHeader>
                <TableRow>
                  <TableHead className="w-16">#</TableHead>
                  <TableHead>الموديل</TableHead>
                  <TableHead>المخزن</TableHead>
                  <TableHead className="text-center">العدد</TableHead>
                  <TableHead className="text-center">الإجراءات</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {groupedData.length > 0 ? (
                  groupedData.map((group, index) => (
                    <TableRow
                      key={`${group.model}-${group.warehouseName || 'N/A'}`}
                    >
                      <TableCell className="font-medium text-center text-gray-500">
                        {index + 1}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2 space-x-reverse">
                          <Smartphone className="h-4 w-4 text-gray-500" />
                          <span className="font-medium">{group.model}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2 space-x-reverse">
                          <Building className="h-4 w-4 text-gray-500" />
                          <span>{group.warehouseName || 'غير محدد'}</span>
                        </div>
                      </TableCell>
                      <TableCell className="text-center">
                        <div className="enhanced-badge bg-blue-500/10 text-blue-600 border-blue-500/20">
                          {group.count}
                        </div>
                      </TableCell>
                      <TableCell className="text-center">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() =>
                            setDeviceListDetails({
                              title: `تفاصيل ${group.model} - ${details.title}`,
                              items: group.items,
                            })
                          }
                          className="enhanced-modal-button hover:bg-primary/10"
                          title="عرض تفاصيل الأجهزة"
                        >
                          <Eye className="h-4 w-4 mr-2" /> عرض التفاصيل
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={5} className="h-32 text-center">
                      <div className="flex flex-col items-center space-y-3 text-muted-foreground">
                        <BarChart3 className="h-12 w-12" />
                        <p>لا توجد بيانات لعرضها.</p>
                      </div>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
          <DialogFooter className="justify-between">
            <div className="flex gap-2">
              <Button variant="outline" onClick={() => handleExport('pdf')}>
                <FileDown className="ml-2 h-4 w-4" /> PDF
              </Button>
              <Button variant="outline" onClick={() => handleExport('excel')}>
                <FileSpreadsheet className="ml-2 h-4 w-4" /> Excel
              </Button>
              <Button variant="outline" onClick={handlePrint}>
                <Printer className="ml-2 h-4 w-4" /> طباعة
              </Button>
            </div>
            <DialogClose asChild>
              <Button variant="outline">إغلاق</Button>
            </DialogClose>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      {deviceListDetails && (
        <DeviceListDialog
          isOpen={!!deviceListDetails}
          onClose={() => setDeviceListDetails(null)}
          details={deviceListDetails}
        />
      )}
    </>
  );
};

// Component for the new summary modal
const CumulativeSummaryDialog = ({
  isOpen,
  onClose,
  results,
  processedSerials,
  devices,
  warehouses,
}: {
  isOpen: boolean;
  onClose: () => void;
  results: StocktakeResults | null;
  processedSerials: Set<string>;
  devices: Device[];
  warehouses: Warehouse[];
}) => {
  const summaryByModelAndWarehouse = useMemo(() => {
    if (!results) return [];

    const summaryMap: Record<
      string,
      {
        model: string;
        warehouseName: string;
        total: number;
        entered: number;
        missing: number;
      }
    > = {};

    const expectedDevicesInScope = devices.filter(
      (d) =>
        results.matching.some((m) => m.deviceId === d.id) ||
        results.missing.some((m) => m.deviceId === d.id)
    );

    // Initialize with total expected devices
    expectedDevicesInScope.forEach((device) => {
      const warehouseName =
        warehouses.find((w) => w.id === device.warehouseId)?.name || 'غير محدد';
      const key = `${device.model}-${warehouseName}`;

      if (!summaryMap[key]) {
        summaryMap[key] = {
          model: device.model,
          warehouseName,
          total: 0,
          entered: 0,
          missing: 0,
        };
      }
      summaryMap[key].total++;
    });

    // Count entered devices
    processedSerials.forEach((serial) => {
      const device = devices.find((d) => d.id === serial);
      if (device && expectedDevicesInScope.some((d) => d.id === serial)) {
        const warehouseName =
          warehouses.find((w) => w.id === device.warehouseId)?.name ||
          'غير محدد';
        const key = `${device.model}-${warehouseName}`;
        if (summaryMap[key]) {
          summaryMap[key].entered++;
        }
      }
    });

    // Calculate missing
    Object.values(summaryMap).forEach((summary) => {
      summary.missing = summary.total - summary.entered;
    });

    return Object.values(summaryMap);
  }, [results, processedSerials, devices, warehouses]);

  const handleExport = (format: 'pdf' | 'excel') => {
    if (!summaryByModelAndWarehouse.length) return;

    if (format === 'pdf') {
      const doc = new jsPDF();
      doc.setR2L(true);
      doc.setFont('Amiri-Regular');
      doc.text('ملخص الجرد التراكمي', 105, 15, { align: 'center' });
      autoTable(doc, {
        startY: 25,
        head: [
          [
            'الموديل',
            'المخزن',
            'إجمالي المخزون',
            'الأجهزة المدخلة',
            'الأجهزة المفقودة',
          ],
        ],
        body: summaryByModelAndWarehouse.map((item) => [
          item.model,
          item.warehouseName,
          item.total,
          item.entered,
          item.missing,
        ]),
        styles: { font: 'Amiri-Regular', halign: 'right' },
        headStyles: { halign: 'center', fillColor: [44, 51, 51] },
      });
      doc.save('cumulative_stocktake_summary.pdf');
    } else {
      const dataToExport = summaryByModelAndWarehouse.map((item) => ({
        الموديل: item.model,
        المخزن: item.warehouseName,
        'إجمالي المخزون': item.total,
        'الأجهزة المدخلة': item.entered,
        'الأجهزة المفقودة': item.missing,
      }));
      const worksheet = XLSX.utils.json_to_sheet(dataToExport);
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, 'ملخص الجرد');
      XLSX.writeFile(workbook, 'cumulative_stocktake_summary.xlsx');
    }
  };

  const handlePrint = () => {
    if (!summaryByModelAndWarehouse.length) return;
    const doc = new jsPDF();
    doc.setR2L(true);
    doc.setFont('Amiri-Regular');
    doc.text('ملخص الجرد التراكمي', 105, 15, { align: 'center' });
    autoTable(doc, {
      startY: 25,
      head: [
        [
          'الموديل',
          'المخزن',
          'إجمالي المخزون',
          'الأجهزة المدخلة',
          'الأجهزة المفقودة',
        ],
      ],
      body: summaryByModelAndWarehouse.map((item) => [
        item.model,
        item.warehouseName,
        item.total,
        item.entered,
        item.missing,
      ]),
      styles: { font: 'Amiri-Regular', halign: 'right' },
      headStyles: { halign: 'center', fillColor: [44, 51, 51] },
    });
    doc.autoPrint();
    doc.output('dataurlnewwindow');
  };

  if (!results) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="enhanced-dialog sm:max-w-5xl">
        <DialogHeader className="enhanced-dialog-header">
          <DialogTitle className="enhanced-dialog-title flex items-center space-x-2 space-x-reverse">
            <TrendingUp className="h-5 w-5 text-primary" />
            <span>ملخص الجرد التراكمي</span>
          </DialogTitle>
          <DialogDescription className="enhanced-dialog-description">
            مقارنة شاملة بين الأجهزة المدخلة والمفقودة لكل موديل ومخزن
          </DialogDescription>
        </DialogHeader>
        <div className="enhanced-scroll-area p-4">
          <Table className="enhanced-modal-table">
            <TableHeader>
              <TableRow>
                <TableHead className="w-16">#</TableHead>
                <TableHead>الموديل</TableHead>
                <TableHead>المخزن</TableHead>
                <TableHead className="text-center">إجمالي المخزون</TableHead>
                <TableHead className="text-center">الأجهزة المدخلة</TableHead>
                <TableHead className="text-center">الأجهزة المفقودة</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {summaryByModelAndWarehouse.map((item, index) => (
                <TableRow key={index}>
                  <TableCell className="font-medium text-center text-gray-500">
                    {index + 1}
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2 space-x-reverse">
                      <Smartphone className="h-4 w-4 text-gray-500" />
                      <span className="font-medium">{item.model}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2 space-x-reverse">
                      <Building className="h-4 w-4 text-gray-500" />
                      <span>{item.warehouseName}</span>
                    </div>
                  </TableCell>
                  <TableCell className="text-center">
                    <div className="enhanced-badge bg-gray-500/10 text-gray-600 border-gray-500/20">
                      {item.total}
                    </div>
                  </TableCell>
                  <TableCell className="text-center">
                    <div className="enhanced-badge bg-green-500/10 text-green-600 border-green-500/20">
                      {item.entered}
                    </div>
                  </TableCell>
                  <TableCell className="text-center">
                    <div className={`enhanced-badge ${
                      item.missing > 0
                        ? 'bg-red-500/10 text-red-600 border-red-500/20'
                        : 'bg-green-500/10 text-green-600 border-green-500/20'
                    }`}>
                      {item.missing}
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
        <DialogFooter className="justify-between">
          <div className="flex gap-2">
            <Button variant="outline" onClick={() => handleExport('pdf')}>
              <FileDown className="ml-2 h-4 w-4" /> PDF
            </Button>
            <Button variant="outline" onClick={() => handleExport('excel')}>
              <FileSpreadsheet className="ml-2 h-4 w-4" /> Excel
            </Button>
            <Button variant="outline" onClick={handlePrint}>
              <Printer className="ml-2 h-4 w-4" /> طباعة
            </Button>
          </div>
          <DialogClose asChild>
            <Button variant="outline">إغلاق</Button>
          </DialogClose>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};


// Component for showing details of a historic stocktake
const HistoryDetailsDialog = ({
  isOpen,
  onClose,
  historyEntry,
  onReopen,
}: {
  isOpen: boolean;
  onClose: () => void;
  historyEntry: StocktakeHistory | null;
  onReopen: (entry: StocktakeHistory) => void;
}) => {
  const [resultDetails, setResultDetails] = useState<ResultDetails | null>(
    null
    );
  if (!historyEntry) return null;

  const handleResultCardClick = (
    title: string,
    items: StocktakeResultList[] | undefined,
  ) => {
    if (!items || items.length === 0) {
      toast({ title: 'لا توجد بيانات', description: 'هذه القائمة فارغة.' });
      return;
    }
    setResultDetails({ title, items });
  };

  const renderResultCard = (
    title: string,
    count: number,
    icon: React.ReactNode,
    className: string,
    items: StocktakeResultList[] | undefined,
  ) => (
    <Card
      className={cn(
        'cursor-pointer transition-all hover:shadow-md hover:-translate-y-1',
        className,
      )}
      onClick={() => handleResultCardClick(title, items)}
    >
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        {icon}
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{count}</div>
      </CardContent>
    </Card>
  );

  return (
    <>
      <ResultSummaryDialog
        isOpen={!!resultDetails}
        onClose={() => setResultDetails(null)}
        details={resultDetails}
      />
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>تفاصيل الجرد: {historyEntry.id}</DialogTitle>
            <DialogDescription>
              تاريخ الإكمال:{' '}
              {new Date(historyEntry.completedAt).toLocaleString('ar-EG', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit',
              })}{' '}
              | بواسطة: {historyEntry.userName}
            </DialogDescription>
          </DialogHeader>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 my-4">
            {renderResultCard(
              'أجهزة مطابقة',
              historyEntry.summary.matched,
              <Check className="h-4 w-4 text-green-500" />,
              'bg-green-500/10 border-green-500/20',
              historyEntry.result?.matching,
            )}
            {renderResultCard(
              'أجهزة مفقودة',
              historyEntry.summary.missing,
              <X className="h-4 w-4 text-red-500" />,
              'bg-red-500/10 border-red-500/20',
              historyEntry.result?.missing,
            )}
            {renderResultCard(
              'أجهزة زائدة/خاطئة',
              historyEntry.summary.extra,
              <AlertTriangle className="h-4 w-4 text-yellow-500" />,
              'bg-yellow-500/10 border-yellow-500/20',
              historyEntry.result?.extra,
            )}
            {renderResultCard(
              'مباعة وموجودة',
              historyEntry.summary.sold,
              <ShoppingCart className="h-4 w-4 text-purple-500" />,
              'bg-purple-500/10 border-purple-500/20',
              historyEntry.result?.soldButFound,
            )}
            {renderResultCard(
              'موجودة في الصيانة',
              historyEntry.summary.inMaintenance,
              <Wrench className="h-4 w-4 text-blue-500" />,
              'bg-blue-500/10 border-blue-500/20',
              historyEntry.result?.inMaintenance,
            )}
          </div>
          <DialogFooter className="justify-between">
            <div className="flex gap-2">
              <Button variant="outline" size="sm">
                <FileDown className="ml-2 h-4 w-4" /> تصدير التقرير
              </Button>
              <Button variant="outline" size="sm">
                <Printer className="ml-2 h-4 w-4" /> طباعة
              </Button>
            </div>
            <Button
              variant="secondary"
              size="sm"
              onClick={() => onReopen(historyEntry)}
            >
              <Edit className="ml-2 h-4 w-4" /> فتح وتعديل الأمر
            </Button>
            <Button variant="outline" onClick={onClose}>
              إغلاق
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default function StocktakingPage() {
  const { toast } = useToast();

  // State management
  const [isLoading, setIsLoading] = useState(true);
  const [devices, setDevices] = useState<Device[]>([]);
  const [warehouses, setWarehouses] = useState<Warehouse[]>([]);
  const [systemSettings, setSystemSettings] = useState<SystemSettings>({
    id: 1,
    logoUrl: '',
    companyNameAr: 'نظام إدارة الأجهزة',
    companyNameEn: 'Device Management System',
    addressAr: '',
    addressEn: '',
    phone: '',
    email: '',
    website: '',
    footerTextAr: '',
    footerTextEn: '',
    updatedAt: new Date(),
    createdAt: new Date()
  });
  const [currentUser, setCurrentUser] = useState({ name: 'المستخدم الحالي' });

  // API functions
  const fetchAllData = async () => {
    setIsLoading(true);
    try {
      const [devicesRes, warehousesRes] = await Promise.all([
        fetch('/api/devices-simple'),
        fetch('/api/warehouses-simple')
      ]);

      if (devicesRes.ok) setDevices(await devicesRes.json());
      if (warehousesRes.ok) setWarehouses(await warehousesRes.json());

      // جلب إعدادات النظام
      try {
        const settingsRes = await fetch('/api/settings-simple');
        if (settingsRes.ok) setSystemSettings(await settingsRes.json());
      } catch (error) {
        console.error('خطأ في تحميل إعدادات النظام:', error);
      }

    } catch (error) {
      console.error('خطأ في تحميل البيانات:', error);
      toast({
        variant: 'destructive',
        title: 'خطأ',
        description: 'فشل في تحميل البيانات'
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchAllData();
  }, []);

  const [stocktakeId, setStocktakeId] = useState('');
  const [notes, setNotes] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [warehouseScope, setWarehouseScope] = useState<'all' | 'selected'>(
    'all'
    );
  const [selectedWarehouses, setSelectedWarehouses] = useState<number[]>([]);
  const [includeMaintenance, setIncludeMaintenance] = useState(false);
  const [modelScope, setModelScope] = useState<'all' | 'selected'>('all');
  const [selectedModels, setSelectedModels] = useState<number[]>([]);
  const [scannedItems, setScannedItems] = useState<string[]>([]);
  const [imeiInput, setImeiInput] = useState('');
  const [results, setResults] = useState<StocktakeResults | null>(null);

  const [showModelDialog, setShowModelDialog] = useState(false);
  const [isHistoryModalOpen, setIsHistoryModalOpen] = useState(false);
  const [isDraftsModalOpen, setIsDraftsModalOpen] = useState(false);
  const [isPasteModalOpen, setIsPasteModalOpen] = useState(false);
  const [pastedImeis, setPastedImeis] = useState('');
  const [selectedHistory, setSelectedHistory] =
    useState<StocktakeHistory | null>(null);
  const [showHistoryDetailsModal, setShowHistoryDetailsModal] = useState(false);
  const [drafts, setDrafts] = useState<StocktakeDraft[]>([]);
  const [history, setHistory] = useState<StocktakeHistory[]>([]);
  const [isCancelAlertOpen, setIsCancelAlertOpen] = useState(false);
  const [isDeleteAlertOpen, setIsDeleteAlertOpen] = useState(false);


  const [processedSerialsInSession, setProcessedSerialsInSession] = useState<
    Set<string>
  >(new Set());
  const [duplicateImeis, setDuplicateImeis] = useState<string[]>([]);
  const [invalidImeis, setInvalidImeis] = useState<string[]>([]);
  const [showDetailsDialog, setShowDetailsDialog] = useState(false);
  const [dialogTitle, setDialogTitle] = useState('');
  const [dialogItems, setDialogItems] = useState<string[]>([]);
  const [resultDetails, setResultDetails] = useState<ResultDetails | null>(
    null
    );
  const [isSummaryModalOpen, setIsSummaryModalOpen] = useState(false);


  const fileInputRef = useRef<HTMLInputElement>(null);

  const generateNewStocktakeId = (
    currentHistory: StocktakeHistory[],
    currentDrafts: StocktakeDraft[],
  ) => {
    const historyIds = currentHistory
      .map((h) => parseInt(h.id.replace('STK-', ''), 10))
      .filter((n) => !isNaN(n));
    const draftIds = currentDrafts
      .map((d) => parseInt(d.id.replace('STK-', ''), 10))
      .filter((n) => !isNaN(n));
    const allIds = [...historyIds, ...draftIds];
    const maxId = allIds.length > 0 ? Math.max(...allIds) : 0;
    return `STK-${maxId + 1}`;
  };

  useEffect(() => {
    // Load drafts and history from local storage on mount
    const savedDrafts = localStorage.getItem('stocktakeDrafts');
    const loadedDrafts = savedDrafts ? JSON.parse(savedDrafts) : [];
    setDrafts(loadedDrafts);

    const savedHistory = localStorage.getItem('stocktakeHistory');
    const loadedHistory = savedHistory ? JSON.parse(savedHistory) : [];
    setHistory(loadedHistory);

    setStocktakeId(generateNewStocktakeId(loadedHistory, loadedDrafts));
  }, []);

  const resetPage = () => {
    const newId = generateNewStocktakeId(history, drafts);
    setStocktakeId(newId);
    setNotes('');
    setIsProcessing(false);
    setWarehouseScope('all');
    setSelectedWarehouses([]);
    setIncludeMaintenance(false);
    setModelScope('all');
    setSelectedModels([]);
    setScannedItems([]);
    setImeiInput('');
    setResults(null);
    setDuplicateImeis([]);
    setInvalidImeis([]);
    setProcessedSerialsInSession(new Set());
  };

  const handleCreateNew = () => {
    const hasUnsavedData =
      processedSerialsInSession.size > 0 || notes.trim() !== '';

    if (hasUnsavedData) {
      toast({
        title: 'عملية جرد نشطة',
        description: 'يرجى حفظ المسودة الحالية أو إلغائها قبل البدء من جديد.',
        variant: 'destructive',
      });
      return;
    }
    resetPage();
    toast({
      title: 'تم التعيين',
      description: 'تم إعداد صفحة الجرد لعملية جديدة.',
    });
  };

  const getWarehouseName = (id: number | undefined) =>
    warehouses.find((w) => w.id === id)?.name || 'غير محدد';

  const handleStartStocktake = () => {
    if (warehouseScope === 'selected' && selectedWarehouses.length === 0) {
      toast({
        variant: 'destructive',
        title: 'خطأ في النطاق',
        description: 'يرجى اختيار مخزن واحد على الأقل للمتابعة.',
      });
      return;
    }
    if (scannedItems.length === 0) {
      toast({
        variant: 'destructive',
        title: 'القائمة فارغة',
        description: 'يرجى إضافة أجهزة للمطابقة.',
      });
      return;
    }

    setIsProcessing(true);
    
    // Update the set of all processed serials for this session
    const newProcessedSerials = new Set([...processedSerialsInSession, ...scannedItems]);

    const expectedDevices = devices.filter((device) => {
      const inWarehouseScope =
        warehouseScope === 'all' ||
        selectedWarehouses.includes(device.warehouseId ?? -1);
      const isMaintenanceScope =
        includeMaintenance &&
        ['تحتاج صيانة', 'قيد الإصلاح'].includes(device.status);
      const inModelScope =
        modelScope === 'all' ||
        selectedModels.includes(parseInt(device.model.replace(/\D/g, ''))); // Simplified model ID parsing

      return (inWarehouseScope || isMaintenanceScope) && inModelScope;
    });

    const expectedDeviceIds = new Set(expectedDevices.map((d) => d.id));

    // Initialize with existing results or create a new object
    const currentResults = results ? JSON.parse(JSON.stringify(results)) : { ...initialResultsState };

    // Process only the newly scanned items
    scannedItems.forEach((imei) => {
      const device = devices.find((d) => d.id === imei);
      if (device) {
        if (device.status === 'مباع') {
          currentResults.soldButFound.push({
            deviceId: device.id,
            model: device.model,
            status: device.status,
            warehouseName: getWarehouseName(device.warehouseId),
          });
        } else if (
          ['تحتاج صيانة', 'قيد الإصلاح'].includes(device.status)
        ) {
          currentResults.inMaintenance.push({
            deviceId: device.id,
            model: device.model,
            status: device.status,
            warehouseName: getWarehouseName(device.warehouseId),
          });
        } else if (expectedDeviceIds.has(imei)) {
          currentResults.matching.push({
            deviceId: device.id,
            model: device.model,
            status: device.status,
            warehouseName: getWarehouseName(device.warehouseId),
          });
        } else {
          currentResults.extra.push({
            deviceId: device.id,
            model: device.model,
            status: device.status,
            warehouseName: getWarehouseName(device.warehouseId),
            note: 'موجود في موقع غير متوقع',
          });
        }
      } else {
        currentResults.extra.push({
          deviceId: imei,
          model: 'جهاز غير مسجل',
          status: 'غير معروف',
          note: 'غير مسجل بالنظام',
        });
      }
    });
    
    // Recalculate missing based on all processed serials
    currentResults.missing = expectedDevices
      .filter((d) => !newProcessedSerials.has(d.id))
      .map((d) => ({
        deviceId: d.id,
        model: d.model,
        status: d.status,
        warehouseName: getWarehouseName(d.warehouseId),
      }));

    currentResults.expectedCount = expectedDevices.length;

    setResults(currentResults);
    setScannedItems([]); // Clear the temporary list of scanned items
    setProcessedSerialsInSession(newProcessedSerials); // Update the session's master list

    setIsProcessing(false);
    toast({
      title: 'اكتملت المطابقة',
      description: 'تم تحديث النتائج. قائمة الجرد جاهزة لدفعة جديدة.',
    });
  };

  const handleAddImei = () => {
    if (!imeiInput.trim()) return;
    const imei = imeiInput.trim();
    if (processedSerialsInSession.has(imei) || scannedItems.includes(imei)) {
      setDuplicateImeis((prev) => [...prev, imei]);
      toast({
        variant: 'destructive',
        title: 'IMEI مكرر',
        description: 'تم مسح هذا الرقم التسلسلي بالفعل في هذه الجلسة.',
      });
      setImeiInput('');
      return;
    }
    setScannedItems((prev) => [...prev, imei]);
    setImeiInput('');
  };

  const handleFileImport = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      const text = e.target?.result as string;
      addItemsFromText(text);
    };
    reader.readAsText(file);
    event.target.value = '';
  };

  const handlePastedText = () => {
    addItemsFromText(pastedImeis);
    setPastedImeis('');
    setIsPasteModalOpen(false);
  };

  const addItemsFromText = (text: string) => {
    const lines = text
      .split(/\r?\n/)
      .map((line) => line.trim())
      .filter(Boolean);
    const existingAndProcessed = new Set([
      ...scannedItems,
      ...processedSerialsInSession,
    ]);

    const newItems: string[] = [];
    const newDuplicates: string[] = [];
    const newInvalids: string[] = [];

    lines.forEach((line) => {
      if (line.length < 5) { // Simple validation
        newInvalids.push(line);
      } else if (existingAndProcessed.has(line)) {
        newDuplicates.push(line);
      } else {
        newItems.push(line);
        existingAndProcessed.add(line);
      }
    });

    setScannedItems((prev) => [...prev, ...newItems]);
    setDuplicateImeis((prev) => [...prev, ...newDuplicates]);
    setInvalidImeis((prev) => [...prev, ...newInvalids]);

    toast({
      title: 'اكتملت الإضافة',
      description: `تمت إضافة ${newItems.length} جهازًا. تم تجاهل ${newDuplicates.length} مكرر و ${newInvalids.length} غير صالح.`,
    });
  };


  const handleRemoveImei = (imei: string) => {
    setScannedItems((p) => p.filter((i) => i !== imei));
  };

  const handleSaveDraftClick = () => {
    if (
      scannedItems.length === 0 &&
      processedSerialsInSession.size === 0 &&
      !notes
    ) {
      toast({
        title: 'لا يوجد ما يمكن حفظه',
        description: 'الرجاء إضافة أجهزة أو ملاحظات أولاً.',
        variant: 'destructive',
      });
      return;
    }
    const draftData: StocktakeDraft = {
      id: stocktakeId,
      scope: {
        warehouseIds: selectedWarehouses,
        deviceModelIds: selectedModels,
        includeMaintenance,
      },
      notes,
      processedSerialNumbers: [...processedSerialsInSession, ...scannedItems],
      lastSavedAt: new Date().toISOString(),
      result: results || initialResultsState,
    };
    const updatedDrafts = [
      ...drafts.filter((d) => d.id !== draftData.id),
      draftData,
    ];
    setDrafts(updatedDrafts);
    localStorage.setItem('stocktakeDrafts', JSON.stringify(updatedDrafts));
    toast({
      title: 'تم الحفظ كمسودة',
      description: `تم حفظ عملية الجرد رقم ${stocktakeId} بنجاح.`,
    });
  };

  const handleResumeDraft = (draftToResume: StocktakeHistory | StocktakeDraft) => {
      resetPage(); 
      setTimeout(() => {
        const draft = { ...draftToResume };
        setStocktakeId(draft.id);
        setNotes('notes' in draft ? draft.notes || '' : '');
        setSelectedWarehouses(draft.scope.warehouseIds || []);
        setWarehouseScope((draft.scope.warehouseIds?.length ?? 0) > 0 ? 'selected' : 'all');
        setSelectedModels(draft.scope.deviceModelIds || []);
        setModelScope((draft.scope.deviceModelIds?.length ?? 0) > 0 ? 'selected' : 'all');
        setIncludeMaintenance(draft.scope.includeMaintenance || false);
        setScannedItems([]);

        const processed =
          'processedSerialNumbers' in draft && draft.processedSerialNumbers
            ? draft.processedSerialNumbers
            : draft.result
              ? Object.values(draft.result).filter(Array.isArray).flatMap((cat: any) => (cat || []).map((i: any) => i.deviceId))
              : [];
        
        setProcessedSerialsInSession(new Set(processed));
        
        const fullResult: StocktakeResults = {
            expectedCount: draft.result?.expectedCount || 0,
            matching: draft.result?.matching || [],
            missing: draft.result?.missing || [],
            extra: draft.result?.extra || [],
            soldButFound: draft.result?.soldButFound || [],
            inMaintenance: draft.result?.inMaintenance || [],
        };
        setResults(fullResult);

        setDuplicateImeis([]);
        setInvalidImeis([]);
        setIsDraftsModalOpen(false);
        setShowHistoryDetailsModal(false);
        toast({
          title: 'تم استئناف العملية',
          description: `تم استئناف عملية الجرد رقم ${draft.id}.`,
        });
      }, 100); 
  };


  const handleDeleteDraft = (draftId: string) => {
    const updatedDrafts = drafts.filter((d) => d.id !== draftId);
    setDrafts(updatedDrafts);
    localStorage.setItem('stocktakeDrafts', JSON.stringify(updatedDrafts));
    toast({
      title: 'تم حذف المسودة',
      variant: 'destructive',
    });
  };

  const handleCompleteAndSave = () => {
    if (!results) {
      toast({
        title: 'خطأ',
        description: 'يرجى إجراء المطابقة أولاً قبل حفظ النتائج.',
        variant: 'destructive',
      });
      return;
    }

    const newHistoryEntry: StocktakeHistory = {
      id: stocktakeId,
      completedAt: new Date().toISOString(),
      userName: currentUser?.name || 'غير معروف',
      userId: currentUser?.id || 0,
      scope: {
        warehouseIds: selectedWarehouses,
        deviceModelIds: selectedModels,
        includeMaintenance: includeMaintenance,
      },
      summary: {
        expected: results.expectedCount,
        matched: results.matching.length,
        missing: results.missing.length,
        extra: results.extra.length,
        misplaced:
          results.extra.filter((d) => d.note !== 'غير مسجل بالنظام').length,
        sold: results.soldButFound.length,
        inMaintenance: results.inMaintenance.length,
      },
      result: results,
    };

    const existingHistoryIndex = history.findIndex((h) => h.id === stocktakeId);
    let updatedHistory;

    if (existingHistoryIndex > -1) {
      updatedHistory = [...history];
      updatedHistory[existingHistoryIndex] = newHistoryEntry;
    } else {
      updatedHistory = [...history, newHistoryEntry];
    }
    
    setHistory(updatedHistory.sort((a,b) => new Date(b.completedAt).getTime() - new Date(a.completedAt).getTime()));
    localStorage.setItem('stocktakeHistory', JSON.stringify(updatedHistory));

    const updatedDrafts = drafts.filter((d) => d.id !== stocktakeId);
    setDrafts(updatedDrafts);
    localStorage.setItem('stocktakeDrafts', JSON.stringify(updatedDrafts));

    toast({
      title: 'تم الحفظ بنجاح',
      description: 'تم حفظ نتائج الجرد في سجل العمليات.',
    });

    resetPage();
  };
  
  const handleDeleteStocktake = () => {
    // Remove from drafts
    const updatedDrafts = drafts.filter((d) => d.id !== stocktakeId);
    setDrafts(updatedDrafts);
    localStorage.setItem('stocktakeDrafts', JSON.stringify(updatedDrafts));
    
    // Remove from history
    const updatedHistory = history.filter((h) => h.id !== stocktakeId);
    setHistory(updatedHistory);
    localStorage.setItem('stocktakeHistory', JSON.stringify(updatedHistory));

    resetPage();
    setIsDeleteAlertOpen(false);
    
    toast({
      title: 'تم الحذف',
      description: 'تم حذف أمر الجرد نهائياً.',
      variant: 'destructive',
    });
  };

  const openDetailsDialog = (title: string, items: string[]) => {
    setDialogTitle(title);
    setDialogItems(items);
    setShowDetailsDialog(true);
  };

  const handleResultCardClick = (
    title: string,
    items: StocktakeResultList[] | undefined,
  ) => {
    if (!items) {
      toast({ title: 'لا توجد بيانات', description: 'هذه القائمة فارغة.' });
      return;
    }
    if (items.length > 0) {
      setResultDetails({ title, items });
    } else {
      toast({ title: 'لا توجد بيانات', description: 'هذه القائمة فارغة.' });
    }
  };

  const renderResultCard = (
    title: string,
    count: number,
    icon: React.ReactNode,
    className: string,
    onClick: () => void,
  ) => (
    <Card
      className={cn(
        'cursor-pointer transition-all hover:shadow-md hover:-translate-y-1',
        className,
      )}
      onClick={onClick}
    >
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        {icon}
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{count}</div>
      </CardContent>
    </Card>
  );

  return (
    <div className="stocktaking-page space-y-6">
      <ResultSummaryDialog
        isOpen={!!resultDetails}
        onClose={() => setResultDetails(null)}
        details={resultDetails}
      />
      <CumulativeSummaryDialog
        isOpen={isSummaryModalOpen}
        onClose={() => setIsSummaryModalOpen(false)}
        results={results}
        processedSerials={processedSerialsInSession}
        devices={devices}
        warehouses={warehouses}
      />

      {/* Header Section */}
      <div className="header-card p-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight flex items-center space-x-3 space-x-reverse">
              <div className="p-3 rounded-full bg-gradient-to-br from-primary/20 to-primary/10 icon-enhanced">
                <ClipboardList className="h-8 w-8 text-primary" />
              </div>
              <span className="bg-gradient-to-r from-primary to-primary/80 bg-clip-text text-transparent">
                نظام الجرد المتقدم
              </span>
            </h1>
            <p className="text-muted-foreground mt-2 text-lg">
              إدارة وتنفيذ عمليات جرد المخزون بطريقة احترافية ودقيقة
            </p>
          </div>
          <div className="flex items-center space-x-3 space-x-reverse">
            {/* زر الوضع الليلي */}
            <DarkModeToggle
              size="md"
              variant="outline"
              className="enhanced-button"
            />

            <div className="enhanced-badge bg-blue-500/10 text-blue-600 border-blue-500/20">
              <Package className="h-4 w-4 ml-1 icon-enhanced" />
              إجمالي الأجهزة: {processedSerialsInSession.size + scannedItems.length}
            </div>
            {results && (
              <div className="enhanced-badge bg-green-500/10 text-green-600 border-green-500/20">
                <CheckCircle2 className="h-4 w-4 ml-1 icon-enhanced" />
                مطابقة: {results.matching.length}
              </div>
            )}
            {results && results.missing.length > 0 && (
              <div className="enhanced-badge bg-red-500/10 text-red-600 border-red-500/20">
                <XCircle className="h-4 w-4 ml-1 icon-enhanced" />
                مفقودة: {results.missing.length}
              </div>
            )}
          </div>
        </div>
      </div>

      <Card className="enhanced-stocktake-card card-new-stocktake">
        <CardHeader className="flex flex-row items-center justify-between">
          <div className="space-y-2">
            <CardTitle className="text-2xl flex items-center space-x-2 space-x-reverse">
              <PlusCircle className="h-6 w-6 text-primary icon-enhanced" />
              <span>عملية جرد جديدة</span>
            </CardTitle>
            <CardDescription className="text-base">
              حدد نطاق الجرد، أدخل الأجهزة، ثم ابدأ المطابقة لعرض النتائج.
            </CardDescription>
          </div>
          <div className="flex gap-3">
            <Button
              onClick={handleCreateNew}
              className="enhanced-button bg-primary hover:bg-primary/90 text-white"
            >
              <PlusCircle className="ml-2 h-4 w-4" /> إنشاء عملية جديدة
            </Button>
            <Button
              variant="outline"
              onClick={() => setIsHistoryModalOpen(true)}
              className="enhanced-button variant-outline bg-white text-gray-800 border-gray-300 hover:bg-gray-50"
            >
              <FolderOpen className="ml-2 h-4 w-4" /> عرض العمليات السابقة
            </Button>
            <Button
              variant="outline"
              onClick={() => setIsDraftsModalOpen(true)}
              className="enhanced-button variant-outline bg-white text-gray-800 border-gray-300 hover:bg-gray-50"
            >
              <Save className="ml-2 h-4 w-4" /> فتح مسودة
            </Button>
          </div>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Basic Information Section */}
          <div className="info-section">
            <h3 className="font-semibold text-base flex items-center space-x-2 space-x-reverse mb-3 text-gray-800">
              <Info className="h-4 w-4 text-primary icon-enhanced" />
              <span>معلومات أساسية</span>
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3">
              <div className="space-y-2">
                <Label htmlFor="stocktakeId" className="flex items-center space-x-2 space-x-reverse text-gray-700 font-medium">
                  <BarChart3 className="h-4 w-4 text-blue-600" />
                  <span>رقم عملية الجرد</span>
                </Label>
                <Input
                  id="stocktakeId"
                  value={stocktakeId}
                  disabled
                  className="enhanced-input font-mono bg-gray-50 text-gray-700"
                />
              </div>
              <div className="space-y-2">
                <Label className="flex items-center space-x-2 space-x-reverse text-gray-700 font-medium">
                  <User className="h-4 w-4 text-blue-600" />
                  <span>المستخدم المسؤول</span>
                </Label>
                <Input
                  value={currentUser?.name || 'مدير النظام'}
                  disabled
                  className="enhanced-input bg-gray-50 text-gray-700"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="stocktakeDate" className="flex items-center space-x-2 space-x-reverse text-gray-700 font-medium">
                  <Calendar className="h-4 w-4 text-blue-600" />
                  <span>تاريخ الجرد</span>
                </Label>
                <Input
                  id="stocktakeDate"
                  type="date"
                  defaultValue={new Date().toISOString().split('T')[0]}
                  className="enhanced-input bg-white text-gray-800"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="notes-input" className="flex items-center space-x-2 space-x-reverse text-gray-700 font-medium">
                  <FileSpreadsheet className="h-4 w-4 text-blue-600" />
                  <span>ملاحظات</span>
                </Label>
                <Input
                  id="notes-input"
                  placeholder="ملاحظات على عملية الجرد"
                  value={notes}
                  onChange={(e) => setNotes(e.target.value)}
                  className="enhanced-input bg-white text-gray-800 placeholder-gray-500"
                />
              </div>
            </div>
          </div>
          {/* Scope Selection Section */}
          <div className="scope-section">
            <h3 className="font-semibold text-base flex items-center space-x-2 space-x-reverse mb-3 text-gray-800">
              <Layers className="h-4 w-4 text-green-600 icon-enhanced" />
              <span>تحديد نطاق الجرد</span>
            </h3>
            <div className="grid md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label className="flex items-center space-x-2 space-x-reverse text-sm font-medium text-gray-700">
                  <Building className="h-4 w-4 text-green-600" />
                  <span>المخازن</span>
                </Label>
                <Select
                  value={warehouseScope}
                  onValueChange={(v) => setWarehouseScope(v as any)}
                >
                  <SelectTrigger className="enhanced-input">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">
                      <div className="flex items-center space-x-2 space-x-reverse">
                        <Building className="h-4 w-4" />
                        <span>كل المخازن</span>
                      </div>
                    </SelectItem>
                    <SelectItem value="selected">
                      <div className="flex items-center space-x-2 space-x-reverse">
                        <CheckCircle2 className="h-4 w-4" />
                        <span>تحديد مخازن</span>
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
                {warehouseScope === 'selected' && (
                  <div className="device-list-container p-2 max-h-24 overflow-y-auto">
                    {warehouses.map((w) => (
                      <div key={w.id} className="flex items-center gap-2 p-1 text-sm">
                        <input
                          type="checkbox"
                          id={`wh-${w.id}`}
                          checked={selectedWarehouses.includes(w.id)}
                          onChange={(e) =>
                            setSelectedWarehouses((p) =>
                              e.target.checked
                                ? [...p, w.id]
                                : p.filter((id) => id !== w.id),
                            )
                          }
                          className="rounded"
                        />
                        <Label htmlFor={`wh-${w.id}`} className="flex-1 cursor-pointer text-gray-700">
                          {w.name}
                        </Label>
                      </div>
                    ))}
                  </div>
                )}
              </div>
              <div className="space-y-2">
                <Label className="flex items-center space-x-2 space-x-reverse text-sm font-medium text-gray-700">
                  <Smartphone className="h-4 w-4 text-green-600" />
                  <span>الموديلات</span>
                </Label>
                <Select
                  value={modelScope}
                  onValueChange={(v) => setModelScope(v as any)}
                >
                  <SelectTrigger className="enhanced-input">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">
                      <div className="flex items-center space-x-2 space-x-reverse">
                        <Smartphone className="h-4 w-4" />
                        <span>كل الموديلات</span>
                      </div>
                    </SelectItem>
                    <SelectItem value="selected">
                      <div className="flex items-center space-x-2 space-x-reverse">
                        <CheckCircle2 className="h-4 w-4" />
                        <span>تحديد موديلات</span>
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
                {modelScope === 'selected' && (
                  <Button
                    variant="outline"
                    onClick={() => setShowModelDialog(true)}
                    className="enhanced-button variant-outline bg-white text-gray-800 border-gray-300 hover:bg-gray-50 w-full justify-start"
                  >
                    <Search className="ml-2 h-4 w-4" />
                    اختر الموديلات ({selectedModels.length})
                  </Button>
                )}
              </div>
              <div className="space-y-2">
                <Label className="flex items-center space-x-2 space-x-reverse text-sm font-medium text-gray-700">
                  <Wrench className="h-4 w-4 text-green-600" />
                  <span>خيارات إضافية</span>
                </Label>
                <div className="flex items-center gap-3 p-3 bg-blue-500/10 rounded-lg border border-blue-500/20">
                  <input
                    type="checkbox"
                    id="inc-maint"
                    checked={includeMaintenance}
                    onChange={(e) => setIncludeMaintenance(e.target.checked)}
                    className="rounded"
                  />
                  <Label htmlFor="inc-maint" className="flex items-center space-x-2 space-x-reverse cursor-pointer text-gray-700">
                    <Wrench className="h-4 w-4 text-blue-500" />
                    <span>تضمين قسم الصيانة</span>
                  </Label>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card className="enhanced-stocktake-card card-inventory-list">
        <CardHeader>
          <div className="flex justify-between items-center">
            <div className="space-y-2">
              <CardTitle className="text-2xl flex items-center space-x-2 space-x-reverse">
                <ScanLine className="h-6 w-6 text-green-600 icon-enhanced" />
                <span>قائمة الجرد الفعلية</span>
              </CardTitle>
              <CardDescription className="text-base">
                أدخل أو امسح الأرقام التسلسلية للأجهزة المراد جردها
              </CardDescription>
            </div>
            <div className="flex items-center gap-3 text-sm">
              <div className="flex items-center space-x-2 space-x-reverse">
                <Package className="h-4 w-4 text-blue-500 icon-enhanced" />
                <span className="text-muted-foreground">الإجمالي:</span>
                <div className="enhanced-badge bg-blue-500/10 text-blue-600 border-blue-500/20">
                  {processedSerialsInSession.size + scannedItems.length}
                </div>
              </div>
              <Button
                variant="link"
                className="p-0 h-auto text-destructive hover:text-destructive/80 enhanced-button"
                onClick={() =>
                  openDetailsDialog('الأرقام المكررة', duplicateImeis)
                }
                disabled={duplicateImeis.length === 0}
              >
                <div className="flex items-center space-x-1 space-x-reverse">
                  <AlertCircle className="h-4 w-4 icon-enhanced" />
                  <span>مكرر:</span>
                  <div className="enhanced-badge bg-red-500/10 text-red-600 border-red-500/20">
                    {duplicateImeis.length}
                  </div>
                </div>
              </Button>
              <Button
                variant="link"
                className="p-0 h-auto text-yellow-600 hover:text-yellow-600/80 enhanced-button"
                onClick={() =>
                  openDetailsDialog('الأرقام غير الصالحة', invalidImeis)
                }
                disabled={invalidImeis.length === 0}
              >
                <div className="flex items-center space-x-1 space-x-reverse">
                  <XCircle className="h-4 w-4 icon-enhanced" />
                  <span>غير صالح:</span>
                  <div className="enhanced-badge bg-yellow-500/10 text-yellow-600 border-yellow-500/20">
                    {invalidImeis.length}
                  </div>
                </div>
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Input Methods Section */}
          <div className="input-section">
            <h3 className="font-semibold text-base flex items-center space-x-2 space-x-reverse mb-3 text-gray-800">
              <ScanLine className="h-4 w-4 text-orange-600 icon-enhanced" />
              <span>طرق الإدخال</span>
            </h3>
            <div className="grid md:grid-cols-2 gap-4">
              <div className="space-y-3">
                <Label htmlFor="imei-input" className="flex items-center space-x-2 space-x-reverse text-base font-medium text-gray-700">
                  <ScanLine className="h-4 w-4 text-orange-600" />
                  <span>إدخال IMEI يدوي أو مسح باركود</span>
                </Label>
                <div className="flex gap-2">
                  <Input
                    id="imei-input"
                    placeholder="امسح أو أدخل الرقم التسلسلي"
                    value={imeiInput}
                    onChange={(e) => setImeiInput(e.target.value)}
                    onKeyDown={(e) => e.key === 'Enter' && handleAddImei()}
                    className="enhanced-input font-mono bg-white text-gray-800 placeholder-gray-500"
                  />
                  <Button
                    onClick={handleAddImei}
                    className="enhanced-button bg-primary hover:bg-primary/90 text-white"
                  >
                    <PlusCircle className="h-4 w-4 ml-2" />
                    إضافة
                  </Button>
                </div>
              </div>
              <div className="space-y-3">
                <Label className="flex items-center space-x-2 space-x-reverse text-base font-medium text-gray-700">
                  <Upload className="h-4 w-4 text-orange-600" />
                  <span>استيراد أو لصق قائمة</span>
                </Label>
                <div className="flex gap-2">
                  <input
                    type="file"
                    ref={fileInputRef}
                    onChange={handleFileImport}
                    className="hidden"
                    accept=".txt,.csv"
                  />
                  <Button
                    variant="outline"
                    size="sm"
                    className="enhanced-button variant-outline bg-white text-gray-800 border-gray-300 hover:bg-gray-50 flex-1"
                    onClick={() => fileInputRef.current?.click()}
                    title="استيراد من ملف"
                  >
                    <Upload className="h-4 w-4 ml-2" /> استيراد من ملف
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="enhanced-button variant-outline bg-white text-gray-800 border-gray-300 hover:bg-gray-50 flex-1"
                    onClick={() => setIsPasteModalOpen(true)}
                    title="لصق قائمة من الحافظة"
                  >
                    <ClipboardPaste className="h-4 w-4 ml-2" /> لصق قائمة
                  </Button>
                </div>
              </div>
            </div>
          </div>
          {/* Device List */}
          <div className="device-list-container mt-4 h-64">
            <div className="p-3">
              <h4 className="font-semibold mb-3 flex items-center space-x-2 space-x-reverse text-sm">
                <Package className="h-4 w-4 text-primary icon-enhanced" />
                <span>الأجهزة المضافة في الدفعة الحالية ({scannedItems.length})</span>
              </h4>
              {scannedItems.length > 0 ? (
                <div className="overflow-hidden">
                  <table className="device-table">
                    <thead>
                      <tr>
                        <th className="device-row-number">#</th>
                        <th>الرقم التسلسلي</th>
                        <th className="device-actions">إجراء</th>
                      </tr>
                    </thead>
                    <tbody>
                      {scannedItems.map((imei, index) => {
                        return (
                          <tr key={imei}>
                            <td className="device-row-number">{index + 1}</td>
                            <td className="device-imei" title={`IMEI: ${imei}`}>{imei}</td>
                            <td className="device-actions">
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-6 w-6 enhanced-button hover:bg-destructive/10"
                                onClick={() => handleRemoveImei(imei)}
                                title="حذف الجهاز من القائمة"
                              >
                                <Trash2 className="h-3 w-3 text-destructive icon-enhanced" />
                              </Button>
                            </td>
                          </tr>
                        );
                      })}
                    </tbody>
                  </table>
                </div>
              ) : (
                <div className="text-center text-muted-foreground pt-8 flex flex-col items-center space-y-2">
                  <Package className="h-10 w-10 text-muted-foreground/50" />
                  <p className="text-sm">لم يتم إضافة أجهزة بعد.</p>
                  <p className="text-xs">ابدأ بإدخال الأرقام التسلسلية أعلاه</p>
                </div>
              )}
            </div>
          </div>
        </CardContent>
        <CardFooter className="justify-between pt-6 border-t">
          <div className="flex gap-3">
            <Button
              variant="outline"
              onClick={handleSaveDraftClick}
              className="enhanced-button variant-outline bg-white text-gray-800 border-gray-300 hover:bg-gray-50"
            >
              <Save className="h-4 w-4 ml-2" />
              حفظ كمسودة
            </Button>
            <Button
              variant="destructive"
              onClick={() => setIsCancelAlertOpen(true)}
              className="enhanced-button variant-destructive bg-red-600 text-white hover:bg-red-700"
            >
              <X className="ml-2 h-4 w-4" /> إلغاء الأمر
            </Button>
          </div>
          <Button
            onClick={handleStartStocktake}
            disabled={isProcessing || scannedItems.length === 0}
            className="enhanced-button bg-primary hover:bg-primary/90 disabled:opacity-50 text-white"
            size="lg"
          >
            <ClipboardList className="h-4 w-4 ml-2" />
            {isProcessing ? (
              <div className="flex items-center space-x-2 space-x-reverse">
                <div className="loading-shimmer w-4 h-4 rounded"></div>
                <span>جاري المعالجة...</span>
              </div>
            ) : (
              `بدء المطابقة (${scannedItems.length})`
            )}
          </Button>
        </CardFooter>
      </Card>

      {results && (
        <Card className="enhanced-stocktake-card card-results">
          <CardHeader>
            <div className="space-y-2">
              <CardTitle className="text-2xl flex items-center space-x-2 space-x-reverse">
                <TrendingUp className="h-6 w-6 text-orange-600 icon-enhanced" />
                <span>نتائج الجرد</span>
              </CardTitle>
              <CardDescription className="text-base">
                ملخص شامل لنتائج عملية الجرد مع إمكانية عرض التفاصيل
              </CardDescription>
            </div>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4 mb-6">
              <div
                className="result-card result-summary cursor-pointer"
                onClick={() => setIsSummaryModalOpen(true)}
              >
                <div className="flex items-center justify-between mb-3">
                  <Package className="h-6 w-6 text-gray-500 icon-enhanced" />
                  <div className="enhanced-badge bg-gray-500/10 text-gray-600 border-gray-500/20">
                    {processedSerialsInSession.size}
                  </div>
                </div>
                <h3 className="font-semibold text-sm">ملخص الجرد التراكمي</h3>
                <p className="text-xs text-muted-foreground mt-1">إجمالي الأجهزة المعالجة</p>
              </div>

              <div
                className="result-card result-matching cursor-pointer"
                onClick={() => handleResultCardClick('تفاصيل الأجهزة المطابقة', results.matching)}
              >
                <div className="flex items-center justify-between mb-3">
                  <CheckCircle2 className="h-6 w-6 text-green-500 icon-enhanced" />
                  <div className="enhanced-badge bg-green-500/10 text-green-600 border-green-500/20">
                    {results.matching.length}
                  </div>
                </div>
                <h3 className="font-semibold text-sm">أجهزة مطابقة</h3>
                <p className="text-xs text-muted-foreground mt-1">موجودة في المكان الصحيح</p>
              </div>

              <div
                className="result-card result-missing cursor-pointer"
                onClick={() => handleResultCardClick('تفاصيل الأجهزة المفقودة', results.missing)}
              >
                <div className="flex items-center justify-between mb-3">
                  <XCircle className="h-6 w-6 text-red-500 icon-enhanced" />
                  <div className="enhanced-badge bg-red-500/10 text-red-600 border-red-500/20">
                    {results.missing.length}
                  </div>
                </div>
                <h3 className="font-semibold text-sm">أجهزة مفقودة</h3>
                <p className="text-xs text-muted-foreground mt-1">غير موجودة في المخزن</p>
              </div>

              <div
                className="result-card result-extra cursor-pointer"
                onClick={() => handleResultCardClick('تفاصيل الأجهزة الزائدة أو في غير مكانها', results.extra)}
              >
                <div className="flex items-center justify-between mb-3">
                  <AlertTriangle className="h-6 w-6 text-yellow-500 icon-enhanced" />
                  <div className="enhanced-badge bg-yellow-500/10 text-yellow-600 border-yellow-500/20">
                    {results.extra.length}
                  </div>
                </div>
                <h3 className="font-semibold text-sm">أجهزة زائدة/خاطئة</h3>
                <p className="text-xs text-muted-foreground mt-1">في غير مكانها المحدد</p>
              </div>

              <div
                className="result-card result-sold cursor-pointer"
                onClick={() => handleResultCardClick('تفاصيل الأجهزة المباعة والموجودة', results.soldButFound)}
              >
                <div className="flex items-center justify-between mb-3">
                  <ShoppingCart className="h-6 w-6 text-purple-500 icon-enhanced" />
                  <div className="enhanced-badge bg-purple-500/10 text-purple-600 border-purple-500/20">
                    {results.soldButFound.length}
                  </div>
                </div>
                <h3 className="font-semibold text-sm">مباعة وموجودة</h3>
                <p className="text-xs text-muted-foreground mt-1">تم بيعها لكنها موجودة</p>
              </div>

              <div
                className="result-card result-maintenance cursor-pointer"
                onClick={() => handleResultCardClick('تفاصيل الأجهزة الموجودة في الصيانة', results.inMaintenance)}
              >
                <div className="flex items-center justify-between mb-3">
                  <Wrench className="h-6 w-6 text-blue-500 icon-enhanced" />
                  <div className="enhanced-badge bg-blue-500/10 text-blue-600 border-blue-500/20">
                    {results.inMaintenance.length}
                  </div>
                </div>
                <h3 className="font-semibold text-sm">موجودة في الصيانة</h3>
                <p className="text-xs text-muted-foreground mt-1">قيد الإصلاح حالياً</p>
              </div>
            </div>
          </CardContent>
          <CardFooter className="justify-between pt-6 border-t">
            <div className="flex gap-3">
              <Button
                variant="destructive"
                onClick={() => setIsDeleteAlertOpen(true)}
                className="enhanced-button variant-destructive bg-red-600 text-white hover:bg-red-700"
              >
                <Trash2 className="ml-2 h-4 w-4" /> حذف الأمر
              </Button>
            </div>
            <Button
              onClick={handleCompleteAndSave}
              className="enhanced-button bg-green-600 hover:bg-green-700 text-white"
              size="lg"
            >
              <Save className="h-4 w-4 ml-2" />
              إكمال وحفظ
            </Button>
          </CardFooter>
        </Card>
      )}

      {/* Dialog for result details */}
      <CommandDialog open={showModelDialog} onOpenChange={setShowModelDialog}>
        <CommandInput placeholder="ابحث عن موديل..." />
        <CommandList>
          <CommandEmpty>لم يتم العثور على موديلات.</CommandEmpty>
          <CommandGroup heading="الموديلات المتاحة">
            {/* Populate with models from store */}
          </CommandGroup>
        </CommandList>
      </CommandDialog>

      <Dialog open={isDraftsModalOpen} onOpenChange={setIsDraftsModalOpen}>
        <DialogContent className="enhanced-dialog max-w-4xl">
          <DialogHeader className="enhanced-dialog-header">
            <DialogTitle className="enhanced-dialog-title flex items-center space-x-2 space-x-reverse">
              <Save className="h-5 w-5 text-primary" />
              <span>المسودات المحفوظة</span>
            </DialogTitle>
            <DialogDescription className="enhanced-dialog-description">
              استئناف العمل على عمليات الجرد المحفوظة مسبقاً
            </DialogDescription>
          </DialogHeader>
          <div className="enhanced-scroll-area p-4">
            <Table className="enhanced-modal-table">
              <TableHeader>
                <TableRow>
                  <TableHead className="w-16">#</TableHead>
                  <TableHead>رقم الجرد</TableHead>
                  <TableHead>تاريخ الحفظ</TableHead>
                  <TableHead className="text-center">عدد الأجهزة</TableHead>
                  <TableHead className="text-center">إجراءات</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {drafts.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={5} className="h-32 text-center">
                      <div className="flex flex-col items-center space-y-3 text-muted-foreground">
                        <Save className="h-12 w-12" />
                        <p>لا توجد مسودات محفوظة.</p>
                        <p className="text-sm">احفظ عملية جرد كمسودة لتظهر هنا</p>
                      </div>
                    </TableCell>
                  </TableRow>
                ) : (
                  drafts.map((draft, index) => (
                    <TableRow key={draft.id}>
                      <TableCell className="font-medium text-center text-gray-500">
                        {index + 1}
                      </TableCell>
                      <TableCell className="font-mono font-medium">
                        {draft.id}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2 space-x-reverse">
                          <Calendar className="h-4 w-4 text-gray-500" />
                          <div className="flex flex-col">
                            <span>{new Date(draft.lastSavedAt).toLocaleDateString('ar-EG')}</span>
                            <span className="text-xs text-gray-500">
                              {new Date(draft.lastSavedAt).toLocaleTimeString('ar-EG', {
                                hour: '2-digit',
                                minute: '2-digit'
                              })}
                            </span>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell className="text-center">
                        <div className="enhanced-badge bg-blue-500/10 text-blue-600 border-blue-500/20">
                          <Package className="h-3 w-3 ml-1" />
                          {draft.processedSerialNumbers.length}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex gap-2 justify-center">
                          <Button
                            size="sm"
                            onClick={() => handleResumeDraft(draft)}
                            className="enhanced-modal-button primary"
                            title="استئناف العمل على هذه المسودة"
                          >
                            <PlusCircle className="h-4 w-4 ml-1" />
                            استئناف
                          </Button>
                          <Button
                            variant="destructive"
                            size="sm"
                            onClick={() => handleDeleteDraft(draft.id)}
                            className="enhanced-modal-button destructive"
                            title="حذف هذه المسودة نهائياً"
                          >
                            <Trash2 className="h-4 w-4 ml-1" />
                            حذف
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </DialogContent>
      </Dialog>

      <Dialog open={isHistoryModalOpen} onOpenChange={setIsHistoryModalOpen}>
        <DialogContent className="enhanced-dialog max-w-5xl">
          <DialogHeader className="enhanced-dialog-header">
            <DialogTitle className="enhanced-dialog-title flex items-center space-x-2 space-x-reverse">
              <FolderOpen className="h-5 w-5 text-primary" />
              <span>سجل عمليات الجرد السابقة</span>
            </DialogTitle>
            <DialogDescription className="enhanced-dialog-description">
              عرض جميع عمليات الجرد المكتملة مع إمكانية الاطلاع على التفاصيل
            </DialogDescription>
          </DialogHeader>
          <div className="enhanced-scroll-area p-4">
            <Table className="enhanced-modal-table">
              <TableHeader>
                <TableRow>
                  <TableHead className="w-24">#</TableHead>
                  <TableHead>رقم العملية</TableHead>
                  <TableHead>المخزن</TableHead>
                  <TableHead>التاريخ</TableHead>
                  <TableHead>المستخدم</TableHead>
                  <TableHead className="text-center">مطابق</TableHead>
                  <TableHead className="text-center">مفقود</TableHead>
                  <TableHead className="text-center">زائد</TableHead>
                  <TableHead className="text-center">إجراء</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {history.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={9} className="h-32 text-center">
                      <div className="flex flex-col items-center space-y-3 text-muted-foreground">
                        <FolderOpen className="h-12 w-12" />
                        <p>لا يوجد سجل لعرضه.</p>
                        <p className="text-sm">ستظهر العمليات المكتملة هنا</p>
                      </div>
                    </TableCell>
                  </TableRow>
                ) : (
                  history.map((hist, index) => (
                    <TableRow key={hist.id}>
                      <TableCell className="font-medium text-center text-gray-500">
                        {index + 1}
                      </TableCell>
                      <TableCell className="font-mono font-medium">
                        {hist.id}
                      </TableCell>
                      <TableCell>
                        <div className="flex flex-col">
                          <span className="font-medium">
                            {hist.scope.warehouseIds.length > 0
                              ? hist.scope.warehouseIds
                                  .map((id) => getWarehouseName(id))
                                  .join(', ')
                              : 'كل المخازن'}
                          </span>
                          {hist.scope.includeMaintenance && (
                            <span className="text-xs text-blue-600 bg-blue-50 px-2 py-1 rounded mt-1 inline-block w-fit">
                              + قسم الصيانة
                            </span>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2 space-x-reverse">
                          <Calendar className="h-4 w-4 text-gray-500" />
                          <span>{new Date(hist.completedAt).toLocaleDateString('ar-EG')}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2 space-x-reverse">
                          <User className="h-4 w-4 text-gray-500" />
                          <span>{hist.userName}</span>
                        </div>
                      </TableCell>
                      <TableCell className="text-center">
                        <div className="enhanced-badge bg-green-500/10 text-green-600 border-green-500/20">
                          {hist.summary.matched}
                        </div>
                      </TableCell>
                      <TableCell className="text-center">
                        <div className="enhanced-badge bg-red-500/10 text-red-600 border-red-500/20">
                          {hist.summary.missing}
                        </div>
                      </TableCell>
                      <TableCell className="text-center">
                        <div className="enhanced-badge bg-blue-500/10 text-blue-600 border-blue-500/20">
                          {hist.summary.extra}
                        </div>
                      </TableCell>
                      <TableCell className="text-center">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => {
                            setSelectedHistory(hist);
                            setShowHistoryDetailsModal(true);
                          }}
                          className="enhanced-modal-button hover:bg-primary/10"
                          title="عرض التفاصيل"
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </DialogContent>
      </Dialog>

      <HistoryDetailsDialog
        isOpen={showHistoryDetailsModal}
        onClose={() => setShowHistoryDetailsModal(false)}
        historyEntry={selectedHistory}
        onReopen={handleResumeDraft}
      />

      <Dialog open={isPasteModalOpen} onOpenChange={setIsPasteModalOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>لصق قائمة الأرقام التسلسلية</DialogTitle>
            <DialogDescription>
              ألصق قائمة الأرقام التسلسلية هنا، كل رقم في سطر جديد.
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <Textarea
              placeholder="111111111111111
222222222222222
333333333333333"
              className="h-48 font-mono"
              value={pastedImeis}
              onChange={(e) => setPastedImeis(e.target.value)}
            />
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsPasteModalOpen(false)}
            >
              إلغاء
            </Button>
            <Button onClick={handlePastedText}>إضافة للقائمة</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      <AlertDialog open={isCancelAlertOpen} onOpenChange={setIsCancelAlertOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>هل أنت متأكد من الإلغاء؟</AlertDialogTitle>
            <AlertDialogDescription>
              سيتم فقدان جميع البيانات غير المحفوظة في أمر الجرد الحالي.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>تراجع</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => {
                resetPage();
                setIsCancelAlertOpen(false);
              }}
              className="bg-destructive hover:bg-destructive/90"
            >
              متابعة الإلغاء
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
      <AlertDialog open={isDeleteAlertOpen} onOpenChange={setIsDeleteAlertOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>هل أنت متأكد من الحذف؟</AlertDialogTitle>
            <AlertDialogDescription>
              سيؤدي هذا الإجراء إلى حذف أمر الجرد الحالي من المسودات نهائيًا.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>تراجع</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteStocktake}
              className="bg-destructive hover:bg-destructive/90"
            >
              متابعة الحذف
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
      <Dialog open={showDetailsDialog} onOpenChange={setShowDetailsDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>{dialogTitle}</DialogTitle>
          </DialogHeader>
          <ScrollArea className="max-h-80">
            <ul className="space-y-1 font-mono text-sm text-muted-foreground">
              {dialogItems.map((item, index) => (
                <li key={index}>{item}</li>
              ))}
            </ul>
          </ScrollArea>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowDetailsDialog(false)}
            >
              إغلاق
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}