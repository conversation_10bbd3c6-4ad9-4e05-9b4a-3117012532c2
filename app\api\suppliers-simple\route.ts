import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// GET - جلب جميع الموردين
export async function GET() {
  try {
    const suppliers = await prisma.supplier.findMany({
      orderBy: { name: 'asc' }
    });

    return NextResponse.json(suppliers);
  } catch (error) {
    console.error('خطأ في جلب الموردين:', error);
    return NextResponse.json(
      { error: 'فشل في جلب الموردين' },
      { status: 500 }
    );
  }
}

// POST - إنشاء مورد جديد
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { name, phone, email, address } = body;

    // التحقق من البيانات المطلوبة
    if (!name) {
      return NextResponse.json(
        { error: 'اسم المورد مطلوب' },
        { status: 400 }
      );
    }

    // التحقق من عدم تكرار الهاتف
    if (phone) {
      const existingSupplier = await prisma.supplier.findFirst({
        where: { phone }
      });

      if (existingSupplier) {
        return NextResponse.json(
          { error: 'يوجد مورد بهذا الرقم بالفعل' },
          { status: 400 }
        );
      }
    }

    const supplier = await prisma.supplier.create({
      data: {
        name,
        phone: phone || null,
        email: email || null,
        address: address || null
      }
    });

    return NextResponse.json(supplier, { status: 201 });
  } catch (error) {
    console.error('خطأ في إنشاء المورد:', error);
    return NextResponse.json(
      { error: 'فشل في إنشاء المورد' },
      { status: 500 }
    );
  }
}

// PUT - تحديث مورد
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { id, name, phone, email, address } = body;

    if (!id) {
      return NextResponse.json(
        { error: 'معرف المورد مطلوب' },
        { status: 400 }
      );
    }

    // التحقق من وجود المورد
    const existingSupplier = await prisma.supplier.findUnique({
      where: { id: parseInt(id) }
    });

    if (!existingSupplier) {
      return NextResponse.json(
        { error: 'المورد غير موجود' },
        { status: 404 }
      );
    }

    // التحقق من عدم تكرار الهاتف (إذا تم تغييره)
    if (phone && phone !== existingSupplier.phone) {
      const duplicateSupplier = await prisma.supplier.findFirst({
        where: { 
          phone,
          id: { not: parseInt(id) }
        }
      });

      if (duplicateSupplier) {
        return NextResponse.json(
          { error: 'يوجد مورد آخر بهذا الرقم' },
          { status: 400 }
        );
      }
    }

    const supplier = await prisma.supplier.update({
      where: { id: parseInt(id) },
      data: {
        ...(name && { name }),
        ...(phone !== undefined && { phone }),
        ...(email !== undefined && { email }),
        ...(address !== undefined && { address })
      }
    });

    return NextResponse.json(supplier);
  } catch (error) {
    console.error('خطأ في تحديث المورد:', error);
    return NextResponse.json(
      { error: 'فشل في تحديث المورد' },
      { status: 500 }
    );
  }
}

// DELETE - حذف مورد
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json(
        { error: 'معرف المورد مطلوب' },
        { status: 400 }
      );
    }

    // التحقق من وجود المورد
    const existingSupplier = await prisma.supplier.findUnique({
      where: { id: parseInt(id) }
    });

    if (!existingSupplier) {
      return NextResponse.json(
        { error: 'المورد غير موجود' },
        { status: 404 }
      );
    }

    // التحقق من وجود أوامر توريد مرتبطة
    const supplyOrdersCount = await prisma.supplyOrder.count({
      where: { supplierId: parseInt(id) }
    });

    if (supplyOrdersCount > 0) {
      return NextResponse.json(
        { error: `لا يمكن حذف المورد لأنه مرتبط بـ ${supplyOrdersCount} أمر توريد` },
        { status: 400 }
      );
    }

    await prisma.supplier.delete({
      where: { id: parseInt(id) }
    });

    return NextResponse.json({ message: 'تم حذف المورد بنجاح' });
  } catch (error) {
    console.error('خطأ في حذف المورد:', error);
    return NextResponse.json(
      { error: 'فشل في حذف المورد' },
      { status: 500 }
    );
  }
}
