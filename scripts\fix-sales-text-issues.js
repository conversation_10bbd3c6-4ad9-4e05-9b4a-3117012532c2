const fs = require('fs');

function fixSalesTextIssues() {
  console.log('🔧 إصلاح مشاكل .text() في sales/page.tsx...\n');

  const filePath = 'app/(main)/sales/page.tsx';
  
  if (!fs.existsSync(filePath)) {
    console.log('❌ ملف sales/page.tsx غير موجود');
    return false;
  }

  let content = fs.readFileSync(filePath, 'utf8');
  let hasChanges = false;
  const originalContent = content;

  console.log('📄 معالجة sales/page.tsx...');

  // 1. إصلاح مشاكل .text() مع الأقواس الخاطئة
  const textIssuePattern = /\.text\(([^)]+),\)\s*\{\s*([^}]+)\s*\}\s*\)/g;
  const textMatches = content.match(textIssuePattern);
  if (textMatches) {
    content = content.replace(textIssuePattern, '.text($1, { $2 })');
    console.log(`   ✅ إصلاح ${textMatches.length} مشكلة .text()`);
    hasChanges = true;
  }

  // 2. إصلاح مشاكل autoTable مع الأقواس الخاطئة
  const autoTablePattern = /autoTable\(doc,\)\s*\{/g;
  const autoTableMatches = content.match(autoTablePattern);
  if (autoTableMatches) {
    content = content.replace(autoTablePattern, 'autoTable(doc, {');
    console.log(`   ✅ إصلاح ${autoTableMatches.length} مشكلة autoTable()`);
    hasChanges = true;
  }

  // 3. إصلاح مشاكل .text() المتبقية
  const remainingTextPattern = /\.text\(([^)]+),\)\s*\{\s*align:\s*'([^']+)'\s*\}\s*\)/g;
  content = content.replace(remainingTextPattern, '.text($1, { align: \'$2\' })');

  // 4. إصلاح مشاكل .text() مع فاصلة زائدة
  const extraCommaPattern = /\.text\(([^,]+),([^,]+),([^,]+),\)\s*\{\s*align:\s*'([^']+)'\s*\}/g;
  content = content.replace(extraCommaPattern, '.text($1, $2, $3, { align: \'$4\' })');

  // 5. إصلاح مشاكل محددة أخرى
  content = content.replace(/doc\.text\('فاتورة بيع', 190, 45,\)\s*\{\s*align:\s*'right'\s*\}\s*\)/g, 
    "doc.text('فاتورة بيع', 190, 45, { align: 'right' })");

  // 6. إصلاح مشاكل في استدعاءات أخرى
  content = content.replace(/(\w+)\(([^)]+),\)\s*\{/g, '$1($2, {');

  // 7. فحص وإصلاح الأقواس غير المتطابقة
  const openBraces = (content.match(/\{/g) || []).length;
  const closeBraces = (content.match(/\}/g) || []).length;
  const openParens = (content.match(/\(/g) || []).length;
  const closeParens = (content.match(/\)/g) || []).length;

  console.log(`   📊 فحص الأقواس:`);
  console.log(`      - أقواس معقوفة: ${openBraces} فتح، ${closeBraces} إغلاق`);
  console.log(`      - أقواس عادية: ${openParens} فتح، ${closeParens} إغلاق`);

  if (content !== originalContent) {
    hasChanges = true;
  }

  if (hasChanges) {
    fs.writeFileSync(filePath, content);
    console.log(`   🎉 تم إصلاح sales/page.tsx بنجاح`);
    return true;
  } else {
    console.log(`   ℹ️ sales/page.tsx: لا يحتاج إصلاح`);
    return true;
  }
}

// دالة لفحص مشاكل محددة في sales
function checkSalesSpecificIssues() {
  console.log('\n🔍 فحص مشاكل محددة في sales/page.tsx...\n');

  const filePath = 'app/(main)/sales/page.tsx';
  
  if (!fs.existsSync(filePath)) {
    console.log('❌ ملف sales/page.tsx غير موجود');
    return false;
  }

  const content = fs.readFileSync(filePath, 'utf8');
  const issues = [];

  // فحص مشاكل .text()
  const textIssues = content.match(/\.text\([^)]+,\)\s*\{/g);
  if (textIssues) {
    issues.push(`${textIssues.length} مشكلة في .text()`);
  }

  // فحص مشاكل autoTable
  const autoTableIssues = content.match(/autoTable\([^)]+,\)\s*\{/g);
  if (autoTableIssues) {
    issues.push(`${autoTableIssues.length} مشكلة في autoTable()`);
  }

  // فحص أقواس غير متطابقة
  const openParens = (content.match(/\(/g) || []).length;
  const closeParens = (content.match(/\)/g) || []).length;
  if (openParens !== closeParens) {
    issues.push(`أقواس غير متطابقة: ${openParens} فتح، ${closeParens} إغلاق`);
  }

  if (issues.length > 0) {
    console.log('⚠️ المشاكل المكتشفة:');
    issues.forEach(issue => console.log(`   - ${issue}`));
    return false;
  } else {
    console.log('✅ sales/page.tsx: نظيف من المشاكل المحددة');
    return true;
  }
}

if (require.main === module) {
  const success = fixSalesTextIssues();
  const clean = checkSalesSpecificIssues();
  process.exit(success && clean ? 0 : 1);
}

module.exports = { fixSalesTextIssues, checkSalesSpecificIssues };
