'use client';

import { useState, useEffect } from 'react';
import { AuditLog } from '@/lib/types';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';

export default function AuditLogsPage() {
  const [auditLogs, setAuditLogs] = useState<AuditLog[]>([]);

  useEffect(() => {
    fetchAuditLogs();
  }, []);

  const fetchAuditLogs = async () => {
    const res = await fetch('/api/audit-logs');
    const data = await res.json();
    setAuditLogs(data);
  };

  return (
    <div className="container mx-auto py-10">
      <h1 className="text-3xl font-bold mb-6">سجلات التدقيق</h1>

      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>الطابع الزمني</TableHead>
            <TableHead>المستخدم</TableHead>
            <TableHead>العملية</TableHead>
            <TableHead>التفاصيل</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {auditLogs.map((log) => (
            <TableRow key={log.id}>
              <TableCell>{new Date(log.timestamp).toLocaleString()}</TableCell>
              <TableCell>
                {log.username} (ID: {log.userId})
              </TableCell>
              <TableCell>{log.operation}</TableCell>
              <TableCell>{log.details}</TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}
