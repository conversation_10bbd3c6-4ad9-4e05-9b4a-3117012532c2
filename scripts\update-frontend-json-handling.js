const fs = require('fs');
const path = require('path');
const glob = require('glob');

// البحث عن جميع ملفات TypeScript/JavaScript في مجلد app
const files = glob.sync('app/**/*.{ts,tsx,js,jsx}', { 
  ignore: ['node_modules/**', 'dist/**', '.next/**'] 
});

console.log(`🔍 Found ${files.length} files to check...`);

let updatedFiles = 0;

files.forEach(filePath => {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let hasChanges = false;

    // إزالة معالجة JSON القديمة من items
    const oldJsonProcessing = /items:\s*[^.]*\.\s*items\s*\?\s*\(\s*typeof\s+[^.]*\.items\s*===\s*['"]string['"][\s\S]*?\)\s*:\s*\[\]/g;
    if (oldJsonProcessing.test(content)) {
      content = content.replace(oldJsonProcessing, 'items: order.items || []');
      hasChanges = true;
    }

    // تحديث معالجة JSON في map functions
    const mapJsonProcessing = /\.map\(\([^)]+\)\s*=>\s*\(\{[\s\S]*?items:\s*[^.]*\.items\s*\?\s*\(\s*typeof[\s\S]*?\)\s*:\s*\[\][\s\S]*?\}\)\)/g;
    if (mapJsonProcessing.test(content)) {
      content = content.replace(mapJsonProcessing, (match) => {
        return match.replace(/items:\s*[^.]*\.items\s*\?\s*\(\s*typeof[\s\S]*?\)\s*:\s*\[\]/, 'items: item.items || []');
      });
      hasChanges = true;
    }

    // تحديث دوال ensureItemsArray لتكون أبسط
    const ensureItemsArrayPattern = /const ensureItemsArray = \([^)]+\)[^{]*\{[\s\S]*?if \(typeof items === 'string'\)[\s\S]*?\}[\s\S]*?return \[\];[\s\S]*?\};/g;
    if (ensureItemsArrayPattern.test(content)) {
      content = content.replace(ensureItemsArrayPattern, `const ensureItemsArray = (items: any) => {
  if (Array.isArray(items)) {
    return items;
  }
  return [];
};`);
      hasChanges = true;
    }

    // إزالة JSON.parse من items
    const jsonParsePattern = /JSON\.parse\([^)]*\.items\)/g;
    if (jsonParsePattern.test(content)) {
      content = content.replace(jsonParsePattern, (match) => {
        const varName = match.match(/JSON\.parse\(([^.]+)\.items\)/);
        return varName ? `${varName[1]}.items` : 'items';
      });
      hasChanges = true;
    }

    // تحديث typeof checks للـ items
    const typeofItemsPattern = /typeof\s+[^.]*\.items\s*===\s*['"]string['"][\s\S]*?JSON\.parse\([^)]+\)[\s\S]*?:/g;
    if (typeofItemsPattern.test(content)) {
      content = content.replace(typeofItemsPattern, 'Array.isArray(items) ? items :');
      hasChanges = true;
    }

    if (hasChanges) {
      fs.writeFileSync(filePath, content);
      console.log(`✅ Updated: ${filePath}`);
      updatedFiles++;
    }

  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message);
  }
});

console.log(`\n🎉 Updated ${updatedFiles} files successfully!`);

// تحديث ملفات محددة بشكل يدوي
const specificUpdates = [
  {
    file: 'app/(main)/supply/page.tsx',
    updates: [
      {
        search: /items_json\s*\?\s*\(\s*typeof\s+[^.]*\.items_json[\s\S]*?\)\s*:\s*\[\]/g,
        replace: 'items || []'
      }
    ]
  }
];

specificUpdates.forEach(update => {
  try {
    if (fs.existsSync(update.file)) {
      let content = fs.readFileSync(update.file, 'utf8');
      let hasChanges = false;

      update.updates.forEach(u => {
        if (u.search.test(content)) {
          content = content.replace(u.search, u.replace);
          hasChanges = true;
        }
      });

      if (hasChanges) {
        fs.writeFileSync(update.file, content);
        console.log(`✅ Applied specific updates to: ${update.file}`);
      }
    }
  } catch (error) {
    console.error(`❌ Error applying specific updates to ${update.file}:`, error.message);
  }
});

console.log('\n🚀 Frontend JSON handling updated successfully!');
