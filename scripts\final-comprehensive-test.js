const { PrismaClient } = require('@prisma/client');
const fs = require('fs');

const prisma = new PrismaClient();

async function finalComprehensiveTest() {
  console.log('🎯 الاختبار الشامل النهائي للنظام...\n');

  const testResults = {
    database: { connected: false, models: 0, totalModels: 12 },
    apis: { working: 0, total: 0, errors: [] },
    pages: { syntaxOk: 0, total: 0, errors: [] },
    issues: { resolved: 0, remaining: 0 }
  };

  // 1. اختبار قاعدة البيانات
  console.log('🗄️ اختبار قاعدة البيانات الشامل...');
  try {
    await prisma.$connect();
    testResults.database.connected = true;
    console.log('   ✅ الاتصال بقاعدة البيانات: نجح');

    const models = [
      'warehouse', 'client', 'supplier', 'user', 'device',
      'supplyOrder', 'sale', 'return', 'evaluationOrder',
      'maintenanceOrder', 'deliveryOrder', 'maintenanceReceiptOrder'
    ];

    for (const model of models) {
      try {
        await prisma[model].findFirst();
        testResults.database.models++;
        console.log(`   ✅ نموذج ${model}: يعمل`);
      } catch (error) {
        console.log(`   ❌ نموذج ${model}: ${error.message}`);
      }
    }

  } catch (error) {
    console.log(`   ❌ فشل الاتصال: ${error.message}`);
  }

  // 2. اختبار APIs
  console.log('\n🔌 اختبار جميع APIs...');
  const apiPaths = [
    'app/api/warehouses-simple/route.ts',
    'app/api/clients-simple/route.ts',
    'app/api/suppliers-simple/route.ts',
    'app/api/devices/route.ts',
    'app/api/supply/route.ts',
    'app/api/sales/route.ts',
    'app/api/returns/route.ts',
    'app/api/evaluations/route.ts',
    'app/api/maintenance-orders/route.ts',
    'app/api/delivery-orders/route.ts',
    'app/api/maintenance-receipts/route.ts',
    'app/api/users/route.ts'
  ];

  apiPaths.forEach(apiPath => {
    testResults.apis.total++;
    if (fs.existsSync(apiPath)) {
      const content = fs.readFileSync(apiPath, 'utf8');
      if (content.includes('GET') && content.includes('POST')) {
        testResults.apis.working++;
        const apiName = apiPath.split('/').slice(-2, -1)[0];
        console.log(`   ✅ ${apiName}: مكتمل`);
      } else {
        const apiName = apiPath.split('/').slice(-2, -1)[0];
        console.log(`   ⚠️ ${apiName}: غير مكتمل`);
        testResults.apis.errors.push(`${apiName}: API غير مكتمل`);
      }
    } else {
      const apiName = apiPath.split('/').slice(-2, -1)[0];
      console.log(`   ❌ ${apiName}: غير موجود`);
      testResults.apis.errors.push(`${apiName}: ملف غير موجود`);
    }
  });

  // 3. اختبار صيغة الصفحات
  console.log('\n📄 اختبار صيغة جميع الصفحات...');
  const pageFiles = [
    'app/(main)/warehouses/page.tsx',
    'app/(main)/clients/page.tsx',
    'app/(main)/supply/page.tsx',
    'app/(main)/sales/page.tsx',
    'app/(main)/returns/page.tsx',
    'app/(main)/grading/page.tsx',
    'app/(main)/maintenance/page.tsx',
    'app/(main)/maintenance-transfer/page.tsx',
    'app/(main)/messaging/page.tsx',
    'app/(main)/inventory/page.tsx',
    'app/(main)/track/page.tsx',
    'app/(main)/users/page.tsx'
  ];

  pageFiles.forEach(pagePath => {
    testResults.pages.total++;
    if (fs.existsSync(pagePath)) {
      const content = fs.readFileSync(pagePath, 'utf8');
      
      // فحص الأخطاء الشائعة
      const issues = [];
      
      // فحص الأقواس المفقودة
      if (content.includes('if (') && content.includes(') return') && !content.includes(')) return')) {
        issues.push('أقواس مفقودة في if statements');
      }
      
      // فحص استخدام متغيرات غير معرفة
      if (content.includes('currentUser') && !content.includes('useStore') && !content.includes('const { currentUser }')) {
        issues.push('currentUser غير معرف');
      }
      
      if (content.includes('permissions') && !content.includes('const permissions =')) {
        issues.push('permissions غير معرف');
      }
      
      // فحص استخدام .items بدون فحص
      const unsafeItemsUsage = content.match(/\w+\.items\.(map|filter|find|some|every|length)/g) || [];
      const safeItemsUsage = unsafeItemsUsage.filter(usage => {
        const context = content.substring(
          Math.max(0, content.indexOf(usage) - 50),
          content.indexOf(usage) + usage.length + 50
        );
        return !context.includes('Array.isArray');
      });
      
      if (safeItemsUsage.length > 0) {
        issues.push(`${safeItemsUsage.length} استخدام غير آمن لـ .items`);
      }

      const pageName = pagePath.split('/').slice(-2, -1)[0];
      if (issues.length === 0) {
        testResults.pages.syntaxOk++;
        console.log(`   ✅ ${pageName}: صيغة صحيحة`);
      } else {
        console.log(`   ⚠️ ${pageName}: ${issues.join(', ')}`);
        testResults.pages.errors.push(`${pageName}: ${issues.join(', ')}`);
      }
    } else {
      const pageName = pagePath.split('/').slice(-2, -1)[0];
      console.log(`   ❌ ${pageName}: غير موجود`);
      testResults.pages.errors.push(`${pageName}: ملف غير موجود`);
    }
  });

  // 4. اختبار العمليات الأساسية
  console.log('\n🧪 اختبار العمليات الأساسية...');
  try {
    // اختبار إنشاء وحذف بيانات
    const testData = await prisma.warehouse.create({
      data: {
        name: `اختبار نهائي ${Date.now()}`,
        location: 'موقع اختبار',
        type: 'main'
      }
    });
    
    await prisma.warehouse.delete({
      where: { id: testData.id }
    });
    
    console.log('   ✅ عمليات CRUD: تعمل بشكل صحيح');
    testResults.issues.resolved++;
    
  } catch (error) {
    console.log(`   ❌ عمليات CRUD: ${error.message}`);
    testResults.issues.remaining++;
  }

  // 5. النتائج النهائية
  console.log('\n📊 التقرير النهائي الشامل:');
  console.log('═══════════════════════════════════════');

  console.log(`\n🗄️ قاعدة البيانات:`);
  console.log(`   - الاتصال: ${testResults.database.connected ? '✅ متصل' : '❌ غير متصل'}`);
  console.log(`   - النماذج: ${testResults.database.models}/${testResults.database.totalModels} (${Math.round((testResults.database.models / testResults.database.totalModels) * 100)}%)`);

  console.log(`\n🔌 APIs:`);
  console.log(`   - العاملة: ${testResults.apis.working}/${testResults.apis.total} (${Math.round((testResults.apis.working / testResults.apis.total) * 100)}%)`);

  console.log(`\n📄 الصفحات:`);
  console.log(`   - صيغة صحيحة: ${testResults.pages.syntaxOk}/${testResults.pages.total} (${Math.round((testResults.pages.syntaxOk / testResults.pages.total) * 100)}%)`);

  // حساب النتيجة الإجمالية
  const dbScore = testResults.database.connected ? 25 : 0;
  const modelsScore = (testResults.database.models / testResults.database.totalModels) * 25;
  const apisScore = (testResults.apis.working / testResults.apis.total) * 25;
  const pagesScore = (testResults.pages.syntaxOk / testResults.pages.total) * 25;
  const totalScore = dbScore + modelsScore + apisScore + pagesScore;

  console.log(`\n🏆 النتيجة الإجمالية: ${Math.round(totalScore)}%`);

  if (totalScore >= 95) {
    console.log('🎉 ممتاز! النظام جاهز للإنتاج بشكل كامل');
    console.log('🚀 يمكن نشره فوراً بثقة تامة');
  } else if (totalScore >= 85) {
    console.log('👍 جيد جداً! النظام شبه جاهز للإنتاج');
    console.log('⚡ يحتاج تحسينات بسيطة فقط');
  } else if (totalScore >= 75) {
    console.log('👌 جيد! النظام يعمل بشكل أساسي');
    console.log('🔧 يحتاج بعض التحسينات');
  } else {
    console.log('⚠️ يحتاج المزيد من العمل');
  }

  // عرض الأخطاء المتبقية
  if (testResults.apis.errors.length > 0 || testResults.pages.errors.length > 0) {
    console.log('\n🔧 المشاكل المتبقية:');
    [...testResults.apis.errors, ...testResults.pages.errors].forEach(error => {
      console.log(`   - ${error}`);
    });
  }

  console.log('\n📝 التوصيات النهائية:');
  console.log('═══════════════════════════════════════');
  if (totalScore >= 90) {
    console.log('✅ النظام جاهز للاستخدام التجاري');
    console.log('✅ يمكن البدء في التدريب والنشر');
  } else {
    console.log('🔄 إكمال الصفحات المتبقية');
    console.log('🧪 إجراء اختبارات إضافية');
    console.log('🎨 تحسين واجهات المستخدم');
  }

  return totalScore >= 85;
}

if (require.main === module) {
  finalComprehensiveTest()
    .then((success) => {
      process.exit(success ? 0 : 1);
    })
    .catch((error) => {
      console.error('💥 فشل الاختبار الشامل:', error);
      process.exit(1);
    })
    .finally(() => {
      prisma.$disconnect();
    });
}

module.exports = { finalComprehensiveTest };
