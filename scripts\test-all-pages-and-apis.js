const { PrismaClient } = require('@prisma/client');
const fs = require('fs');

const prisma = new PrismaClient();

async function testAllPagesAndAPIs() {
  console.log('🧪 اختبار شامل لجميع الصفحات والـ APIs...\n');

  const testResults = {
    apis: { working: 0, total: 0, errors: [] },
    pages: { updated: 0, total: 0, needsWork: [] },
    database: { models: 0, working: true, errors: [] }
  };

  // 1. اختبار قاعدة البيانات
  console.log('🗄️ اختبار قاعدة البيانات...');
  try {
    const models = [
      'warehouse', 'client', 'supplier', 'user', 'device',
      'supplyOrder', 'sale', 'return', 'evaluationOrder',
      'maintenanceOrder', 'deliveryOrder', 'maintenanceReceiptOrder'
    ];

    for (const model of models) {
      try {
        await prisma[model].findFirst();
        testResults.database.models++;
      } catch (error) {
        testResults.database.working = false;
        testResults.database.errors.push(`${model}: ${error.message}`);
      }
    }

    console.log(`   ✅ ${testResults.database.models}/${models.length} نماذج تعمل`);
    if (testResults.database.errors.length > 0) {
      console.log(`   ⚠️ ${testResults.database.errors.length} نماذج بها مشاكل`);
    }

  } catch (error) {
    console.log(`   ❌ خطأ في الاتصال بقاعدة البيانات: ${error.message}`);
    testResults.database.working = false;
  }

  // 2. اختبار APIs
  console.log('\n🔌 فحص APIs...');
  const apiPaths = [
    'app/api/warehouses/route.ts',
    'app/api/clients/route.ts', 
    'app/api/suppliers/route.ts',
    'app/api/users/route.ts',
    'app/api/devices/route.ts',
    'app/api/supply/route.ts',
    'app/api/sales/route.ts',
    'app/api/returns/route.ts',
    'app/api/evaluations/route.ts',
    'app/api/maintenance-orders/route.ts',
    'app/api/delivery-orders/route.ts',
    'app/api/maintenance-receipts/route.ts',
    'app/api/employee-requests/route.ts',
    'app/api/internal-messages/route.ts',
    'app/api/stocktakes/route.ts',
    'app/api/warehouse-transfers/route.ts'
  ];

  apiPaths.forEach(apiPath => {
    testResults.apis.total++;
    if (fs.existsSync(apiPath)) {
      const content = fs.readFileSync(apiPath, 'utf8');
      if (content.includes('GET') && content.includes('POST')) {
        testResults.apis.working++;
        console.log(`   ✅ ${apiPath.split('/').slice(-2, -1)[0]}`);
      } else {
        testResults.apis.errors.push(`${apiPath}: API غير مكتمل`);
        console.log(`   ⚠️ ${apiPath.split('/').slice(-2, -1)[0]}: غير مكتمل`);
      }
    } else {
      testResults.apis.errors.push(`${apiPath}: ملف غير موجود`);
      console.log(`   ❌ ${apiPath.split('/').slice(-2, -1)[0]}: غير موجود`);
    }
  });

  // 3. فحص الصفحات
  console.log('\n📄 فحص الصفحات...');
  const pagePaths = [
    { path: 'app/(main)/warehouses/page.tsx', name: 'المخازن', updated: true },
    { path: 'app/(main)/clients/page.tsx', name: 'العملاء', updated: false },
    { path: 'app/(main)/users/page.tsx', name: 'المستخدمين', updated: false },
    { path: 'app/(main)/inventory/page.tsx', name: 'المخزون', updated: false },
    { path: 'app/(main)/track/page.tsx', name: 'تتبع الجهاز', updated: false },
    { path: 'app/(main)/stocktaking/page.tsx', name: 'الجرد', updated: false },
    { path: 'app/(main)/requests/page.tsx', name: 'طلبات الموظفين', updated: false },
    { path: 'app/(main)/messaging/page.tsx', name: 'المراسلات', updated: false },
    { path: 'app/(main)/supply/page.tsx', name: 'التوريد', updated: true },
    { path: 'app/(main)/sales/page.tsx', name: 'المبيعات', updated: true },
    { path: 'app/(main)/returns/page.tsx', name: 'المرتجعات', updated: true },
    { path: 'app/(main)/grading/page.tsx', name: 'التقييم', updated: true },
    { path: 'app/(main)/maintenance/page.tsx', name: 'الصيانة', updated: true },
    { path: 'app/(main)/maintenance-transfer/page.tsx', name: 'نقل الصيانة', updated: true }
  ];

  pagePaths.forEach(pageInfo => {
    testResults.pages.total++;
    if (fs.existsSync(pageInfo.path)) {
      const content = fs.readFileSync(pageInfo.path, 'utf8');
      
      if (pageInfo.updated || !content.includes('useStore()')) {
        testResults.pages.updated++;
        console.log(`   ✅ ${pageInfo.name}: محدثة`);
      } else {
        testResults.pages.needsWork.push(pageInfo.name);
        console.log(`   🔄 ${pageInfo.name}: تحتاج تحديث`);
      }
    } else {
      testResults.pages.needsWork.push(`${pageInfo.name}: ملف غير موجود`);
      console.log(`   ❌ ${pageInfo.name}: غير موجودة`);
    }
  });

  // 4. النتائج النهائية
  console.log('\n📊 ملخص النتائج:');
  console.log(`\n🗄️ قاعدة البيانات:`);
  console.log(`   - النماذج العاملة: ${testResults.database.models}`);
  console.log(`   - الحالة: ${testResults.database.working ? '✅ تعمل' : '❌ مشاكل'}`);

  console.log(`\n🔌 APIs:`);
  console.log(`   - العاملة: ${testResults.apis.working}/${testResults.apis.total}`);
  console.log(`   - النسبة: ${Math.round((testResults.apis.working / testResults.apis.total) * 100)}%`);

  console.log(`\n📄 الصفحات:`);
  console.log(`   - المحدثة: ${testResults.pages.updated}/${testResults.pages.total}`);
  console.log(`   - النسبة: ${Math.round((testResults.pages.updated / testResults.pages.total) * 100)}%`);

  // 5. التوصيات
  console.log('\n🎯 التوصيات:');
  
  if (testResults.apis.working === testResults.apis.total) {
    console.log('   ✅ جميع APIs تعمل بشكل صحيح');
  } else {
    console.log(`   🔧 يجب إصلاح ${testResults.apis.total - testResults.apis.working} APIs`);
  }

  if (testResults.pages.updated === testResults.pages.total) {
    console.log('   ✅ جميع الصفحات محدثة');
  } else {
    console.log(`   🔄 يجب تحديث ${testResults.pages.total - testResults.pages.updated} صفحات:`);
    testResults.pages.needsWork.forEach(page => {
      console.log(`      - ${page}`);
    });
  }

  // 6. تقييم عام
  const overallScore = (
    (testResults.database.working ? 25 : 0) +
    (testResults.apis.working / testResults.apis.total * 35) +
    (testResults.pages.updated / testResults.pages.total * 40)
  );

  console.log(`\n🏆 التقييم العام: ${Math.round(overallScore)}%`);
  
  if (overallScore >= 90) {
    console.log('🎉 ممتاز! النظام جاهز للإنتاج');
  } else if (overallScore >= 70) {
    console.log('👍 جيد! يحتاج بعض التحسينات');
  } else if (overallScore >= 50) {
    console.log('⚠️ مقبول! يحتاج عمل إضافي');
  } else {
    console.log('🔧 يحتاج عمل كبير');
  }

  return overallScore >= 70;
}

if (require.main === module) {
  testAllPagesAndAPIs()
    .then((success) => {
      process.exit(success ? 0 : 1);
    })
    .catch((error) => {
      console.error('💥 فشل الاختبار:', error);
      process.exit(1);
    })
    .finally(() => {
      prisma.$disconnect();
    });
}

module.exports = { testAllPagesAndAPIs };
