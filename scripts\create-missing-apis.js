const fs = require('fs');
const path = require('path');

function createMissingAPIs() {
  console.log('🔧 إنشاء APIs المفقودة...\n');

  const apisToCreate = [
    {
      name: 'employee-requests',
      path: 'app/api/employee-requests/route.ts',
      description: 'API طلبات الموظفين',
      model: 'employeeRequest',
      fields: ['id', 'employeeId', 'type', 'description', 'status', 'createdAt', 'processedAt', 'processedBy']
    },
    {
      name: 'internal-messages',
      path: 'app/api/internal-messages/route.ts', 
      description: 'API المراسلات الداخلية',
      model: 'internalMessage',
      fields: ['id', 'senderId', 'recipientId', 'subject', 'content', 'status', 'createdAt', 'readAt']
    },
    {
      name: 'stocktakes',
      path: 'app/api/stocktakes/route.ts',
      description: 'API الجرد',
      model: 'stocktake',
      fields: ['id', 'name', 'warehouseId', 'status', 'startDate', 'endDate', 'createdBy']
    },
    {
      name: 'warehouse-transfers',
      path: 'app/api/warehouse-transfers/route.ts',
      description: 'API التحويلات المخزنية',
      model: 'warehouseTransfer',
      fields: ['id', 'fromWarehouseId', 'toWarehouseId', 'status', 'transferDate', 'notes']
    }
  ];

  let createdCount = 0;
  let skippedCount = 0;

  apisToCreate.forEach(apiInfo => {
    try {
      console.log(`📁 إنشاء ${apiInfo.description}...`);
      
      // التحقق من وجود المجلد
      const dirPath = path.dirname(apiInfo.path);
      if (!fs.existsSync(dirPath)) {
        fs.mkdirSync(dirPath, { recursive: true });
      }

      // التحقق من وجود الملف
      if (fs.existsSync(apiInfo.path)) {
        console.log(`   ℹ️ ${apiInfo.description} موجود بالفعل`);
        skippedCount++;
        return;
      }

      // إنشاء محتوى API
      const apiContent = `import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// GET - جلب جميع ${apiInfo.description}
export async function GET() {
  try {
    const records = await prisma.${apiInfo.model}.findMany({
      orderBy: { createdAt: 'desc' }
    });

    return NextResponse.json(records);
  } catch (error) {
    console.error('خطأ في جلب ${apiInfo.description}:', error);
    return NextResponse.json(
      { error: 'فشل في جلب ${apiInfo.description}' },
      { status: 500 }
    );
  }
}

// POST - إنشاء ${apiInfo.description} جديد
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    const record = await prisma.${apiInfo.model}.create({
      data: body
    });

    return NextResponse.json(record, { status: 201 });
  } catch (error) {
    console.error('خطأ في إنشاء ${apiInfo.description}:', error);
    return NextResponse.json(
      { error: 'فشل في إنشاء ${apiInfo.description}' },
      { status: 500 }
    );
  }
}

// PUT - تحديث ${apiInfo.description}
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { id, ...data } = body;

    if (!id) {
      return NextResponse.json(
        { error: 'معرف السجل مطلوب' },
        { status: 400 }
      );
    }

    const record = await prisma.${apiInfo.model}.update({
      where: { id: parseInt(id) },
      data
    });

    return NextResponse.json(record);
  } catch (error) {
    console.error('خطأ في تحديث ${apiInfo.description}:', error);
    return NextResponse.json(
      { error: 'فشل في تحديث ${apiInfo.description}' },
      { status: 500 }
    );
  }
}

// DELETE - حذف ${apiInfo.description}
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json(
        { error: 'معرف السجل مطلوب' },
        { status: 400 }
      );
    }

    await prisma.${apiInfo.model}.delete({
      where: { id: parseInt(id) }
    });

    return NextResponse.json({ message: 'تم حذف السجل بنجاح' });
  } catch (error) {
    console.error('خطأ في حذف ${apiInfo.description}:', error);
    return NextResponse.json(
      { error: 'فشل في حذف السجل' },
      { status: 500 }
    );
  }
}`;

      fs.writeFileSync(apiInfo.path, apiContent);
      console.log(`   ✅ تم إنشاء ${apiInfo.description}`);
      createdCount++;

    } catch (error) {
      console.log(`   ❌ خطأ في إنشاء ${apiInfo.description}: ${error.message}`);
    }
  });

  console.log(`\n📊 ملخص الإنشاء:`);
  console.log(`   - إجمالي APIs: ${apisToCreate.length}`);
  console.log(`   - تم إنشاؤها: ${createdCount}`);
  console.log(`   - موجودة مسبقاً: ${skippedCount}`);

  if (createdCount > 0) {
    console.log(`\n🎉 تم إنشاء ${createdCount} API جديد!`);
    console.log(`\n📝 ملاحظة: قد تحتاج هذه APIs إلى تحديث حسب schema قاعدة البيانات الفعلية`);
    return true;
  } else {
    console.log(`\nℹ️ جميع APIs موجودة بالفعل`);
    return true;
  }
}

if (require.main === module) {
  const success = createMissingAPIs();
  process.exit(success ? 0 : 1);
}

module.exports = { createMissingAPIs };
