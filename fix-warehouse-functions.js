// Script لتحديث وظائف التخويل المخزني
const fs = require('fs');

const filePath = 'context/store.tsx';
let content = fs.readFileSync(filePath, 'utf8');

console.log('🔄 تحديث وظائف التخويل المخزني...');

// 1. تحديث addWarehouseTransfer
console.log('📝 تحديث addWarehouseTransfer...');

const addWarehouseTransferStart = 'const addWarehouseTransfer = (';
const addWarehouseTransferIndex = content.indexOf(addWarehouseTransferStart);

if (addWarehouseTransferIndex !== -1) {
  // البحث عن نهاية الوظيفة
  let braceCount = 0;
  let currentIndex = addWarehouseTransferIndex;
  let foundStart = false;
  
  while (currentIndex < content.length) {
    if (content[currentIndex] === '{') {
      braceCount++;
      foundStart = true;
    } else if (content[currentIndex] === '}') {
      braceCount--;
      if (foundStart && braceCount === 0) {
        break;
      }
    }
    currentIndex++;
  }
  
  const addWarehouseTransferEndIndex = currentIndex;
  
  const newAddWarehouseTransferFunction = `const addWarehouseTransfer = async (
    transfer: Omit<WarehouseTransfer, "id" | "transferNumber" | "createdAt">,
  ) => {
    try {
      // إعداد البيانات الأساسية
      const transferNumber = \`WT-\${Date.now()}\`;
      
      // حالياً نحفظ محلياً حتى يتم إنشاء API
      // TODO: إنشاء /api/warehouse-transfers endpoint
      
      const newId = Math.max(0, ...warehouseTransfers.map((t) => t.id)) + 1;
      const newTransfer: WarehouseTransfer = {
        ...transfer,
        id: newId,
        transferNumber,
        createdAt: new Date().toISOString(),
      };

      setWarehouseTransfers((prev) => [newTransfer, ...prev]);

      // تحديث حالة الأجهزة
      transfer.items.forEach((item) => {
        updateDeviceStatus(item.deviceId, "محول");
      });

      addActivity({
        type: "supply",
        description: \`تم إنشاء تحويل مخزني جديد: \${transferNumber}\`,
      });
      
      return newTransfer;
    } catch (error) {
      console.error('Failed to add warehouse transfer:', error);
      addActivity({
        type: "supply",
        description: \`⚠️ فشل في إنشاء تحويل مخزني: \${error instanceof Error ? error.message : String(error)}\`,
      });
      throw error;
    }
  }`;

  const beforeFunction = content.substring(0, addWarehouseTransferIndex);
  const afterFunction = content.substring(addWarehouseTransferEndIndex + 1);
  content = beforeFunction + newAddWarehouseTransferFunction + afterFunction;
  console.log('✅ تم تحديث addWarehouseTransfer');
}

// 2. تحديث updateWarehouseTransfer
console.log('📝 تحديث updateWarehouseTransfer...');

const updateWarehouseTransferStart = 'const updateWarehouseTransfer = (transfer: WarehouseTransfer) => {';
const updateWarehouseTransferIndex = content.indexOf(updateWarehouseTransferStart);

if (updateWarehouseTransferIndex !== -1) {
  // البحث عن نهاية الوظيفة
  const nextFunctionStart = content.indexOf('const deleteWarehouseTransfer', updateWarehouseTransferIndex);
  const updateWarehouseTransferEndIndex = content.lastIndexOf('};', nextFunctionStart);
  
  const newUpdateWarehouseTransferFunction = `const updateWarehouseTransfer = async (transfer: WarehouseTransfer) => {
    try {
      // حالياً نحفظ محلياً حتى يتم إنشاء API
      // TODO: إرسال إلى /api/warehouse-transfers

      setWarehouseTransfers((prev) =>
        prev.map((t) => (t.id === transfer.id ? transfer : t)),
      );

      addActivity({
        type: "supply",
        description: \`تم تحديث التحويل المخزني: \${transfer.transferNumber}\`,
      });
      
      return transfer;
    } catch (error) {
      console.error('Failed to update warehouse transfer:', error);
      addActivity({
        type: "supply",
        description: \`⚠️ فشل في تحديث التحويل المخزني: \${error instanceof Error ? error.message : String(error)}\`,
      });
      throw error;
    }
  };`;

  const beforeFunction = content.substring(0, updateWarehouseTransferIndex);
  const afterFunction = content.substring(updateWarehouseTransferEndIndex + 2);
  content = beforeFunction + newUpdateWarehouseTransferFunction + '\n\n  ' + afterFunction;
  console.log('✅ تم تحديث updateWarehouseTransfer');
}

// 3. تحديث deleteWarehouseTransfer
console.log('📝 تحديث deleteWarehouseTransfer...');

const deleteWarehouseTransferStart = 'const deleteWarehouseTransfer = (transferId: number) => {';
const deleteWarehouseTransferIndex = content.indexOf(deleteWarehouseTransferStart);

if (deleteWarehouseTransferIndex !== -1) {
  // البحث عن نهاية الوظيفة
  let braceCount = 0;
  let currentIndex = deleteWarehouseTransferIndex;
  let foundStart = false;
  
  while (currentIndex < content.length) {
    if (content[currentIndex] === '{') {
      braceCount++;
      foundStart = true;
    } else if (content[currentIndex] === '}') {
      braceCount--;
      if (foundStart && braceCount === 0) {
        break;
      }
    }
    currentIndex++;
  }
  
  const deleteWarehouseTransferEndIndex = currentIndex;
  
  const newDeleteWarehouseTransferFunction = `const deleteWarehouseTransfer = async (transferId: number) => {
    try {
      // حالياً نحفظ محلياً حتى يتم إنشاء API
      // TODO: إرسال إلى /api/warehouse-transfers

      setWarehouseTransfers((prev) => prev.filter((t) => t.id !== transferId));

      addActivity({
        type: "supply",
        description: \`تم حذف التحويل المخزني رقم \${transferId}\`,
      });
    } catch (error) {
      console.error('Failed to delete warehouse transfer:', error);
      addActivity({
        type: "supply",
        description: \`⚠️ فشل في حذف التحويل المخزني: \${error instanceof Error ? error.message : String(error)}\`,
      });
      throw error;
    }
  }`;

  const beforeFunction = content.substring(0, deleteWarehouseTransferIndex);
  const afterFunction = content.substring(deleteWarehouseTransferEndIndex + 1);
  content = beforeFunction + newDeleteWarehouseTransferFunction + afterFunction;
  console.log('✅ تم تحديث deleteWarehouseTransfer');
}

fs.writeFileSync(filePath, content, 'utf8');

console.log('🎉 تم تحديث وظائف التخويل المخزني بنجاح!');
console.log('ملاحظة: التخويل المخزني يحتاج إنشاء API endpoint جديد لاحقاً');
