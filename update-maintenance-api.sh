#!/bin/bash

# This script updates the maintenance order CRUD functions in store.tsx to use the API

cat << 'EOL' > maintenance-api-fix.js
// Updated maintenance order functions that use the API endpoints

const addMaintenanceOrderFix = `  const addMaintenanceOrder = async (
    order: Omit<MaintenanceOrder, "id" | "createdAt"> & {
      id?: number;
      status?: "wip" | "completed" | "draft";
    },
  ) => {
    try {
      // إذا لم يكن هناك رقم أمر، نقوم بإنشاء واحد
      if (!order.orderNumber) {
        const allExisting = [...maintenanceOrders];
        const useId =
          order.id && order.id > 0
            ? order.id
            : Math.max(0, ...allExisting.map((o) => o.id)) + 1;
        order.orderNumber = \`MAINT-\${useId}\`;
      }
      
      // إرسال الأمر إلى API
      const response = await fetch('/api/maintenance-orders', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...order,
          status: order.status || 'wip',
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create maintenance order');
      }

      // استقبال الأمر الذي تم إنشاؤه من API
      const newOrder = await response.json();

      // تحديث حالة التطبيق
      setMaintenanceOrders((prev) => [newOrder, ...prev]);

      // إضافة نشاط
      addActivity({
        type: "maintenance",
        description: \`تم إنشاء أمر صيانة \${newOrder.orderNumber}.\`,
      });
      
      return newOrder;
    } catch (error) {
      console.error('Failed to add maintenance order:', error);
      addActivity({
        type: "maintenance",
        description: \`⚠️ فشل في إنشاء أمر صيانة: \${error instanceof Error ? error.message : String(error)}\`,
      });
      throw error;
    }
  };`;

const updateMaintenanceOrderFix = `  const updateMaintenanceOrder = async (updatedOrder: MaintenanceOrder) => {
    try {
      // إرسال الأمر إلى API
      const response = await fetch('/api/maintenance-orders', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(updatedOrder),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update maintenance order');
      }

      // استقبال الأمر المحدّث من API
      const savedOrder = await response.json();

      // تحديث حالة التطبيق
      setMaintenanceOrders((prev) =>
        prev.map((o) =>
          o.id === savedOrder.id
            ? savedOrder
            : o
        ),
      );

      // تحديث حالة الأجهزة
      const originalOrder = maintenanceOrders.find(
        (o) => o.id === updatedOrder.id,
      );
      if (originalOrder) {
        const originalDeviceIds = new Set(originalOrder.items.map((i) => i.id));
        const updatedDeviceIds = new Set(updatedOrder.items.map((i) => i.id));

        const removedDeviceIds = [...originalDeviceIds].filter(
          (id) => !updatedDeviceIds.has(id),
        );
        setDevices((prev) =>
          prev.map((device) =>
            removedDeviceIds.includes(device.id)
              ? { ...device, status: "تحتاج صيانة" }
              : device,
          ),
        );
      }

      updatedOrder.items.forEach((item) => {
        updateDeviceStatus(item.id, "قيد الإصلاح");
      });

      // إضافة نشاط
      addActivity({
        type: "maintenance",
        description: \`تم تحديث أمر الصيانة \${updatedOrder.orderNumber}\`,
      });
      
      return savedOrder;
    } catch (error) {
      console.error('Failed to update maintenance order:', error);
      addActivity({
        type: "maintenance",
        description: \`⚠️ فشل في تحديث أمر صيانة: \${error instanceof Error ? error.message : String(error)}\`,
      });
      throw error;
    }
  };`;

const deleteMaintenanceOrderFix = `  const deleteMaintenanceOrder = async (orderId: number) => {
    try {
      const orderToDelete = maintenanceOrders.find((o) => o.id === orderId);
      if (!orderToDelete) return;

      const relationCheck = checkMaintenanceOrderRelations(orderId);
      if (!relationCheck.canDelete) {
        throw new Error(
          \`لا يمكن حذف أمر الصيانة: \${relationCheck.reason}\${relationCheck.relatedOperations ? "\\nالعمليات المرتبطة: " + relationCheck.relatedOperations.join(", ") : ""}\`,
        );
      }

      // إرسال طلب الحذف إلى API
      const response = await fetch('/api/maintenance-orders', {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ id: orderId }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete maintenance order');
      }

      // تحديث حالة التطبيق
      setMaintenanceOrders((prev) => prev.filter((o) => o.id !== orderId));

      // إضافة نشاط
      addActivity({
        type: "maintenance",
        description: \`تم حذف أمر الصيانة \${orderToDelete.orderNumber}\`,
      });
    } catch (error) {
      console.error('Failed to delete maintenance order:', error);
      addActivity({
        type: "maintenance",
        description: \`⚠️ فشل في حذف أمر صيانة: \${error instanceof Error ? error.message : String(error)}\`,
      });
      throw error;
    }
  };`;

const fs = require('fs');
const path = require('path');

// Read the store.tsx file
const filePath = path.join(__dirname, 'context', 'store.tsx');
let content = fs.readFileSync(filePath, 'utf8');

// Replace the functions
content = content.replace(/const addMaintenanceOrder = async[\s\S]*?description: `تم إنشاء أمر صيانة \${newOrder\.orderNumber}\.`,\s*}\);(\s*}\);|}\);)/s, addMaintenanceOrderFix);
content = content.replace(/const updateMaintenanceOrder = \([\s\S]*?description: `تم تحديث أمر الصيانة \${updatedOrder\.orderNumber}`[\s\S]*?}\);(\s*}\);|}\);)/s, updateMaintenanceOrderFix);
content = content.replace(/const deleteMaintenanceOrder = \([\s\S]*?description: `تم حذف أمر الصيانة \${orderToDelete\.orderNumber}`[\s\S]*?}\);(\s*}\);|}\);)/s, deleteMaintenanceOrderFix);

// Write the updated content back to the file
fs.writeFileSync(filePath, content);
console.log('Maintenance order functions updated successfully to use API endpoints.');
EOL

# Run the script to update the functions
node maintenance-api-fix.js
