/* تحسينات مظهر صفحة الجرد */

/* تحسينات عامة للصفحة */
.stocktaking-page {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  min-height: 100vh;
  padding: 1rem;
}

/* تحسينات بطاقة الرأس */
.header-card {
  position: relative;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 1.5rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.header-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #3b82f6, #6366f1, #8b5cf6, #ec4899);
  animation: gradient-shift 4s ease-in-out infinite;
}

@keyframes gradient-shift {
  0%, 100% { opacity: 0.8; }
  50% { opacity: 1; }
}

/* تحسينات البطاقات الرئيسية */
.enhanced-stocktake-card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 1.25rem;
  box-shadow: 0 6px 25px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.enhanced-stocktake-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--card-accent, #3b82f6), var(--card-accent-end, #6366f1));
}

.enhanced-stocktake-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
  background: rgba(255, 255, 255, 0.95);
}

/* ألوان مختلفة للبطاقات */
.card-new-stocktake { --card-accent: #3b82f6; --card-accent-end: #1d4ed8; }
.card-inventory-list { --card-accent: #10b981; --card-accent-end: #059669; }
.card-results { --card-accent: #f59e0b; --card-accent-end: #d97706; }

/* تحسينات قسم المعلومات الأساسية */
.info-section {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(99, 102, 241, 0.05) 100%);
  border: 1px solid rgba(59, 130, 246, 0.1);
  border-radius: 0.75rem;
  padding: 1rem;
  position: relative;
  overflow: hidden;
}

.info-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: linear-gradient(to bottom, #3b82f6, #6366f1);
  border-radius: 0 2px 2px 0;
}

/* تحسينات قسم النطاق */
.scope-section {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.05) 0%, rgba(5, 150, 105, 0.05) 100%);
  border: 1px solid rgba(16, 185, 129, 0.1);
  border-radius: 0.75rem;
  padding: 1rem;
  position: relative;
  overflow: hidden;
}

.scope-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: linear-gradient(to bottom, #10b981, #059669);
  border-radius: 0 2px 2px 0;
}

/* تحسينات قسم الإدخال */
.input-section {
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.05) 0%, rgba(217, 119, 6, 0.05) 100%);
  border: 1px solid rgba(245, 158, 11, 0.1);
  border-radius: 0.75rem;
  padding: 1rem;
  position: relative;
  overflow: hidden;
}

.input-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: linear-gradient(to bottom, #f59e0b, #d97706);
  border-radius: 0 2px 2px 0;
}

/* تحسينات حقول الإدخال */
.enhanced-input {
  background: rgba(255, 255, 255, 0.9) !important;
  backdrop-filter: blur(5px);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 0.75rem;
  transition: all 0.3s ease;
  font-family: 'JetBrains Mono', monospace;
  color: #1f2937 !important;
}

.enhanced-input:focus {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);
  border-color: #3b82f6;
  background: rgba(255, 255, 255, 0.98) !important;
  color: #1f2937 !important;
}

.enhanced-input:disabled {
  background: rgba(243, 244, 246, 0.9) !important;
  color: #6b7280 !important;
  opacity: 0.8;
}

/* تحسينات الأزرار */
.enhanced-button {
  position: relative;
  overflow: hidden;
  border-radius: 0.75rem;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  color: #1f2937 !important;
  font-weight: 500;
}

/* أزرار outline */
.enhanced-button[data-variant="outline"],
.enhanced-button.variant-outline {
  background: rgba(255, 255, 255, 0.9) !important;
  border: 1px solid rgba(0, 0, 0, 0.1) !important;
  color: #1f2937 !important;
}

/* أزرار primary */
.enhanced-button[data-variant="primary"],
.enhanced-button.bg-primary {
  background: #3b82f6 !important;
  color: white !important;
}

/* أزرار destructive */
.enhanced-button[data-variant="destructive"],
.enhanced-button.variant-destructive {
  background: #ef4444 !important;
  color: white !important;
}

/* أزرار green */
.enhanced-button.bg-green-600 {
  background: #16a34a !important;
  color: white !important;
}

.enhanced-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.enhanced-button:hover::before {
  left: 100%;
}

.enhanced-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.enhanced-button:hover[data-variant="outline"],
.enhanced-button:hover.variant-outline {
  background: rgba(255, 255, 255, 1) !important;
  color: #1f2937 !important;
}

.enhanced-button:active {
  transform: scale(0.98);
}

/* تحسينات قائمة الأجهزة */
.device-list-container {
  background: rgba(255, 255, 255, 0.9) !important;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 0.75rem;
  max-height: 300px;
  overflow-y: auto;
  position: relative;
  color: #1f2937 !important;
}

.device-list-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, #3b82f6, #6366f1);
  border-radius: 0.75rem 0.75rem 0 0;
}

/* جدول الأجهزة المحسن */
.device-table {
  width: 100%;
  border-collapse: collapse;
}

.device-table th {
  background: rgba(59, 130, 246, 0.1);
  color: #1f2937;
  font-weight: 600;
  font-size: 0.875rem;
  padding: 0.75rem 0.5rem;
  text-align: right;
  border-bottom: 2px solid rgba(59, 130, 246, 0.2);
  position: sticky;
  top: 0;
  z-index: 10;
}

.device-table td {
  padding: 0.5rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  font-size: 0.875rem;
  color: #1f2937 !important;
  vertical-align: middle;
}

.device-table tr:hover {
  background: rgba(59, 130, 246, 0.05) !important;
}

.device-table tr:hover td {
  color: #1f2937 !important;
}

.device-row-number {
  font-weight: 600;
  color: #6b7280 !important;
  text-align: center;
  width: 40px;
}

.device-imei {
  font-family: 'JetBrains Mono', monospace;
  font-weight: 500;
  color: #1f2937 !important;
}

/* تم إزالة أنماط خانة الموديل */

.device-actions {
  width: 50px;
  text-align: center;
}

/* تحسينات بطاقات النتائج */
.result-card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 1rem;
  padding: 1.5rem;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.result-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: var(--result-color, #3b82f6);
  border-radius: 1rem 1rem 0 0;
}

.result-card:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  background: rgba(255, 255, 255, 0.95);
}

/* ألوان مختلفة لبطاقات النتائج */
.result-matching { --result-color: #10b981; }
.result-missing { --result-color: #ef4444; }
.result-extra { --result-color: #f59e0b; }
.result-sold { --result-color: #8b5cf6; }
.result-maintenance { --result-color: #3b82f6; }
.result-summary { --result-color: #6b7280; }

/* تحسينات الشارات */
.enhanced-badge {
  background: rgba(255, 255, 255, 0.95) !important;
  backdrop-filter: blur(5px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 0.5rem;
  padding: 0.25rem 0.75rem;
  font-weight: 600;
  font-size: 0.875rem;
  transition: all 0.2s ease;
  color: inherit !important;
}

.enhanced-badge:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  background: rgba(255, 255, 255, 1) !important;
}

/* تحسينات الأيقونات */
.icon-enhanced {
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
  transition: all 0.3s ease;
}

.icon-enhanced:hover {
  transform: scale(1.1);
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.15));
}

/* تأثيرات التحميل */
.loading-shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer-loading 1.5s infinite;
  border-radius: 0.5rem;
}

@keyframes shimmer-loading {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

/* إصلاحات التباين والوضوح */
.enhanced-stocktake-card,
.info-section,
.scope-section,
.input-section,
.result-card {
  color: #1f2937 !important;
}

.enhanced-stocktake-card h1,
.enhanced-stocktake-card h2,
.enhanced-stocktake-card h3,
.enhanced-stocktake-card h4,
.enhanced-stocktake-card h5,
.enhanced-stocktake-card h6,
.info-section h1,
.info-section h2,
.info-section h3,
.info-section h4,
.info-section h5,
.info-section h6,
.scope-section h1,
.scope-section h2,
.scope-section h3,
.scope-section h4,
.scope-section h5,
.scope-section h6,
.input-section h1,
.input-section h2,
.input-section h3,
.input-section h4,
.input-section h5,
.input-section h6,
.result-card h1,
.result-card h2,
.result-card h3,
.result-card h4,
.result-card h5,
.result-card h6 {
  color: #1f2937 !important;
}

.enhanced-stocktake-card p,
.info-section p,
.scope-section p,
.input-section p,
.result-card p {
  color: #4b5563 !important;
}

.enhanced-stocktake-card label,
.info-section label,
.scope-section label,
.input-section label,
.result-card label {
  color: #374151 !important;
}

/* تحسينات التصميم المتجاوب */
@media (max-width: 768px) {
  .stocktaking-page {
    padding: 0.5rem;
  }

  .enhanced-stocktake-card {
    border-radius: 1rem;
  }

  .info-section,
  .scope-section,
  .input-section {
    padding: 1rem;
    border-radius: 0.75rem;
  }

  .result-card {
    padding: 1rem;
  }

  .device-list-container {
    max-height: 200px;
  }
}

/* تحسينات إضافية للنصوص والتباين */
.enhanced-input::placeholder {
  color: #9ca3af !important;
  opacity: 1;
}

.enhanced-input:focus::placeholder {
  color: #6b7280 !important;
}

/* تحسين ألوان الأزرار */
.enhanced-button {
  color: inherit !important;
  font-weight: 500 !important;
  text-shadow: none !important;
}

.enhanced-button:hover {
  color: inherit !important;
}

/* إصلاحات محددة للأزرار */
button[variant="outline"],
.variant-outline {
  background: rgba(255, 255, 255, 0.95) !important;
  color: #1f2937 !important;
  border: 1px solid #d1d5db !important;
}

button[variant="outline"]:hover,
.variant-outline:hover {
  background: rgba(255, 255, 255, 1) !important;
  color: #1f2937 !important;
  border: 1px solid #9ca3af !important;
}

button[variant="destructive"],
.variant-destructive {
  background: #dc2626 !important;
  color: white !important;
  border: none !important;
}

button[variant="destructive"]:hover,
.variant-destructive:hover {
  background: #b91c1c !important;
  color: white !important;
}

/* أزرار الحجم الصغير */
.enhanced-button[size="sm"] {
  font-size: 0.875rem !important;
  padding: 0.5rem 1rem !important;
}

/* أزرار الحجم الكبير */
.enhanced-button[size="lg"] {
  font-size: 1rem !important;
  padding: 0.75rem 1.5rem !important;
  font-weight: 600 !important;
}

/* تحسين ألوان النصوص في البطاقات */
.header-card * {
  color: #1f2937 !important;
}

.header-card .text-muted-foreground {
  color: #6b7280 !important;
}

/* تحسين ألوان الشارات المحددة */
.enhanced-badge.bg-blue-500\/10 {
  color: #1e40af !important;
}

.enhanced-badge.bg-green-500\/10 {
  color: #166534 !important;
}

.enhanced-badge.bg-red-500\/10 {
  color: #dc2626 !important;
}

.enhanced-badge.bg-yellow-500\/10 {
  color: #ca8a04 !important;
}

.enhanced-badge.bg-purple-500\/10 {
  color: #7c3aed !important;
}

.enhanced-badge.bg-gray-500\/10 {
  color: #374151 !important;
}

/* تحسينات النوافذ المنبثقة */
.enhanced-dialog {
  background: rgba(255, 255, 255, 0.98) !important;
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 1rem;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
}

.enhanced-dialog-header {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(99, 102, 241, 0.05) 100%);
  border-bottom: 1px solid rgba(59, 130, 246, 0.1);
  border-radius: 1rem 1rem 0 0;
  padding: 1.5rem;
  position: relative;
}

.enhanced-dialog-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #3b82f6, #6366f1, #8b5cf6);
  border-radius: 1rem 1rem 0 0;
}

.enhanced-dialog-title {
  color: #1f2937 !important;
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
}

.enhanced-dialog-description {
  color: #6b7280 !important;
  font-size: 0.875rem;
  margin-top: 0.5rem;
}

/* تحسينات جداول النوافذ المنبثقة */
.enhanced-modal-table {
  background: rgba(255, 255, 255, 0.95) !important;
  border-radius: 0.75rem;
  overflow: hidden;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.enhanced-modal-table thead {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(99, 102, 241, 0.1) 100%);
}

.enhanced-modal-table th {
  color: #1f2937 !important;
  font-weight: 600;
  font-size: 0.875rem;
  padding: 0.75rem;
  text-align: right;
  border-bottom: 2px solid rgba(59, 130, 246, 0.2);
}

.enhanced-modal-table td {
  color: #374151 !important;
  font-size: 0.875rem;
  padding: 0.75rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  vertical-align: middle;
}

.enhanced-modal-table tr:hover {
  background: rgba(59, 130, 246, 0.05) !important;
}

.enhanced-modal-table tr:hover td {
  color: #1f2937 !important;
}

/* تحسينات منطقة التمرير */
.enhanced-scroll-area {
  max-height: 400px; /* حوالي 6 صفوف */
  overflow-y: auto;
  border-radius: 0.5rem;
  scrollbar-width: thin;
  scrollbar-color: rgba(59, 130, 246, 0.3) transparent;
}

.enhanced-scroll-area::-webkit-scrollbar {
  width: 8px;
}

.enhanced-scroll-area::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 4px;
}

.enhanced-scroll-area::-webkit-scrollbar-thumb {
  background: rgba(59, 130, 246, 0.3);
  border-radius: 4px;
  transition: background 0.2s ease;
}

.enhanced-scroll-area::-webkit-scrollbar-thumb:hover {
  background: rgba(59, 130, 246, 0.5);
}

/* تحسينات أزرار النوافذ المنبثقة */
.enhanced-modal-button {
  background: rgba(255, 255, 255, 0.9) !important;
  border: 1px solid rgba(0, 0, 0, 0.1) !important;
  color: #1f2937 !important;
  border-radius: 0.5rem;
  padding: 0.5rem 1rem;
  font-weight: 500;
  transition: all 0.2s ease;
}

.enhanced-modal-button:hover {
  background: rgba(255, 255, 255, 1) !important;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.enhanced-modal-button.primary {
  background: #3b82f6 !important;
  color: white !important;
  border: none !important;
}

.enhanced-modal-button.primary:hover {
  background: #2563eb !important;
}

.enhanced-modal-button.destructive {
  background: #ef4444 !important;
  color: white !important;
  border: none !important;
}

.enhanced-modal-button.destructive:hover {
  background: #dc2626 !important;
}

/* ===== الوضع الليلي (Dark Mode) ===== */

/* متغيرات الألوان للوضع الليلي */
.dark-mode {
  --bg-primary: #0f172a;
  --bg-secondary: #1e293b;
  --bg-tertiary: #334155;
  --text-primary: #f8fafc;
  --text-secondary: #cbd5e1;
  --text-muted: #94a3b8;
  --border-color: #475569;
  --accent-primary: #3b82f6;
  --accent-success: #10b981;
  --accent-warning: #f59e0b;
  --accent-danger: #ef4444;
}

/* خلفية الصفحة في الوضع الليلي */
.dark-mode .page-container {
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
  color: var(--text-primary);
}

/* البطاقات في الوضع الليلي */
.dark-mode .enhanced-stocktake-card,
.dark-mode .result-card,
.dark-mode .info-section,
.dark-mode .scope-section,
.dark-mode .input-section {
  background: rgba(30, 41, 59, 0.9) !important;
  backdrop-filter: blur(15px);
  border: 1px solid rgba(71, 85, 105, 0.3) !important;
  color: var(--text-primary) !important;
}

.dark-mode .enhanced-stocktake-card::before,
.dark-mode .result-card::before,
.dark-mode .info-section::before,
.dark-mode .scope-section::before,
.dark-mode .input-section::before {
  background: linear-gradient(90deg, var(--accent-primary), #6366f1, #8b5cf6);
}

/* النصوص في الوضع الليلي */
.dark-mode .enhanced-stocktake-card h1,
.dark-mode .enhanced-stocktake-card h2,
.dark-mode .enhanced-stocktake-card h3,
.dark-mode .enhanced-stocktake-card h4,
.dark-mode .enhanced-stocktake-card h5,
.dark-mode .enhanced-stocktake-card h6,
.dark-mode .info-section h1,
.dark-mode .info-section h2,
.dark-mode .info-section h3,
.dark-mode .info-section h4,
.dark-mode .info-section h5,
.dark-mode .info-section h6 {
  color: var(--text-primary) !important;
}

.dark-mode .enhanced-stocktake-card p,
.dark-mode .info-section p,
.dark-mode .enhanced-stocktake-card label,
.dark-mode .info-section label {
  color: var(--text-secondary) !important;
}

/* حقول الإدخال في الوضع الليلي */
.dark-mode .enhanced-input {
  background: rgba(51, 65, 85, 0.9) !important;
  border: 2px solid rgba(71, 85, 105, 0.5) !important;
  color: var(--text-primary) !important;
}

.dark-mode .enhanced-input:focus {
  background: rgba(51, 65, 85, 0.95) !important;
  border-color: var(--accent-primary) !important;
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.25);
}

.dark-mode .enhanced-input:disabled {
  background: rgba(71, 85, 105, 0.5) !important;
  color: var(--text-muted) !important;
}

.dark-mode .enhanced-input::placeholder {
  color: var(--text-muted) !important;
}

/* الأزرار في الوضع الليلي */
.dark-mode .enhanced-button {
  background: rgba(51, 65, 85, 0.9) !important;
  border: 1px solid rgba(71, 85, 105, 0.5) !important;
  color: var(--text-primary) !important;
}

.dark-mode .enhanced-button:hover {
  background: rgba(51, 65, 85, 1) !important;
  border-color: rgba(71, 85, 105, 0.8) !important;
}

.dark-mode .enhanced-button.variant-outline {
  background: rgba(30, 41, 59, 0.9) !important;
  border: 1px solid rgba(71, 85, 105, 0.5) !important;
  color: var(--text-primary) !important;
}

.dark-mode .enhanced-button.variant-outline:hover {
  background: rgba(51, 65, 85, 0.9) !important;
}

/* الشارات في الوضع الليلي */
.dark-mode .enhanced-badge {
  background: rgba(51, 65, 85, 0.9) !important;
  border: 1px solid rgba(71, 85, 105, 0.5) !important;
  color: var(--text-primary) !important;
}

.dark-mode .enhanced-badge.bg-blue-500\/10 {
  background: rgba(59, 130, 246, 0.2) !important;
  color: #93c5fd !important;
  border-color: rgba(59, 130, 246, 0.3) !important;
}

.dark-mode .enhanced-badge.bg-green-500\/10 {
  background: rgba(16, 185, 129, 0.2) !important;
  color: #6ee7b7 !important;
  border-color: rgba(16, 185, 129, 0.3) !important;
}

.dark-mode .enhanced-badge.bg-red-500\/10 {
  background: rgba(239, 68, 68, 0.2) !important;
  color: #fca5a5 !important;
  border-color: rgba(239, 68, 68, 0.3) !important;
}

.dark-mode .enhanced-badge.bg-yellow-500\/10 {
  background: rgba(245, 158, 11, 0.2) !important;
  color: #fcd34d !important;
  border-color: rgba(245, 158, 11, 0.3) !important;
}

.dark-mode .enhanced-badge.bg-purple-500\/10 {
  background: rgba(139, 92, 246, 0.2) !important;
  color: #c4b5fd !important;
  border-color: rgba(139, 92, 246, 0.3) !important;
}

.dark-mode .enhanced-badge.bg-gray-500\/10 {
  background: rgba(107, 114, 128, 0.2) !important;
  color: #d1d5db !important;
  border-color: rgba(107, 114, 128, 0.3) !important;
}

/* العناصر الخاصة في الوضع الليلي */
.dark-mode .device-item {
  background: rgba(51, 65, 85, 0.9) !important;
  border: 1px solid rgba(71, 85, 105, 0.5) !important;
  color: var(--text-primary) !important;
}

.dark-mode .device-item:hover {
  background: rgba(51, 65, 85, 1) !important;
}

/* النوافذ المنبثقة في الوضع الليلي */
.dark-mode .enhanced-dialog {
  background: rgba(30, 41, 59, 0.98) !important;
  border: 1px solid rgba(71, 85, 105, 0.3) !important;
}

.dark-mode .enhanced-dialog-header {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(99, 102, 241, 0.1) 100%);
  border-bottom: 1px solid rgba(71, 85, 105, 0.3) !important;
}

.dark-mode .enhanced-dialog-title {
  color: var(--text-primary) !important;
}

.dark-mode .enhanced-dialog-description {
  color: var(--text-secondary) !important;
}

/* جداول النوافذ المنبثقة في الوضع الليلي */
.dark-mode .enhanced-modal-table {
  background: rgba(30, 41, 59, 0.95) !important;
  border: 1px solid rgba(71, 85, 105, 0.3) !important;
}

.dark-mode .enhanced-modal-table thead {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.15) 0%, rgba(99, 102, 241, 0.15) 100%);
}

.dark-mode .enhanced-modal-table th {
  color: var(--text-primary) !important;
  border-bottom: 2px solid rgba(71, 85, 105, 0.4) !important;
}

.dark-mode .enhanced-modal-table td {
  color: var(--text-secondary) !important;
  border-bottom: 1px solid rgba(71, 85, 105, 0.2) !important;
}

.dark-mode .enhanced-modal-table tr:hover {
  background: rgba(59, 130, 246, 0.1) !important;
}

.dark-mode .enhanced-modal-table tr:hover td {
  color: var(--text-primary) !important;
}

/* أزرار النوافذ المنبثقة في الوضع الليلي */
.dark-mode .enhanced-modal-button {
  background: rgba(51, 65, 85, 0.9) !important;
  border: 1px solid rgba(71, 85, 105, 0.5) !important;
  color: var(--text-primary) !important;
}

.dark-mode .enhanced-modal-button:hover {
  background: rgba(51, 65, 85, 1) !important;
}

.dark-mode .enhanced-modal-button.primary {
  background: var(--accent-primary) !important;
  color: white !important;
}

.dark-mode .enhanced-modal-button.primary:hover {
  background: #2563eb !important;
}

.dark-mode .enhanced-modal-button.destructive {
  background: var(--accent-danger) !important;
  color: white !important;
}

.dark-mode .enhanced-modal-button.destructive:hover {
  background: #dc2626 !important;
}

/* شريط التمرير في الوضع الليلي */
.dark-mode .enhanced-scroll-area::-webkit-scrollbar-track {
  background: rgba(71, 85, 105, 0.3);
}

.dark-mode .enhanced-scroll-area::-webkit-scrollbar-thumb {
  background: rgba(59, 130, 246, 0.5);
}

.dark-mode .enhanced-scroll-area::-webkit-scrollbar-thumb:hover {
  background: rgba(59, 130, 246, 0.7);
}

/* تحسينات إضافية للوضع الليلي */

/* النصوص المتدرجة في الوضع الليلي */
.dark-mode .bg-gradient-to-r {
  background: linear-gradient(to right, #93c5fd, #ddd6fe) !important;
  -webkit-background-clip: text !important;
  background-clip: text !important;
  color: transparent !important;
}

/* الأيقونات المحسنة في الوضع الليلي */
.dark-mode .icon-enhanced {
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

/* تحسين التباين للنصوص المهمة */
.dark-mode .text-muted-foreground {
  color: var(--text-secondary) !important;
}

.dark-mode .text-gray-500,
.dark-mode .text-gray-600,
.dark-mode .text-gray-700 {
  color: var(--text-secondary) !important;
}

.dark-mode .text-gray-800,
.dark-mode .text-gray-900 {
  color: var(--text-primary) !important;
}

/* تحسين الحدود في الوضع الليلي */
.dark-mode .border-gray-300,
.dark-mode .border-gray-200 {
  border-color: rgba(71, 85, 105, 0.5) !important;
}

/* تحسين الخلفيات الشفافة */
.dark-mode .bg-white {
  background: rgba(30, 41, 59, 0.9) !important;
}

.dark-mode .bg-gray-50 {
  background: rgba(51, 65, 85, 0.5) !important;
}

.dark-mode .bg-gray-100 {
  background: rgba(51, 65, 85, 0.7) !important;
}

/* تحسين أزرار الإغلاق والتحكم */
.dark-mode [data-radix-dialog-close] {
  color: var(--text-secondary) !important;
}

.dark-mode [data-radix-dialog-close]:hover {
  color: var(--text-primary) !important;
  background: rgba(71, 85, 105, 0.3) !important;
}

/* تحسين القوائم المنسدلة */
.dark-mode [data-radix-select-content] {
  background: rgba(30, 41, 59, 0.98) !important;
  border: 1px solid rgba(71, 85, 105, 0.5) !important;
  color: var(--text-primary) !important;
}

.dark-mode [data-radix-select-item] {
  color: var(--text-secondary) !important;
}

.dark-mode [data-radix-select-item]:hover {
  background: rgba(59, 130, 246, 0.2) !important;
  color: var(--text-primary) !important;
}

/* تحسين التنبيهات والحوارات */
.dark-mode [data-radix-alert-dialog-content] {
  background: rgba(30, 41, 59, 0.98) !important;
  border: 1px solid rgba(71, 85, 105, 0.5) !important;
  color: var(--text-primary) !important;
}

/* تحسين الجداول العادية */
.dark-mode table {
  color: var(--text-secondary) !important;
}

.dark-mode th {
  color: var(--text-primary) !important;
  border-color: rgba(71, 85, 105, 0.5) !important;
}

.dark-mode td {
  border-color: rgba(71, 85, 105, 0.3) !important;
}

/* تحسين الانتقالات السلسة */
.dark-mode * {
  transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease;
}

/* تحسينات خاصة لعناصر الصفحة */

/* تحسين بطاقة الرأس في الوضع الليلي */
.dark-mode .header-card {
  background: rgba(30, 41, 59, 0.95) !important;
  border: 1px solid rgba(71, 85, 105, 0.3) !important;
}

/* تحسين البطاقات المخصصة */
.dark-mode .card-new-stocktake::before {
  background: linear-gradient(90deg, #10b981, #059669) !important;
}

.dark-mode .card-results::before {
  background: linear-gradient(90deg, #3b82f6, #1d4ed8) !important;
}

/* تحسين النماذج والحقول */
.dark-mode .space-y-2 > label,
.dark-mode .space-y-3 > label {
  color: var(--text-secondary) !important;
}

/* تحسين الأقسام المختلفة */
.dark-mode .scope-section {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(5, 150, 105, 0.1) 100%) !important;
  border: 1px solid rgba(16, 185, 129, 0.2) !important;
}

.dark-mode .scope-section::before {
  background: linear-gradient(to bottom, #10b981, #059669) !important;
}

.dark-mode .input-section {
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.1) 0%, rgba(217, 119, 6, 0.1) 100%) !important;
  border: 1px solid rgba(245, 158, 11, 0.2) !important;
}

.dark-mode .input-section::before {
  background: linear-gradient(to bottom, #f59e0b, #d97706) !important;
}

/* تحسين عناصر النتائج */
.dark-mode .result-card {
  background: rgba(30, 41, 59, 0.9) !important;
  border: 1px solid rgba(71, 85, 105, 0.3) !important;
}

/* تحسين الأزرار الخاصة */
.dark-mode .enhanced-button.bg-primary {
  background: var(--accent-primary) !important;
  color: white !important;
}

.dark-mode .enhanced-button.bg-primary:hover {
  background: #2563eb !important;
}

.dark-mode .enhanced-button.bg-green-600 {
  background: var(--accent-success) !important;
  color: white !important;
}

.dark-mode .enhanced-button.bg-green-600:hover {
  background: #059669 !important;
}

/* تحسين العناصر التفاعلية */
.dark-mode [class*="hover:bg-gray-50"]:hover {
  background: rgba(51, 65, 85, 0.5) !important;
}

.dark-mode [class*="hover:bg-primary/10"]:hover {
  background: rgba(59, 130, 246, 0.2) !important;
}

/* تحسين النصوص الخاصة */
.dark-mode .text-primary {
  color: var(--accent-primary) !important;
}

.dark-mode .text-green-500,
.dark-mode .text-green-600 {
  color: #6ee7b7 !important;
}

.dark-mode .text-red-500,
.dark-mode .text-red-600 {
  color: #fca5a5 !important;
}

.dark-mode .text-yellow-500,
.dark-mode .text-yellow-600 {
  color: #fcd34d !important;
}

.dark-mode .text-blue-500,
.dark-mode .text-blue-600 {
  color: #93c5fd !important;
}

/* تحسين الحالات الخاصة */
.dark-mode .font-mono {
  color: var(--text-primary) !important;
  background: rgba(51, 65, 85, 0.3) !important;
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
}

/* تحسين التركيز والتحديد */
.dark-mode *:focus {
  outline-color: var(--accent-primary) !important;
}

.dark-mode ::selection {
  background: rgba(59, 130, 246, 0.3) !important;
  color: var(--text-primary) !important;
}

/* تحسينات خاصة لزر الوضع الليلي */
.dark-mode-toggle {
  position: relative;
  overflow: hidden;
}

.dark-mode-toggle::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s;
}

.dark-mode-toggle:hover::before {
  left: 100%;
}

/* تحسين الانتقال السلس للوضع الليلي */
html {
  transition: background-color 0.3s ease, color 0.3s ease;
}

body {
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* تحسين العناصر المخفية في الوضع الليلي */
.dark-mode .sr-only {
  color: var(--text-primary) !important;
}

/* تحسين الحدود والفواصل */
.dark-mode .border-t,
.dark-mode .border-b,
.dark-mode .border-l,
.dark-mode .border-r {
  border-color: rgba(71, 85, 105, 0.3) !important;
}

.dark-mode .divide-y > * + * {
  border-color: rgba(71, 85, 105, 0.3) !important;
}

/* تحسين الظلال في الوضع الليلي */
.dark-mode .shadow,
.dark-mode .shadow-sm,
.dark-mode .shadow-md,
.dark-mode .shadow-lg,
.dark-mode .shadow-xl {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2) !important;
}

/* تحسين الخلفيات المتدرجة في الوضع الليلي */
.dark-mode .bg-gradient-to-br {
  background: linear-gradient(to bottom right, rgba(30, 41, 59, 0.8), rgba(51, 65, 85, 0.8)) !important;
}

.dark-mode [class*="from-primary/20"] {
  --tw-gradient-from: rgba(59, 130, 246, 0.3) !important;
}

.dark-mode [class*="to-primary/10"] {
  --tw-gradient-to: rgba(59, 130, 246, 0.2) !important;
}

/* تحسينات الطباعة */
@media print {
  .enhanced-stocktake-card,
  .result-card,
  .info-section,
  .scope-section,
  .input-section {
    background: white !important;
    box-shadow: none !important;
    border: 1px solid #e5e7eb !important;
    color: #000 !important;
  }

  .enhanced-stocktake-card::before,
  .result-card::before,
  .info-section::before,
  .scope-section::before,
  .input-section::before {
    display: none !important;
  }

  .enhanced-input,
  .enhanced-badge,
  .device-item {
    color: #000 !important;
    background: white !important;
  }
}
