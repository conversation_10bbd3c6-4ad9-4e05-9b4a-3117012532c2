{"timestamp": "2025-08-01T00:24:26.693Z", "database": {"connection": true, "models": {"User": {"exists": true, "count": 2, "queryTime": 71}, "Warehouse": {"exists": true, "count": 1, "queryTime": 4}, "Client": {"exists": true, "count": 1, "queryTime": 8}, "Supplier": {"exists": true, "count": 1, "queryTime": 3}, "Device": {"exists": true, "count": 1, "queryTime": 5}, "DeviceModel": {"exists": true, "count": 1, "queryTime": 6}, "SupplyOrder": {"exists": true, "count": 1, "queryTime": 3}, "SupplyOrderItem": {"exists": true, "count": 1, "queryTime": 6}, "Sale": {"exists": true, "count": 1, "queryTime": 5}, "SaleItem": {"exists": true, "count": 1, "queryTime": 6}, "Return": {"exists": true, "count": 0, "queryTime": 3}, "ReturnItem": {"exists": true, "count": 0, "queryTime": 3}, "EvaluationOrder": {"exists": true, "count": 0, "queryTime": 2}, "EvaluationOrderItem": {"exists": true, "count": 0, "queryTime": 4}, "MaintenanceOrder": {"exists": true, "count": 0, "queryTime": 2}, "MaintenanceOrderItem": {"exists": true, "count": 0, "queryTime": 2}, "DeliveryOrder": {"exists": true, "count": 0, "queryTime": 3}, "DeliveryOrderItem": {"exists": true, "count": 0, "queryTime": 2}, "MaintenanceReceiptOrder": {"exists": true, "count": 0, "queryTime": 3}, "MaintenanceReceiptOrderItem": {"exists": true, "count": 0, "queryTime": 3}, "EmployeeRequest": {"exists": true, "count": 0, "queryTime": 2}, "InternalMessage": {"exists": true, "count": 0, "queryTime": 3}, "AuditLog": {"exists": true, "count": 6, "queryTime": 8}}, "relationships": {"SupplyOrder-SupplyOrderItem": {"ordersCount": 1, "itemsCount": 1, "isValid": true}, "Sale-SaleItem": {"salesCount": 1, "itemsCount": 1, "isValid": true}, "Return-ReturnItem": {"returnsCount": 0, "itemsCount": 0, "isValid": true}}, "dataIntegrity": {"duplicateDevices": 0}, "performance": {"Simple Count Query": 2, "Complex Join Query": 3, "Aggregation Query": 16}}, "apis": {"endpoints": ["/api/warehouses-simple", "/api/clients-simple", "/api/suppliers-simple", "/api/supply", "/api/sales", "/api/returns", "/api/devices", "/api/users"], "status": {"/api/warehouses-simple": "مو<PERSON><PERSON><PERSON>", "/api/clients-simple": "مو<PERSON><PERSON><PERSON>", "/api/suppliers-simple": "مو<PERSON><PERSON><PERSON>", "/api/supply": "مو<PERSON><PERSON><PERSON>", "/api/sales": "مو<PERSON><PERSON><PERSON>", "/api/returns": "مو<PERSON><PERSON><PERSON>", "/api/devices": "مو<PERSON><PERSON><PERSON>", "/api/users": "مو<PERSON><PERSON><PERSON>"}}, "errors": [], "warnings": [], "recommendations": ["✅ النظام يعمل بشكل ممتاز"], "summary": {"totalRecords": 17, "totalQueryTime": 157}}