import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { requireAuth } from '@/lib/auth';
import { executeInTransaction, executeInLongTransaction, createAuditLogInTransaction, generateUniqueId } from '@/lib/transaction-utils';

export async function GET(request: NextRequest) {
  try {
    // التحقق من التفويض
    const authResult = await requireAuth(request, 'user');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const supplyOrders = await prisma.supplyOrder.findMany({
      include: {
        items: true
      },
      orderBy: { id: 'desc' }
    });
    return NextResponse.json(supplyOrders);
  } catch (error) {
    console.error('Failed to fetch supply orders:', error);
    return NextResponse.json({ error: 'Failed to fetch supply orders' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    // التحقق من التفويض
    const authResult = await requireAuth(request, 'user');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const newOrder = await request.json();

    // Basic validation
    if (!newOrder.supplierId || !newOrder.items || !Array.isArray(newOrder.items) || newOrder.items.length === 0) {
      return NextResponse.json(
        { error: 'Supplier ID and items are required' },
        { status: 400 }
      );
    }

    // تنفيذ العملية داخل معاملة طويلة للتعامل مع الأجهزة الكثيرة
    const result = await executeInLongTransaction(async (tx) => {
      // استخدام رقم الأمر المُرسل من الواجهة الأمامية، أو إنشاء رقم جديد إذا لم يكن متوفراً
      let supplyOrderId = newOrder.supplyOrderId;
      if (!supplyOrderId) {
        supplyOrderId = await generateUniqueId(tx, 'supplyOrder', 'SUP-');
      } else {
        // التحقق من عدم وجود رقم مكرر
        const existingOrder = await tx.supplyOrder.findUnique({
          where: { supplyOrderId }
        });
        if (existingOrder) {
          supplyOrderId = await generateUniqueId(tx, 'supplyOrder', 'SUP-');
        }
      }

      // Create the order in the database
      const order = await tx.supplyOrder.create({
        data: {
          supplyOrderId,
          supplierId: newOrder.supplierId,
          invoiceNumber: newOrder.invoiceNumber || null, // يمكن أن يكون null الآن
          supplyDate: newOrder.supplyDate,
          warehouseId: newOrder.warehouseId || null,
          employeeName: newOrder.employeeName || authResult.user!.username,
          notes: newOrder.notes || '',
          invoiceFileName: newOrder.invoiceFileName || null,
          referenceNumber: newOrder.referenceNumber || null,
          status: newOrder.status || 'completed'
        }
      });

      // Create supply order items
      if (newOrder.items && Array.isArray(newOrder.items)) {
        for (const item of newOrder.items) {
          await tx.supplyOrderItem.create({
            data: {
              supplyOrderId: order.id,
              imei: item.imei || '',
              model: item.model || '',
              manufacturer: item.manufacturer || '',
              condition: item.condition || 'جديد'
            }
          });
        }
      }

      // Add devices to database in batches to handle large quantities
      if (newOrder.items && Array.isArray(newOrder.items)) {
        const BATCH_SIZE = 50; // معالجة 50 جهاز في كل دفعة
        const validItems = newOrder.items.filter(item => item.imei);

        console.log(`Processing ${validItems.length} devices in batches of ${BATCH_SIZE}`);

        // تقسيم الأجهزة إلى دفعات
        for (let i = 0; i < validItems.length; i += BATCH_SIZE) {
          const batch = validItems.slice(i, i + BATCH_SIZE);
          console.log(`Processing batch ${Math.floor(i / BATCH_SIZE) + 1}/${Math.ceil(validItems.length / BATCH_SIZE)}`);

          try {
            // الحصول على الأجهزة الموجودة في هذه الدفعة
            const existingDevices = await tx.device.findMany({
              where: {
                id: {
                  in: batch.map(item => item.imei)
                }
              },
              select: { id: true }
            });

            const existingDeviceIds = new Set(existingDevices.map(d => d.id));

            // إنشاء الأجهزة الجديدة فقط
            const newDevices = batch
              .filter(item => !existingDeviceIds.has(item.imei))
              .map(item => ({
                id: item.imei,
                model: `${item.manufacturer} ${item.model}`,
                status: 'متاح للبيع',
                storage: 'N/A',
                price: 0,
                condition: item.condition || 'جديد',
                warehouseId: newOrder.warehouseId,
                supplierId: newOrder.supplierId
              }));

            if (newDevices.length > 0) {
              await tx.device.createMany({
                data: newDevices,
                skipDuplicates: true // تجاهل المكررات إذا حدثت
              });
              console.log(`Created ${newDevices.length} new devices in this batch`);
            }

          } catch (batchError) {
            console.error(`Failed to process batch ${Math.floor(i / BATCH_SIZE) + 1}:`, batchError);
            // في حالة فشل دفعة، نحاول معالجة الأجهزة واحد تلو الآخر
            for (const item of batch) {
              try {
                const existingDevice = await tx.device.findUnique({
                  where: { id: item.imei }
                });

                if (!existingDevice) {
                  await tx.device.create({
                    data: {
                      id: item.imei,
                      model: `${item.manufacturer} ${item.model}`,
                      status: 'متاح للبيع',
                      storage: 'N/A',
                      price: 0,
                      condition: item.condition || 'جديد',
                      warehouseId: newOrder.warehouseId,
                      supplierId: newOrder.supplierId
                    }
                  });
                }
              } catch (deviceError) {
                console.error(`Failed to create device ${item.imei}:`, deviceError);
              }
            }
          }
        }
      }

      // إنشاء audit log
      await createAuditLogInTransaction(tx, {
        userId: authResult.user!.id,
        username: authResult.user!.username,
        operation: 'CREATE',
        details: `Created supply order: ${order.supplyOrderId}`
      });

      // Return order with items
      const orderWithItems = await tx.supplyOrder.findUnique({
        where: { id: order.id },
        include: { items: true }
      });

      return orderWithItems;
    });

    return NextResponse.json(result, { status: 201 });
  } catch (error) {
    console.error('Failed to create supply order:', error);

    // تسجيل تفصيلي للخطأ
    let errorMessage = 'Failed to create supply order';
    let errorDetails = '';

    if (error instanceof Error) {
      errorDetails = error.message;
      console.error('Error details:', error.stack);
    } else {
      errorDetails = String(error);
    }

    return NextResponse.json({
      error: errorMessage,
      details: errorDetails,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

export async function PUT(request: NextRequest) {
  try {
    // التحقق من التفويض
    const authResult = await requireAuth(request, 'user');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const updatedOrder = await request.json();

    if (!updatedOrder.id) {
      return NextResponse.json({ error: 'Supply order ID is required' }, { status: 400 });
    }

    // تنفيذ العملية داخل معاملة
    const result = await executeInTransaction(async (tx) => {
      // Check if order exists
      const existingOrder = await tx.supplyOrder.findUnique({
        where: { id: updatedOrder.id },
        include: { items: true }
      });

      if (!existingOrder) {
        throw new Error('Supply order not found');
      }

      // Update the order
      const order = await tx.supplyOrder.update({
        where: { id: updatedOrder.id },
        data: {
          supplierId: updatedOrder.supplierId,
          invoiceNumber: updatedOrder.invoiceNumber,
          supplyDate: updatedOrder.supplyDate,
          warehouseId: updatedOrder.warehouseId,
          employeeName: updatedOrder.employeeName,
          notes: updatedOrder.notes || '',
          invoiceFileName: updatedOrder.invoiceFileName,
          referenceNumber: updatedOrder.referenceNumber,
          status: updatedOrder.status || 'completed'
        }
      });

      // Delete existing items
      await tx.supplyOrderItem.deleteMany({
        where: { supplyOrderId: updatedOrder.id }
      });

      // Create new items
      if (updatedOrder.items && Array.isArray(updatedOrder.items)) {
        for (const item of updatedOrder.items) {
          await tx.supplyOrderItem.create({
            data: {
              supplyOrderId: order.id,
              imei: item.imei || '',
              model: item.model || '',
              manufacturer: item.manufacturer || '',
              condition: item.condition || 'جديد'
            }
          });
        }
      }

      // إنشاء audit log
      await createAuditLogInTransaction(tx, {
        userId: authResult.user!.id,
        username: authResult.user!.username,
        operation: 'UPDATE',
        details: `Updated supply order: ${order.supplyOrderId}`
      });

      // Return order with items
      const orderWithItems = await tx.supplyOrder.findUnique({
        where: { id: order.id },
        include: { items: true }
      });

      return orderWithItems;
    });

    return NextResponse.json(result);
  } catch (error) {
    console.error('Failed to update supply order:', error);

    if (error instanceof Error && error.message === 'Supply order not found') {
      return NextResponse.json({ error: 'Supply order not found' }, { status: 404 });
    }

    return NextResponse.json({ error: 'Failed to update supply order' }, { status: 500 });
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // التحقق من التفويض
    const authResult = await requireAuth(request, 'manager');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const { id } = await request.json();

    if (!id) {
      return NextResponse.json({ error: 'Supply order ID is required' }, { status: 400 });
    }

    // تنفيذ العملية داخل معاملة
    const result = await executeInTransaction(async (tx) => {
      // Check if order exists
      const existingOrder = await tx.supplyOrder.findUnique({
        where: { id }
      });

      if (!existingOrder) {
        throw new Error('Supply order not found');
      }

      // استخراج الأجهزة من أمر التوريد لحذفها من المخزون
      let items: any[] = [];
      try {
        items = typeof existingOrder.items === 'string' 
          ? JSON.parse(existingOrder.items) 
          : Array.isArray(existingOrder.items) 
            ? existingOrder.items 
            : [];
      } catch (error) {
        console.error('Error parsing supply order items:', error);
      }

      // حذف الأجهزة من جدول Device
      const deviceIMEIs = items.map(item => item.imei).filter(Boolean);
      if (deviceIMEIs.length > 0) {
        await tx.device.deleteMany({
          where: {
            id: {
              in: deviceIMEIs
            }
          }
        });
      }

      // Delete the order
      await tx.supplyOrder.delete({
        where: { id }
      });

      // إنشاء audit log
      await createAuditLogInTransaction(tx, {
        userId: authResult.user!.id,
        username: authResult.user!.username,
        operation: 'DELETE',
        details: `Deleted supply order: ${existingOrder.supplyOrderId} and ${deviceIMEIs.length} devices`
      });

      return { 
        message: 'Supply order deleted successfully',
        deletedDevices: deviceIMEIs.length
      };
    });

    return NextResponse.json(result);
  } catch (error) {
    console.error('Failed to delete supply order:', error);

    if (error instanceof Error && error.message === 'Supply order not found') {
      return NextResponse.json({ error: 'Supply order not found' }, { status: 404 });
    }

    return NextResponse.json({ error: 'Failed to delete supply order' }, { status: 500 });
  }
}
