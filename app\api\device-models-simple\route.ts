import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

export async function GET(request: NextRequest) {
  try {
    const deviceModels = await prisma.deviceModel.findMany({
      orderBy: { id: 'desc' }
    });
    
    // تحويل BigInt إلى string للـ JSON serialization
    const serializedModels = deviceModels.map(model => ({
      ...model,
      manufacturerId: model.manufacturerId.toString()
    }));
    
    return NextResponse.json(serializedModels);
  } catch (error) {
    console.error('Failed to fetch device models:', error);
    return NextResponse.json({ error: 'Failed to fetch device models' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const newModel = await request.json();

    // Basic validation
    if (!newModel.name || !newModel.manufacturerId) {
      return NextResponse.json(
        { error: 'Name and manufacturer ID are required' },
        { status: 400 }
      );
    }

    // Check if model already exists
    const existingModel = await prisma.deviceModel.findFirst({
      where: {
        name: newModel.name,
        manufacturerId: BigInt(newModel.manufacturerId)
      }
    });

    if (existingModel) {
      return NextResponse.json(
        { error: 'Device model already exists' },
        { status: 409 }
      );
    }

    // Create the model in the database
    const model = await prisma.deviceModel.create({
      data: {
        name: newModel.name,
        manufacturerId: BigInt(newModel.manufacturerId),
        category: newModel.category || 'هاتف ذكي'
      }
    });

    // تحويل BigInt إلى string للـ JSON serialization
    const serializedModel = {
      ...model,
      manufacturerId: model.manufacturerId.toString()
    };
    
    return NextResponse.json(serializedModel, { status: 201 });
  } catch (error) {
    console.error('Failed to create device model:', error);
    return NextResponse.json({ error: 'Failed to create device model' }, { status: 500 });
  }
}

export async function PUT(request: NextRequest) {
  try {
    const updatedModel = await request.json();

    if (!updatedModel.id) {
      return NextResponse.json(
        { error: 'Model ID is required' },
        { status: 400 }
      );
    }

    const model = await prisma.deviceModel.update({
      where: { id: updatedModel.id },
      data: {
        ...(updatedModel.name && { name: updatedModel.name }),
        ...(updatedModel.manufacturerId && { manufacturerId: BigInt(updatedModel.manufacturerId) }),
        ...(updatedModel.category && { category: updatedModel.category })
      }
    });

    // تحويل BigInt إلى string للـ JSON serialization
    const serializedModel = {
      ...model,
      manufacturerId: model.manufacturerId.toString()
    };

    return NextResponse.json(serializedModel);
  } catch (error) {
    console.error('Failed to update device model:', error);
    return NextResponse.json({ error: 'Failed to update device model' }, { status: 500 });
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { id } = await request.json();

    if (!id) {
      return NextResponse.json(
        { error: 'Model ID is required' },
        { status: 400 }
      );
    }

    await prisma.deviceModel.delete({
      where: { id }
    });

    return NextResponse.json({ message: 'Device model deleted successfully' });
  } catch (error) {
    console.error('Failed to delete device model:', error);
    return NextResponse.json({ error: 'Failed to delete device model' }, { status: 500 });
  }
}
