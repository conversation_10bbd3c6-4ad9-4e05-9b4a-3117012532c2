// إصلاح صلاحيات المدير - التأكد من أن المدير لديه جميع الصلاحيات

import { permissionPages } from './lib/types.js';

console.log('🔧 إصلاح صلاحيات المدير');
console.log('=======================');

// إنشاء صلاحيات كاملة للمدير
function createFullAdminPermissions() {
  const permissions = {};
  
  // قائمة جميع الأقسام المتاحة
  const allSections = [
    'dashboard',
    'clients', 
    'warehouses',
    'inventory',
    'supply',
    'sales',
    'requests',
    'returns',
    'maintenance',
    'maintenanceTransfer',
    'warehouseTransfer',
    'grading',
    'track',
    'stocktaking',
    'acceptDevices',
    'messaging',
    'reports',
    'users',
    'settings',
    'pricing'
  ];
  
  // منح جميع الصلاحيات لكل قسم
  allSections.forEach(section => {
    permissions[section] = {
      view: true,
      create: true,
      edit: true,
      delete: true,
      viewAll: true,
      manage: [1, 2, 3], // صلاحية إدارة جميع المخازن
      acceptWithoutWarranty: true
    };
  });
  
  return permissions;
}

// كود إصلاح المدير
const adminFixCode = `
// إصلاح صلاحيات المدير في Store
const fixAdminPermissions = () => {
  const adminPermissions = createFullAdminPermissions();
  
  // تحديث المستخدم المدير
  const adminUser = {
    id: 1,
    name: 'مدير النظام',
    username: 'admin',
    email: '<EMAIL>',
    permissions: adminPermissions,
    status: 'Active'
  };
  
  // حفظ في localStorage إذا لزم الأمر
  if (typeof window !== 'undefined') {
    const users = JSON.parse(localStorage.getItem('users') || '[]');
    const adminIndex = users.findIndex(u => u.id === 1 || u.username === 'admin');
    
    if (adminIndex >= 0) {
      users[adminIndex] = { ...users[adminIndex], permissions: adminPermissions };
    } else {
      users.unshift(adminUser);
    }
    
    localStorage.setItem('users', JSON.stringify(users));
    console.log('✅ تم إصلاح صلاحيات المدير في localStorage');
  }
  
  return adminUser;
};
`;

console.log('📋 صلاحيات المدير الكاملة:');
const fullPermissions = createFullAdminPermissions();
console.log('عدد الأقسام:', Object.keys(fullPermissions).length);
console.log('الأقسام:', Object.keys(fullPermissions));

console.log('\n🔧 كود الإصلاح:');
console.log(adminFixCode);

console.log('\n📝 خطوات الإصلاح:');
console.log('1. تأكد من أن المدير مُسجل دخول');
console.log('2. افتح أدوات المطور في المتصفح (F12)');
console.log('3. انتقل إلى تبويب Console');
console.log('4. انسخ والصق الكود أعلاه');
console.log('5. قم بتحديث الصفحة');

console.log('\n🔍 أو قم بفحص البيانات الحالية:');
console.log('في أدوات المطور -> Application -> Local Storage -> users');
console.log('تأكد من أن المدير (id: 1) لديه جميع الصلاحيات');

console.log('\n✅ الإصلاح جاهز!');
