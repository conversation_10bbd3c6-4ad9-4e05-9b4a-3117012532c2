const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function copyItemsToTemp() {
  console.log('🔄 نسخ البيانات من حقول items إلى حقول items_json المؤقتة...');

  try {
    // نسخ بيانات SupplyOrder
    console.log('📦 نسخ بيانات أوامر التوريد...');
    await prisma.$executeRaw`UPDATE "SupplyOrder" SET items_json = items WHERE items IS NOT NULL`;
    
    // نسخ بيانات Sale
    console.log('💰 نسخ بيانات المبيعات...');
    await prisma.$executeRaw`UPDATE "Sale" SET items_json = items WHERE items IS NOT NULL`;
    
    // نسخ بيانات Return
    console.log('↩️ نسخ بيانات المرتجعات...');
    await prisma.$executeRaw`UPDATE "Return" SET items_json = items WHERE items IS NOT NULL`;
    
    // نسخ بيانات EvaluationOrder
    console.log('📊 نسخ بيانات أوامر التقييم...');
    await prisma.$executeRaw`UPDATE "evaluation_orders" SET items_json = items WHERE items IS NOT NULL`;
    
    // نسخ بيانات MaintenanceOrder
    console.log('🔧 نسخ بيانات أوامر الصيانة...');
    await prisma.$executeRaw`UPDATE "MaintenanceOrder" SET items_json = items WHERE items IS NOT NULL`;
    
    // نسخ بيانات MaintenanceReceiptOrder
    console.log('📋 نسخ بيانات إيصالات الصيانة...');
    await prisma.$executeRaw`UPDATE "MaintenanceReceiptOrder" SET items_json = items WHERE items IS NOT NULL`;
    
    // نسخ بيانات DeliveryOrder
    console.log('🚚 نسخ بيانات أوامر التسليم...');
    await prisma.$executeRaw`UPDATE "DeliveryOrder" SET items_json = items WHERE items IS NOT NULL`;

    console.log('✅ تم نسخ جميع البيانات بنجاح!');
    
  } catch (error) {
    console.error('❌ خطأ في نسخ البيانات:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// تشغيل النسخ
if (require.main === module) {
  copyItemsToTemp()
    .then(() => {
      console.log('🎉 انتهت عملية النسخ بنجاح!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 فشل في نسخ البيانات:', error);
      process.exit(1);
    });
}

module.exports = { copyItemsToTemp };
