// مثال لاستخدام وظائف التصدير في صفحة التقارير

"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { exportTableToPDF, exportToCSV, printReport } from "@/lib/export-utils/arabic-export";
import { FilePdf, FileSpreadsheet, Printer } from "lucide-react";

export default function ReportExample() {
  // بيانات المثال
  const reportData = [
    { id: 1, اسم_المنتج: "هاتف ذكي", الفئة: "إلكترونيات", الكمية: 150, السعر: 1200 },
    { id: 2, اسم_المنتج: "حاسوب محمول", الفئة: "إلكترونيات", الكمية: 50, السعر: 3500 },
    { id: 3, اسم_المنتج: "سماعات لاسلكية", الفئة: "اكسسوارات", الكمية: 200, السعر: 350 },
    { id: 4, اسم_المنتج: "شاحن", الفئة: "اكسسوارات", الكمية: 300, السعر: 75 },
    { id: 5, اسم_المنتج: "كاميرا", الفئة: "إلكترونيات", الكمية: 30, السعر: 2200 },
  ];

  const headers = ["اسم المنتج", "الفئة", "الكمية", "السعر"];

  // وظائف التصدير
  const handlePdfExport = () => {
    const dataForExport = reportData.map(item => [
      item.اسم_المنتج,
      item.الفئة,
      item.الكمية,
      item.السعر
    ]);
    
    exportTableToPDF(dataForExport, headers, "تقرير_المخزون", "تقرير المخزون");
  };

  const handleCsvExport = () => {
    exportToCSV(reportData, headers, "تقرير_المخزون");
  };

  const handlePrint = () => {
    printReport("report-table", "تقرير المخزون");
  };

  return (
    <div className="container mx-auto py-10">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>تقرير المخزون</CardTitle>
          <div className="flex space-x-2 rtl:space-x-reverse">
            <Button variant="outline" size="sm" onClick={handlePdfExport}>
              <FilePdf className="mr-2 h-4 w-4" />
              تصدير PDF
            </Button>
            <Button variant="outline" size="sm" onClick={handleCsvExport}>
              <FileSpreadsheet className="mr-2 h-4 w-4" />
              تصدير CSV
            </Button>
            <Button variant="outline" size="sm" onClick={handlePrint}>
              <Printer className="mr-2 h-4 w-4" />
              طباعة
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div id="report-table" className="rounded-md border">
            <Table dir="rtl">
              <TableHeader>
                <TableRow>
                  {headers.map((header) => (
                    <TableHead key={header} className="text-right">{header}</TableHead>
                  ))}
                </TableRow>
              </TableHeader>
              <TableBody>
                {reportData.map((row) => (
                  <TableRow key={row.id}>
                    <TableCell className="font-medium">{row.اسم_المنتج}</TableCell>
                    <TableCell>{row.الفئة}</TableCell>
                    <TableCell>{row.الكمية}</TableCell>
                    <TableCell>{row.السعر}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
