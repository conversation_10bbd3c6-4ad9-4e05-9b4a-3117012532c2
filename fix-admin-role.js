const { PrismaClient } = require('@prisma/client');

async function fixAdminRole() {
  const prisma = new PrismaClient();
  
  try {
    const updatedUser = await prisma.user.update({
      where: { username: 'admin' },
      data: { role: 'admin' }
    });
    
    console.log('✅ Updated admin role to:', updatedUser.role);
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

fixAdminRole();
