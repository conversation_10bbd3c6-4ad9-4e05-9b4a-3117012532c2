import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { requireAuth } from '@/lib/auth';

export async function GET(request: NextRequest) {
  try {
    // التحقق من التفويض
    const authResult = await requireAuth(request, 'user');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const deviceModels = await prisma.deviceModel.findMany({
      orderBy: { id: 'desc' }
    });

    // تحويل BigInt إلى string للـ JSON serialization
    const serializedModels = deviceModels.map(model => ({
      ...model,
      manufacturerId: model.manufacturerId.toString()
    }));

    return NextResponse.json(serializedModels);
  } catch (error) {
    console.error('Failed to fetch device models:', error);
    return NextResponse.json({ error: 'Failed to fetch device models' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    // التحقق من التفويض
    const authResult = await requireAuth(request, 'user');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const newModel = await request.json();

    // Basic validation
    if (!newModel.name || !newModel.manufacturerId) {
      return NextResponse.json(
        { error: 'Model name and manufacturer ID are required' },
        { status: 400 }
      );
    }

    // Check if model already exists
    const existingModel = await prisma.deviceModel.findFirst({
      where: {
        name: newModel.name,
        manufacturerId: BigInt(newModel.manufacturerId)
      }
    });

    if (existingModel) {
      return NextResponse.json(
        { error: 'Model already exists for this manufacturer' },
        { status: 400 }
      );
    }

    // Create the model in the database
    const model = await prisma.deviceModel.create({
      data: {
        name: newModel.name,
        manufacturerId: BigInt(newModel.manufacturerId),
        category: newModel.category || 'هاتف ذكي'
      }
    });

    // تحويل BigInt إلى string للـ JSON serialization
    const serializedModel = {
      ...model,
      manufacturerId: model.manufacturerId.toString()
    };

    return NextResponse.json(serializedModel, { status: 201 });
  } catch (error) {
    console.error('Failed to create device model:', error);
    return NextResponse.json({ error: 'Failed to create device model' }, { status: 500 });
  }
}
