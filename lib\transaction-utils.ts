import { prisma } from '@/lib/prisma';
import { Prisma } from '@prisma/client';

/**
 * تنفيذ عملية داخل معاملة مع إدارة الأخطاء
 */
export async function executeInTransaction<T>(
  operation: (tx: Prisma.TransactionClient) => Promise<T>,
  options?: {
    maxWait?: number; // الحد الأقصى لانتظار المعاملة (بالميلي ثانية)
    timeout?: number; // مهلة تنفيذ المعاملة (بالميلي ثانية)
  }
): Promise<T> {
  return await prisma.$transaction(async (tx) => {
    return await operation(tx);
  }, {
    maxWait: options?.maxWait || 10000, // 10 ثوان افتراضياً
    timeout: options?.timeout || 60000, // دقيقة واحدة افتراضياً
  });
}

/**
 * تنفيذ عملية طويلة داخل معاملة مع إعدادات محسنة للعمليات الكبيرة
 */
export async function executeInLongTransaction<T>(
  operation: (tx: Prisma.TransactionClient) => Promise<T>
): Promise<T> {
  return await prisma.$transaction(async (tx) => {
    return await operation(tx);
  }, {
    maxWait: 30000, // 30 ثانية للانتظار
    timeout: 300000, // 5 دقائق للتنفيذ
  });
}

/**
 * تنفيذ عدة عمليات داخل معاملة واحدة
 */
export async function executeMultipleInTransaction<T>(
  operations: ((tx: Prisma.TransactionClient) => Promise<any>)[]
): Promise<T[]> {
  return await prisma.$transaction(async (tx) => {
    const results: T[] = [];
    for (const operation of operations) {
      const result = await operation(tx);
      results.push(result);
    }
    return results;
  });
}

/**
 * إنشاء audit log داخل معاملة
 */
export async function createAuditLogInTransaction(
  tx: any,
  data: {
    userId: number;
    username: string;
    operation: string;
    details: string;
  }
) {
  return await tx.auditLog.create({
    data: {
      userId: data.userId,
      username: data.username,
      operation: data.operation,
      details: data.details,
      timestamp: new Date()
    }
  });
}

/**
 * التحقق من وجود العلاقات قبل الحذف
 */
export async function checkRelationsBeforeDelete(
  tx: Prisma.TransactionClient,
  tableName: string,
  recordId: string | number
): Promise<{ hasRelations: boolean; relations: string[] }> {
  const relations: string[] = [];
  
  try {
    switch (tableName) {
      case 'client':
        const clientOperations = await tx.maintenanceOrder.count({
          where: { clientId: recordId as string }
        });
        if (clientOperations > 0) {
          relations.push(`${clientOperations} maintenance orders`);
        }
        break;
        
      case 'device':
        const deviceLogs = await tx.maintenanceLog.count({
          where: { deviceId: recordId as string }
        });
        if (deviceLogs > 0) {
          relations.push(`${deviceLogs} maintenance logs`);
        }
        break;
        
      case 'supplier':
        const supplierOrders = await tx.maintenanceOrder.count({
          where: { supplierId: recordId as string }
        });
        if (supplierOrders > 0) {
          relations.push(`${supplierOrders} maintenance orders`);
        }
        break;
        
      default:
        // يمكن إضافة المزيد من الجداول حسب الحاجة
        break;
    }
    
    return {
      hasRelations: relations.length > 0,
      relations
    };
  } catch (error) {
    console.error('Error checking relations:', error);
    return {
      hasRelations: false,
      relations: []
    };
  }
}

/**
 * إنشاء رقم تسلسلي فريد داخل معاملة
 */
export async function generateUniqueId(
  tx: Prisma.TransactionClient,
  tableName: string,
  prefix: string = ''
): Promise<string> {
  let attempts = 0;
  const maxAttempts = 10;
  
  while (attempts < maxAttempts) {
    const timestamp = Date.now();
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    const id = `${prefix}${timestamp}${random}`;
    
    // التحقق من عدم وجود ID مشابه
    let exists = false;
    
    try {
      switch (tableName) {
        case 'maintenanceOrder':
          const order = await tx.maintenanceOrder.findFirst({
            where: { orderNumber: id }
          });
          exists = !!order;
          break;
        case 'deliveryOrder':
          const delivery = await tx.deliveryOrder.findFirst({
            where: { deliveryOrderNumber: id }
          });
          exists = !!delivery;
          break;
        default:
          // للجداول الأخرى، نفترض أن ID فريد
          exists = false;
      }
      
      if (!exists) {
        return id;
      }
    } catch (error) {
      console.error('Error checking ID uniqueness:', error);
    }
    
    attempts++;
    // انتظار قصير قبل المحاولة التالية
    await new Promise(resolve => setTimeout(resolve, 10));
  }
  
  throw new Error(`Failed to generate unique ID after ${maxAttempts} attempts`);
}
