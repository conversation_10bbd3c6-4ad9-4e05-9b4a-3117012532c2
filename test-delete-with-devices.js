/**
 * اختبار حذف أمر التوريد والأجهزة من قاعدة البيانات
 */

console.log('🧪 اختبار حذف أمر التوريد والأجهزة...\n');

// دالة لإنشاء أمر توريد تجريبي
async function createTestSupplyOrder() {
  const testOrder = {
    supplyOrderId: `TEST-${Date.now()}`,
    supplierId: 1,
    warehouseId: 1,
    employeeName: 'اختبار',
    supplyDate: new Date().toISOString(),
    items: [
      {
        imei: `TEST-${Date.now()}-1`,
        deviceModel: 'Test Phone 1',
        condition: 'جديد',
        price: 1000
      },
      {
        imei: `TEST-${Date.now()}-2`,
        deviceModel: 'Test Phone 2',
        condition: 'جديد',
        price: 1200
      }
    ],
    status: 'completed'
  };

  try {
    console.log('📤 إنشاء أمر توريد تجريبي...');
    const response = await fetch('http://localhost:9005/api/supply', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(testOrder)
    });

    if (response.ok) {
      const result = await response.json();
      console.log('✅ تم إنشاء أمر التوريد:', result.supplyOrderId);
      return { success: true, order: result, testDevices: testOrder.items };
    } else {
      const error = await response.json();
      console.log('❌ فشل في إنشاء أمر التوريد:', error);
      return { success: false };
    }
  } catch (error) {
    console.error('💥 خطأ في إنشاء أمر التوريد:', error.message);
    return { success: false };
  }
}

// دالة للتحقق من وجود الأجهزة في قاعدة البيانات
async function checkDevicesExist(deviceIMEIs) {
  try {
    console.log('🔍 التحقق من وجود الأجهزة في قاعدة البيانات...');
    const response = await fetch('http://localhost:9005/api/devices');
    
    if (response.ok) {
      const devices = await response.json();
      const foundDevices = devices.filter(device => deviceIMEIs.includes(device.id));
      console.log(`📊 عدد الأجهزة الموجودة: ${foundDevices.length} من أصل ${deviceIMEIs.length}`);
      return foundDevices;
    }
  } catch (error) {
    console.error('💥 خطأ في فحص الأجهزة:', error.message);
  }
  return [];
}

// دالة لحذف أمر التوريد
async function deleteSupplyOrder(orderId) {
  try {
    console.log(`🗑️ حذف أمر التوريد ID: ${orderId}...`);
    const response = await fetch('http://localhost:9005/api/supply', {
      method: 'DELETE',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ id: orderId })
    });

    if (response.ok) {
      const result = await response.json();
      console.log('✅ تم حذف أمر التوريد:', result);
      return { success: true, result };
    } else {
      const error = await response.json();
      console.log('❌ فشل في حذف أمر التوريد:', error);
      return { success: false, error };
    }
  } catch (error) {
    console.error('💥 خطأ في حذف أمر التوريد:', error.message);
    return { success: false, error: error.message };
  }
}

// تشغيل الاختبار الكامل
async function runFullTest() {
  console.log('🎯 بدء الاختبار الشامل لحذف أوامر التوريد والأجهزة\n');

  // الخطوة 1: إنشاء أمر توريد تجريبي
  const createResult = await createTestSupplyOrder();
  if (!createResult.success) {
    console.log('❌ فشل الاختبار - لم يتم إنشاء أمر التوريد');
    return;
  }

  const { order, testDevices } = createResult;
  const deviceIMEIs = testDevices.map(device => device.imei);

  // الخطوة 2: التحقق من وجود الأجهزة
  console.log('\n' + '='.repeat(50));
  const devicesBeforeDelete = await checkDevicesExist(deviceIMEIs);
  
  if (devicesBeforeDelete.length !== deviceIMEIs.length) {
    console.log('⚠️ تحذير: لم يتم العثور على جميع الأجهزة المتوقعة');
  }

  // الخطوة 3: حذف أمر التوريد
  console.log('\n' + '='.repeat(50));
  const deleteResult = await deleteSupplyOrder(order.id);
  
  if (!deleteResult.success) {
    console.log('❌ فشل الاختبار - لم يتم حذف أمر التوريد');
    return;
  }

  // الخطوة 4: التحقق من حذف الأجهزة
  console.log('\n' + '='.repeat(50));
  const devicesAfterDelete = await checkDevicesExist(deviceIMEIs);
  
  if (devicesAfterDelete.length === 0) {
    console.log('🎉 نجح الاختبار! تم حذف جميع الأجهزة من قاعدة البيانات');
  } else {
    console.log(`⚠️ مشكلة: ما زال ${devicesAfterDelete.length} جهاز موجود في قاعدة البيانات`);
    devicesAfterDelete.forEach(device => {
      console.log(`  - الجهاز: ${device.id}`);
    });
  }

  console.log('\n🎯 انتهاء الاختبار');
}

// تشغيل الاختبار
runFullTest();
