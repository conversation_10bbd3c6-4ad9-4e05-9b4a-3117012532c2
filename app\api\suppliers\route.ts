import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { requireAuth } from '@/lib/auth';
import { executeInTransaction, createAuditLogInTransaction, checkRelationsBeforeDelete } from '@/lib/transaction-utils';

export async function GET(request: NextRequest) {
  try {
    // التحقق من التفويض
    const authResult = await requireAuth(request, 'user');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    // استرجاع كل الموردين من قاعدة البيانات، مرتبة تنازلياً
    const suppliers = await prisma.supplier.findMany({
      orderBy: { id: 'desc' }
    });
    return NextResponse.json(suppliers);
  } catch (error) {
    console.error('Failed to fetch suppliers:', error);
    return NextResponse.json({ error: 'Failed to fetch suppliers' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    // التحقق من التفويض
    const authResult = await requireAuth(request, 'user');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const newSupplier = await request.json();

    // التحقق من البيانات المطلوبة
    if (!newSupplier.name) {
      return NextResponse.json(
        { error: 'Name is required' },
        { status: 400 }
      );
    }

    // تنفيذ العملية داخل معاملة
    const result = await executeInTransaction(async (tx) => {
      // التحقق من عدم وجود بريد إلكتروني مكرر (إذا تم توفيره)
      if (newSupplier.email && newSupplier.email.trim()) {
        const existingSupplier = await tx.supplier.findFirst({
          where: { 
            email: newSupplier.email
          }
        });

        if (existingSupplier) {
          throw new Error('Email already exists');
        }
      }

      // إنشاء المورد في قاعدة البيانات
      const supplier = await tx.supplier.create({
        data: {
          name: newSupplier.name,
          phone: newSupplier.phone || '',
          email: newSupplier.email || '',
        }
      });

      // إنشاء audit log
      await createAuditLogInTransaction(tx, {
        userId: authResult.user!.id,
        username: authResult.user!.username,
        operation: 'CREATE',
        details: `Created supplier: ${supplier.name}`,
        tableName: 'supplier',
        recordId: supplier.id
      });

      return supplier;
    });

    return NextResponse.json(result, { status: 201 });
  } catch (error) {
    console.error('Failed to create supplier:', error);

    if (error instanceof Error && error.message === 'Email already exists') {
      return NextResponse.json({ error: 'Email already exists' }, { status: 409 });
    }

    return NextResponse.json({ error: 'Failed to create supplier' }, { status: 500 });
  }
}

export async function PUT(request: NextRequest) {
  try {
    // التحقق من التفويض
    const authResult = await requireAuth(request, 'user');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const updatedSupplier = await request.json();

    // تنفيذ العملية داخل معاملة
    const result = await executeInTransaction(async (tx) => {
      // التحقق من وجود المورد
      const existingSupplier = await tx.supplier.findUnique({
        where: { id: updatedSupplier.id }
      });

      if (!existingSupplier) {
        throw new Error('Supplier not found');
      }

      // التحقق من عدم وجود بريد إلكتروني مكرر (باستثناء المورد الحالي)
      if (updatedSupplier.email && updatedSupplier.email.trim() && updatedSupplier.email !== existingSupplier.email) {
        const emailExists = await tx.supplier.findUnique({
          where: { email: updatedSupplier.email }
        });

        if (emailExists) {
          throw new Error('Email already exists');
        }
      }

      // تحديث المورد
      const supplier = await tx.supplier.update({
        where: { id: updatedSupplier.id },
        data: {
          name: updatedSupplier.name,
          phone: updatedSupplier.phone || '',
          email: updatedSupplier.email || '',
        }
      });

      // إنشاء audit log
      await createAuditLogInTransaction(tx, {
        userId: authResult.user!.id,
        username: authResult.user!.username,
        operation: 'UPDATE',
        details: `Updated supplier: ${supplier.name}`,
        tableName: 'supplier',
        recordId: supplier.id
      });

      return supplier;
    });

    return NextResponse.json(result);
  } catch (error) {
    console.error('Failed to update supplier:', error);

    if (error instanceof Error) {
      if (error.message === 'Supplier not found') {
        return NextResponse.json({ error: 'Supplier not found' }, { status: 404 });
      }
      if (error.message === 'Email already exists') {
        return NextResponse.json({ error: 'Email already exists' }, { status: 409 });
      }
    }

    return NextResponse.json({ error: 'Failed to update supplier' }, { status: 500 });
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // التحقق من التفويض - يتطلب صلاحيات إدارية للحذف
    const authResult = await requireAuth(request, 'manager');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const { id } = await request.json();

    // تنفيذ العملية داخل معاملة
    const result = await executeInTransaction(async (tx) => {
      // التحقق من وجود المورد
      const existingSupplier = await tx.supplier.findUnique({
        where: { id }
      });

      if (!existingSupplier) {
        throw new Error('Supplier not found');
      }

      // فحص العلاقات قبل الحذف
      const relationCheck = await checkRelationsBeforeDelete(tx, 'supplier', id);

      // فحص إضافي لأوامر التوريد
      const relatedSupplyOrders = await tx.supplyOrder?.findMany({
        where: { supplierId: id }
      }) || [];

      const relatedOperations: string[] = [...relationCheck.relations];
      if (relatedSupplyOrders.length > 0) {
        relatedOperations.push(`${relatedSupplyOrders.length} أمر توريد`);
      }

      if (relatedOperations.length > 0) {
        throw new Error(`Cannot delete supplier: ${relatedOperations.join(', ')}`);
      }

      // حذف المورد
      await tx.supplier.delete({
        where: { id }
      });

      // إنشاء audit log
      await createAuditLogInTransaction(tx, {
        userId: authResult.user!.id,
        username: authResult.user!.username,
        operation: 'DELETE',
        details: `Deleted supplier: ${existingSupplier.name}`,
        tableName: 'supplier',
        recordId: id
      });

      return { message: 'Supplier deleted successfully' };
    });

    return NextResponse.json(result);
  } catch (error) {
    console.error('Failed to delete supplier:', error);

    if (error instanceof Error) {
      if (error.message === 'Supplier not found') {
        return NextResponse.json({ error: 'Supplier not found' }, { status: 404 });
      }
      if (error.message.startsWith('Cannot delete supplier:')) {
        const relatedOperations = error.message.replace('Cannot delete supplier: ', '').split(', ');
        return NextResponse.json({
          error: 'Cannot delete supplier',
          reason: 'يوجد عمليات مرتبطة بهذا المورد',
          relatedOperations
        }, { status: 409 });
      }
    }

    return NextResponse.json({ error: 'Failed to delete supplier' }, { status: 500 });
  }
}
