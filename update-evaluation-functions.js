// Script لتحديث وظائف التقييم في context/store.tsx لاستخدام API
// نسخ احتياطية قبل التشغيل

const fs = require('fs');

const filePath = 'context/store.tsx';

// قراءة الملف
let content = fs.readFileSync(filePath, 'utf8');

// البحث عن وظائف التقييم
const addEvaluationStart = 'const addEvaluationOrder = (';
const updateEvaluationStart = 'const updateEvaluationOrder = (';
const deleteEvaluationStart = 'const deleteEvaluationOrder = (';

// 1. تحديث addEvaluationOrder
const addEvaluationStartIndex = content.indexOf(addEvaluationStart);
if (addEvaluationStartIndex !== -1) {
  const nextFunctionStart = content.indexOf('const updateEvaluationOrder', addEvaluationStartIndex);
  const addEvaluationEndIndex = content.lastIndexOf('};', nextFunctionStart);
  
  const newAddEvaluationFunction = `const addEvaluationOrder = async (
    order: Omit<EvaluationOrder, "id" | "createdAt">,
  ) => {
    try {
      // إرسال الأمر إلى API
      const response = await fetch('/api/evaluations', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...order,
          date: order.date || new Date().toISOString(),
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create evaluation order');
      }

      // استقبال الأمر الذي تم إنشاؤه من API
      const newOrder = await response.json();

      // تحديث حالة التطبيق
      setEvaluationOrders((prev) => [newOrder, ...prev]);

      // Update device status based on evaluation
      newOrder.items.forEach((item) => {
        let newStatus: DeviceStatus;
        switch (item.finalGrade) {
          case "جاهز للبيع":
            newStatus = "متاح للبيع";
            break;
          case "يحتاج صيانة":
            newStatus = "تحتاج صيانة";
            break;
          case "عيب فني":
            newStatus = "معيب";
            break;
          case "تالف":
            newStatus = "تالف";
            break;
          default:
            return;
        }
        updateDeviceStatus(item.deviceId, newStatus);
      });

      addActivity({
        type: "evaluation",
        description: \`تم تقييم \${newOrder.items.length} أجهزة في الأمر \${newOrder.orderId}\`,
      });
      
      return newOrder;
    } catch (error) {
      console.error('Failed to add evaluation order:', error);
      addActivity({
        type: "evaluation",
        description: \`⚠️ فشل في إنشاء أمر تقييم: \${error instanceof Error ? error.message : String(error)}\`,
      });
      throw error;
    }
  };`;

  const beforeAddFunction = content.substring(0, addEvaluationStartIndex);
  const afterAddFunction = content.substring(addEvaluationEndIndex + 2);
  content = beforeAddFunction + newAddEvaluationFunction + '\n\n  ' + afterAddFunction;
}

// 2. تحديث updateEvaluationOrder
const updateEvaluationStartIndex = content.indexOf(updateEvaluationStart);
if (updateEvaluationStartIndex !== -1) {
  const nextFunctionStart = content.indexOf('const deleteEvaluationOrder', updateEvaluationStartIndex);
  const updateEvaluationEndIndex = content.lastIndexOf('};', nextFunctionStart);
  
  const newUpdateEvaluationFunction = `const updateEvaluationOrder = async (updatedOrder: EvaluationOrder) => {
    try {
      // إرسال التحديث إلى API
      const response = await fetch('/api/evaluations', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(updatedOrder),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update evaluation order');
      }

      // استقبال الأمر المحدث من API
      const updated = await response.json();

      // تحديث حالة التطبيق
      setEvaluationOrders((prev) =>
        prev.map((o) => (o.id === updated.id ? updated : o)),
      );

      // Update device status based on evaluation
      updated.items.forEach((item) => {
        let newStatus: DeviceStatus;
        switch (item.finalGrade) {
          case "جاهز للبيع":
            newStatus = "متاح للبيع";
            break;
          case "يحتاج صيانة":
            newStatus = "تحتاج صيانة";
            break;
          case "عيب فني":
            newStatus = "معيب";
            break;
          case "تالف":
            newStatus = "تالف";
            break;
          default:
            return;
        }
        updateDeviceStatus(item.deviceId, newStatus);
      });

      addActivity({
        type: "evaluation",
        description: \`تم تحديث أمر التقييم \${updated.orderId}\`,
      });
      
      return updated;
    } catch (error) {
      console.error('Failed to update evaluation order:', error);
      addActivity({
        type: "evaluation",
        description: \`⚠️ فشل في تحديث أمر تقييم: \${error instanceof Error ? error.message : String(error)}\`,
      });
      throw error;
    }
  };`;

  const beforeUpdateFunction = content.substring(0, updateEvaluationStartIndex);
  const afterUpdateFunction = content.substring(updateEvaluationEndIndex + 2);
  content = beforeUpdateFunction + newUpdateEvaluationFunction + '\n\n  ' + afterUpdateFunction;
}

// 3. تحديث deleteEvaluationOrder
const deleteEvaluationStartIndex = content.indexOf(deleteEvaluationStart);
if (deleteEvaluationStartIndex !== -1) {
  // البحث عن نهاية الوظيفة
  let braceCount = 0;
  let currentIndex = deleteEvaluationStartIndex;
  let foundStart = false;
  
  while (currentIndex < content.length) {
    if (content[currentIndex] === '{') {
      braceCount++;
      foundStart = true;
    } else if (content[currentIndex] === '}') {
      braceCount--;
      if (foundStart && braceCount === 0) {
        break;
      }
    }
    currentIndex++;
  }
  
  const deleteEvaluationEndIndex = currentIndex;
  
  const newDeleteEvaluationFunction = `const deleteEvaluationOrder = async (orderId: number) => {
    try {
      // إرسال طلب الحذف إلى API
      const response = await fetch('/api/evaluations', {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ id: orderId }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete evaluation order');
      }

      // تحديث حالة التطبيق
      setEvaluationOrders((prev) => prev.filter((o) => o.id !== orderId));

      addActivity({
        type: "evaluation",
        description: \`تم حذف أمر التقييم رقم \${orderId}\`,
      });
    } catch (error) {
      console.error('Failed to delete evaluation order:', error);
      addActivity({
        type: "evaluation",
        description: \`⚠️ فشل في حذف أمر تقييم: \${error instanceof Error ? error.message : String(error)}\`,
      });
      throw error;
    }
  }`;

  const beforeDeleteFunction = content.substring(0, deleteEvaluationStartIndex);
  const afterDeleteFunction = content.substring(deleteEvaluationEndIndex + 1);
  content = beforeDeleteFunction + newDeleteEvaluationFunction + afterDeleteFunction;
}

// كتابة الملف المحدث
fs.writeFileSync(filePath, content, 'utf8');

console.log('تم تحديث وظائف التقييم بنجاح!');
console.log('✅ addEvaluationOrder - يستخدم API الآن');
console.log('✅ updateEvaluationOrder - يستخدم API الآن');
console.log('✅ deleteEvaluationOrder - يستخدم API الآن');
console.log('الآن صفحة الفحص والتقييم ستحتفظ بالبيانات في قاعدة البيانات.');
