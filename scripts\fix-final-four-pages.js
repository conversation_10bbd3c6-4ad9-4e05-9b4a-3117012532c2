const fs = require('fs');
const path = require('path');

function fixFinalFourPages() {
  console.log('🔧 إصلاح الصفحات الأربع الأخيرة...\n');

  const pagesToFix = [
    { name: 'sales', path: 'app/(main)/sales/page.tsx' },
    { name: 'messaging', path: 'app/(main)/messaging/page.tsx' },
    { name: 'track', path: 'app/(main)/track/page.tsx' },
    { name: 'users', path: 'app/(main)/users/page.tsx' }
  ];

  let totalFixed = 0;
  let filesFixed = 0;

  pagesToFix.forEach(page => {
    try {
      if (!fs.existsSync(page.path)) {
        console.log(`⚠️ ${page.name}: الملف غير موجود`);
        return;
      }

      let content = fs.readFileSync(page.path, 'utf8');
      let hasChanges = false;
      const originalContent = content;

      console.log(`📄 معالجة ${page.name}...`);

      // 1. إصلاح أقواس مفقودة في if statements
      const ifStatementPattern = /if\s*\(([^)]+)\)\s+{/g;
      const ifMatches = content.match(ifStatementPattern);
      if (ifMatches) {
        content = content.replace(ifStatementPattern, 'if ($1) {');
        console.log(`   ✅ إصلاح ${ifMatches.length} if statement`);
        hasChanges = true;
      }

      // 2. إصلاح أقواس مفقودة في function calls
      const functionCallPattern = /(\w+)\(([^)]*)\s+{/g;
      const functionMatches = content.match(functionCallPattern);
      if (functionMatches) {
        content = content.replace(functionCallPattern, '$1($2) {');
        console.log(`   ✅ إصلاح ${functionMatches.length} function call`);
        hasChanges = true;
      }

      // 3. إضافة permissions إذا كان مفقوداً
      if (content.includes('permissions') && !content.includes('const permissions =')) {
        // البحث عن مكان مناسب لإضافة التعريف
        const useEffectMatch = content.match(/useEffect\(\(\) => \{[\s\S]*?\}, \[\]\);/);
        if (useEffectMatch) {
          content = content.replace(
            useEffectMatch[0],
            useEffectMatch[0] + '\n\n  // Temporary permissions (remove when auth system is implemented)\n  const permissions = { create: true, edit: true, delete: true, view: true, viewAll: true };'
          );
          console.log(`   ✅ إضافة تعريف permissions`);
          hasChanges = true;
        } else {
          // إضافة بعد آخر useState
          const lastUseStateMatch = content.match(/const \[[\s\S]*?\] = useState\([\s\S]*?\);(?=\s*\n)/g);
          if (lastUseStateMatch) {
            const lastUseState = lastUseStateMatch[lastUseStateMatch.length - 1];
            content = content.replace(
              lastUseState,
              lastUseState + '\n\n  // Temporary permissions\n  const permissions = { create: true, edit: true, delete: true, view: true, viewAll: true };'
            );
            console.log(`   ✅ إضافة تعريف permissions بعد useState`);
            hasChanges = true;
          }
        }
      }

      // 4. إضافة currentUser إذا كان مفقوداً
      if (content.includes('currentUser') && !content.includes('useStore') && !content.includes('const { currentUser }')) {
        // إضافة import useStore
        if (!content.includes('useStore')) {
          content = content.replace(
            /import.*from.*@\/hooks\/use-toast.*/,
            `$&\nimport { useStore } from '@/context/store';`
          );
        }

        // إضافة currentUser من useStore
        const permissionsLine = content.match(/const permissions = .*/);
        if (permissionsLine) {
          content = content.replace(
            permissionsLine[0],
            `  // Get currentUser from store\n  const { currentUser } = useStore();\n\n${permissionsLine[0]}`
          );
          console.log(`   ✅ إضافة currentUser من useStore`);
          hasChanges = true;
        }
      }

      // 5. إصلاح استخدام .items غير الآمن
      const unsafeItemsPattern = /(\w+)\.items\.(map|filter|find|some|every|length)/g;
      const itemsMatches = content.match(unsafeItemsPattern);
      if (itemsMatches) {
        content = content.replace(unsafeItemsPattern, (match, varName, method) => {
          // التحقق من وجود فحص Array.isArray قبل الاستخدام
          const beforeMatch = content.substring(Math.max(0, content.indexOf(match) - 100), content.indexOf(match));
          if (!beforeMatch.includes('Array.isArray') && !beforeMatch.includes('?.')) {
            return `(Array.isArray(${varName}.items) ? ${varName}.items.${method} : [].${method})`;
          }
          return match;
        });
        console.log(`   ✅ إصلاح ${itemsMatches.length} استخدام غير آمن لـ .items`);
        hasChanges = true;
      }

      // 6. إصلاح أقواس غير متطابقة
      const openParens = (content.match(/\(/g) || []).length;
      const closeParens = (content.match(/\)/g) || []).length;
      if (openParens !== closeParens) {
        console.log(`   ⚠️ أقواس غير متطابقة: ${openParens} فتح، ${closeParens} إغلاق`);
      }

      if (hasChanges) {
        fs.writeFileSync(page.path, content);
        console.log(`   🎉 تم إصلاح ${page.name} بنجاح\n`);
        filesFixed++;
        totalFixed++;
      } else {
        console.log(`   ℹ️ ${page.name}: لا يحتاج إصلاح\n`);
      }

    } catch (error) {
      console.log(`   ❌ خطأ في معالجة ${page.name}: ${error.message}\n`);
    }
  });

  console.log(`📊 ملخص إصلاح الصفحات الأربع:`);
  console.log(`   - صفحات تم فحصها: ${pagesToFix.length}`);
  console.log(`   - صفحات تم إصلاحها: ${filesFixed}`);
  console.log(`   - إجمالي المشاكل المُصلحة: ${totalFixed}`);

  if (totalFixed > 0) {
    console.log(`\n🎉 تم إصلاح ${totalFixed} مشكلة في ${filesFixed} صفحة!`);
    console.log(`\n📝 يُنصح بإجراء اختبار شامل للتأكد من عمل جميع الصفحات`);
    return true;
  } else {
    console.log(`\nℹ️ جميع الصفحات الأربع نظيفة`);
    return true;
  }
}

// دالة لفحص الصفحات الأربع بحثاً عن مشاكل محددة
function checkFinalFourPages() {
  console.log('\n🔍 فحص الصفحات الأربع الأخيرة...\n');

  const pagesToCheck = [
    { name: 'sales', path: 'app/(main)/sales/page.tsx' },
    { name: 'messaging', path: 'app/(main)/messaging/page.tsx' },
    { name: 'track', path: 'app/(main)/track/page.tsx' },
    { name: 'users', path: 'app/(main)/users/page.tsx' }
  ];

  const issues = [];

  pagesToCheck.forEach(page => {
    if (!fs.existsSync(page.path)) {
      issues.push(`${page.name}: ملف غير موجود`);
      return;
    }

    const content = fs.readFileSync(page.path, 'utf8');
    const pageIssues = [];

    // فحص أقواس مفقودة في if statements
    const ifIssues = content.match(/if\s*\([^)]+\)\s+{/g);
    if (ifIssues) {
      pageIssues.push(`${ifIssues.length} if statement بأقواس مفقودة`);
    }

    // فحص permissions غير معرف
    if (content.includes('permissions') && !content.includes('const permissions =')) {
      pageIssues.push('permissions غير معرف');
    }

    // فحص currentUser غير معرف
    if (content.includes('currentUser') && !content.includes('useStore') && !content.includes('const { currentUser }')) {
      pageIssues.push('currentUser غير معرف');
    }

    // فحص استخدام .items غير آمن
    const unsafeItems = content.match(/\w+\.items\.(map|filter|find)/g) || [];
    const safeItems = unsafeItems.filter(usage => {
      const context = content.substring(
        Math.max(0, content.indexOf(usage) - 50),
        content.indexOf(usage) + usage.length + 50
      );
      return !context.includes('Array.isArray');
    });
    if (safeItems.length > 0) {
      pageIssues.push(`${safeItems.length} استخدام غير آمن لـ .items`);
    }

    if (pageIssues.length > 0) {
      issues.push(`${page.name}: ${pageIssues.join(', ')}`);
    } else {
      console.log(`✅ ${page.name}: نظيف`);
    }
  });

  if (issues.length > 0) {
    console.log('\n⚠️ المشاكل المكتشفة:');
    issues.forEach(issue => console.log(`   - ${issue}`));
  } else {
    console.log('\n🎉 جميع الصفحات الأربع نظيفة!');
  }

  return issues.length === 0;
}

if (require.main === module) {
  const success = fixFinalFourPages();
  const clean = checkFinalFourPages();
  process.exit(success && clean ? 0 : 1);
}

module.exports = { fixFinalFourPages, checkFinalFourPages };
