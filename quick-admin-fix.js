// إصلاح سريع لصلاحيات المدير
// قم بتشغيل هذا الملف إذا كانت صلاحيات المدير محدودة

console.log('🚀 إصلاح سريع لصلاحيات المدير');
console.log('===================================');

// قائمة جميع الأقسام المطلوبة
const allSections = [
  'dashboard',
  'clients', 
  'warehouses',
  'inventory',
  'supply',
  'sales',
  'requests',
  'returns',
  'maintenance',
  'maintenanceTransfer',
  'warehouseTransfer',
  'grading',
  'track',
  'stocktaking',
  'acceptDevices',
  'messaging',
  'reports',
  'users',
  'settings',
  'pricing'
];

// إنشاء صلاحيات كاملة
const createFullPermissions = () => {
  const permissions = {};
  
  allSections.forEach(section => {
    permissions[section] = {
      view: true,
      create: true,
      edit: true,
      delete: true,
      viewAll: true,
      manage: [1, 2, 3],
      acceptWithoutWarranty: true
    };
  });
  
  return permissions;
};

// كود الإصلاح الذي يمكن نسخه ولصقه في متصفح
const browserFixCode = `
// كود إصلاح صلاحيات المدير - قم بنسخ ولصق هذا في console المتصفح

const allSections = ['dashboard','clients','warehouses','inventory','supply','sales','requests','returns','maintenance','maintenanceTransfer','warehouseTransfer','grading','track','stocktaking','acceptDevices','messaging','reports','users','settings','pricing'];

const createFullPermissions = () => {
  const permissions = {};
  allSections.forEach(section => {
    permissions[section] = {
      view: true,
      create: true, 
      edit: true,
      delete: true,
      viewAll: true,
      manage: [1, 2, 3],
      acceptWithoutWarranty: true
    };
  });
  return permissions;
};

// إصلاح المدير في localStorage
try {
  const users = JSON.parse(localStorage.getItem('users') || '[]');
  const adminIndex = users.findIndex(u => u.id === 1 || u.username === 'admin');
  
  if (adminIndex >= 0) {
    users[adminIndex].permissions = createFullPermissions();
    localStorage.setItem('users', JSON.stringify(users));
    console.log('✅ تم إصلاح صلاحيات المدير!');
    console.log('🔄 قم بتحديث الصفحة الآن');
  } else {
    console.log('❌ لم يتم العثور على المدير');
  }
} catch (error) {
  console.error('❌ خطأ في الإصلاح:', error);
}

// إصلاح المدير في Store إذا كان متاحاً
if (typeof window !== 'undefined' && window.useStore) {
  try {
    const store = window.useStore.getState();
    if (store.updateUser) {
      const adminUser = store.users.find(u => u.id === 1 || u.username === 'admin');
      if (adminUser) {
        store.updateUser({
          ...adminUser,
          permissions: createFullPermissions()
        });
        console.log('✅ تم إصلاح صلاحيات المدير في Store!');
      }
    }
  } catch (error) {
    console.log('⚠️ Store غير متاح، الإصلاح في localStorage كاف');
  }
}
`;

console.log('📋 تفاصيل الإصلاح:');
console.log(`عدد الأقسام: ${allSections.length}`);
console.log('الأقسام:', allSections.join(', '));

console.log('\n🔧 كود الإصلاح للمتصفح:');
console.log('انسخ الكود التالي والصقه في console المتصفح:');
console.log('='.repeat(50));
console.log(browserFixCode);
console.log('='.repeat(50));

console.log('\n📝 خطوات الإصلاح:');
console.log('1. افتح النظام في المتصفح');
console.log('2. سجل دخول كمدير');
console.log('3. اضغط F12 لفتح أدوات المطور');
console.log('4. انتقل إلى تبويب Console');
console.log('5. انسخ والصق الكود أعلاه');
console.log('6. اضغط Enter');
console.log('7. حدث الصفحة (F5)');
console.log('8. اذهب إلى إدارة المستخدمين وجرب إضافة مستخدم جديد');

console.log('\n✅ كود الإصلاح جاهز!');
