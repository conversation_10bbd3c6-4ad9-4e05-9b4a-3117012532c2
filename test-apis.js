const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testAPIs() {
  try {
    console.log('🧪 اختبار APIs...\n');

    // اختبار الأجهزة
    console.log('📱 اختبار API الأجهزة...');
    const devices = await prisma.device.findMany();
    console.log(`✅ تم جلب ${devices.length} جهاز`);
    if (devices.length > 0) {
      console.log('أول جهاز:', {
        id: devices[0].id,
        model: devices[0].model,
        status: devices[0].status,
        price: devices[0].price
      });
    }

    // اختبار موديلات الأجهزة
    console.log('\n📋 اختبار API موديلات الأجهزة...');
    const deviceModels = await prisma.deviceModel.findMany();
    console.log(`✅ تم جلب ${deviceModels.length} موديل`);
    if (deviceModels.length > 0) {
      console.log('أول موديل:', {
        id: deviceModels[0].id,
        name: deviceModels[0].name,
        manufacturerId: deviceModels[0].manufacturerId.toString()
      });
    }

    // اختبار طلبات الموظفين
    console.log('\n📝 اختبار API طلبات الموظفين...');
    const employeeRequests = await prisma.employeeRequest.findMany();
    console.log(`✅ تم جلب ${employeeRequests.length} طلب`);
    if (employeeRequests.length > 0) {
      console.log('أول طلب:', {
        id: employeeRequests[0].id,
        requestNumber: employeeRequests[0].requestNumber,
        requestType: employeeRequests[0].requestType,
        status: employeeRequests[0].status,
        employeeName: employeeRequests[0].employeeName
      });
    }

    // اختبار المخازن
    console.log('\n🏢 اختبار API المخازن...');
    const warehouses = await prisma.warehouse.findMany();
    console.log(`✅ تم جلب ${warehouses.length} مخزن`);
    if (warehouses.length > 0) {
      console.log('أول مخزن:', {
        id: warehouses[0].id,
        name: warehouses[0].name,
        location: warehouses[0].location
      });
    }

    // اختبار أوامر التوريد
    console.log('\n📦 اختبار API أوامر التوريد...');
    const supplyOrders = await prisma.supplyOrder.findMany({
      include: { items: true }
    });
    console.log(`✅ تم جلب ${supplyOrders.length} أمر توريد`);
    if (supplyOrders.length > 0) {
      console.log('أول أمر توريد:', {
        id: supplyOrders[0].id,
        itemsCount: supplyOrders[0].items.length
      });
    }

    // اختبار المبيعات
    console.log('\n💰 اختبار API المبيعات...');
    const sales = await prisma.sale.findMany({
      include: { items: true }
    });
    console.log(`✅ تم جلب ${sales.length} مبيعة`);

    // اختبار المرتجعات
    console.log('\n↩️ اختبار API المرتجعات...');
    const returns = await prisma.return.findMany({
      include: { items: true }
    });
    console.log(`✅ تم جلب ${returns.length} مرتجع`);

    console.log('\n🎉 جميع APIs تعمل بشكل صحيح!');

  } catch (error) {
    console.error('❌ خطأ في اختبار APIs:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testAPIs();
