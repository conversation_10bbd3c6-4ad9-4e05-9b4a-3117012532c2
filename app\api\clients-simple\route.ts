import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// GET - جلب جميع العملاء
export async function GET() {
  try {
    const clients = await prisma.client.findMany({
      orderBy: { name: 'asc' }
    });

    return NextResponse.json(clients);
  } catch (error) {
    console.error('خطأ في جلب العملاء:', error);
    return NextResponse.json(
      { error: 'فشل في جلب العملاء' },
      { status: 500 }
    );
  }
}

// POST - إنشاء عميل جديد
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { name, phone, email, address } = body;

    // التحقق من البيانات المطلوبة
    if (!name) {
      return NextResponse.json(
        { error: 'اسم العميل مطلوب' },
        { status: 400 }
      );
    }

    // التحقق من عدم تكرار الهاتف
    if (phone) {
      const existingClient = await prisma.client.findFirst({
        where: { phone }
      });

      if (existingClient) {
        return NextResponse.json(
          { error: 'يوجد عميل بهذا الرقم بالفعل' },
          { status: 400 }
        );
      }
    }

    const client = await prisma.client.create({
      data: {
        name,
        phone: phone || null,
        email: email || null,
        address: address || null
      }
    });

    return NextResponse.json(client, { status: 201 });
  } catch (error) {
    console.error('خطأ في إنشاء العميل:', error);
    return NextResponse.json(
      { error: 'فشل في إنشاء العميل' },
      { status: 500 }
    );
  }
}

// PUT - تحديث عميل
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { id, name, phone, email, address } = body;

    if (!id) {
      return NextResponse.json(
        { error: 'معرف العميل مطلوب' },
        { status: 400 }
      );
    }

    // التحقق من وجود العميل
    const existingClient = await prisma.client.findUnique({
      where: { id: parseInt(id) }
    });

    if (!existingClient) {
      return NextResponse.json(
        { error: 'العميل غير موجود' },
        { status: 404 }
      );
    }

    // التحقق من عدم تكرار الهاتف (إذا تم تغييره)
    if (phone && phone !== existingClient.phone) {
      const duplicateClient = await prisma.client.findFirst({
        where: { 
          phone,
          id: { not: parseInt(id) }
        }
      });

      if (duplicateClient) {
        return NextResponse.json(
          { error: 'يوجد عميل آخر بهذا الرقم' },
          { status: 400 }
        );
      }
    }

    const client = await prisma.client.update({
      where: { id: parseInt(id) },
      data: {
        ...(name && { name }),
        ...(phone !== undefined && { phone }),
        ...(email !== undefined && { email }),
        ...(address !== undefined && { address })
      }
    });

    return NextResponse.json(client);
  } catch (error) {
    console.error('خطأ في تحديث العميل:', error);
    return NextResponse.json(
      { error: 'فشل في تحديث العميل' },
      { status: 500 }
    );
  }
}

// DELETE - حذف عميل
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json(
        { error: 'معرف العميل مطلوب' },
        { status: 400 }
      );
    }

    // التحقق من وجود العميل
    const existingClient = await prisma.client.findUnique({
      where: { id: parseInt(id) }
    });

    if (!existingClient) {
      return NextResponse.json(
        { error: 'العميل غير موجود' },
        { status: 404 }
      );
    }

    // التحقق من وجود مبيعات مرتبطة
    const salesCount = await prisma.sale.count({
      where: { clientId: parseInt(id) }
    });

    if (salesCount > 0) {
      return NextResponse.json(
        { error: `لا يمكن حذف العميل لأنه مرتبط بـ ${salesCount} عملية بيع` },
        { status: 400 }
      );
    }

    await prisma.client.delete({
      where: { id: parseInt(id) }
    });

    return NextResponse.json({ message: 'تم حذف العميل بنجاح' });
  } catch (error) {
    console.error('خطأ في حذف العميل:', error);
    return NextResponse.json(
      { error: 'فشل في حذف العميل' },
      { status: 500 }
    );
  }
}
