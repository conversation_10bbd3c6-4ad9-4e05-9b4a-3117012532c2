# 🧪 دليل اختبار تقارير تتبع الجهاز

## خطوات الاختبار السريع:

### 1. اختبار الإعدادات الأساسية
```bash
# تأكد من تشغيل الخادم
npm run dev

# انتقل إلى صفحة الإعدادات
http://localhost:3000/settings

# تحقق من:
✅ ظهور تبويب "المظهر"
✅ إمكانية تعديل أسماء الشركة (عربي/إنجليزي)
✅ إمكانية رفع الشعار
✅ حفظ معلومات الاتصال
✅ حفظ نصوص التذييل
```

### 2. اختبار صفحة تتبع الجهاز
```bash
# انتقل إلى صفحة التتبع
http://localhost:3000/track

# تحقق من:
✅ ظهور حقل البحث
✅ إمكانية البحث بالرقم التسلسلي
✅ ظهور معلومات الجهاز
✅ ظهور الأزرار الجديدة
```

### 3. اختبار معاينة التقرير
```bash
# بعد البحث عن جهاز:
1. انقر على "معاينة التقرير"
2. تحقق من ظهور النافذة المنبثقة
3. جرب تغيير اللغة (عربي/إنجليزي/ثنائي)
4. جرب تفعيل "نسخة العميل"
5. انقر على "إظهار المعاينة"
6. تحقق من ظهور التقرير بالتنسيق الصحيح
```

### 4. اختبار الطباعة والتصدير
```bash
# من نافذة معاينة التقرير:
1. انقر على "طباعة" - يجب أن تفتح نافذة طباعة
2. انقر على "تحميل PDF" - يجب أن يتم تحميل ملف PDF
3. تحقق من محتوى PDF:
   ✅ الترويسة مع الشعار
   ✅ أسماء الشركة بالعربية والإنجليزية
   ✅ معلومات الاتصال
   ✅ محتوى التقرير
   ✅ التذييل مع الطابع الزمني
```

## اختبارات مفصلة:

### اختبار 1: الإعدادات ثنائية اللغة
```javascript
// تحقق من API الإعدادات
fetch('/api/settings')
  .then(res => res.json())
  .then(data => {
    console.log('Settings:', data);
    // يجب أن تحتوي على:
    // companyNameAr, companyNameEn
    // addressAr, addressEn
    // phone, email, website
    // footerTextAr, footerTextEn
  });
```

### اختبار 2: مكون DocumentHeader
```jsx
// تحقق من عرض المكون
import DocumentHeader from '@/components/DocumentHeader';

// يجب أن يعرض:
// - الشعار (إذا كان موجود)
// - أسماء الشركة
// - العناوين
// - معلومات الاتصال
```

### اختبار 3: تقرير تتبع الجهاز
```jsx
// تحقق من عرض التقرير
import DeviceTrackingReport from '@/components/DeviceTrackingReport';

// يجب أن يعرض:
// - معلومات الجهاز الأساسية
// - معلومات البيع (للعملاء)
// - معلومات الضمان (للعملاء)
// - الخط الزمني للأحداث
```

### اختبار 4: معاينة التقرير
```jsx
// تحقق من مكون المعاينة
import ReportPreview from '@/components/ReportPreview';

// يجب أن يحتوي على:
// - خيارات اللغة
// - خيار نسخة العميل
// - معاينة التقرير
// - أزرار الطباعة والتحميل
```

## سيناريوهات الاختبار:

### سيناريو 1: عميل يريد تقرير جهازه
```
1. العميل يدخل الرقم التسلسلي
2. يظهر الجهاز مع معلوماته
3. الموظف ينقر على "معاينة التقرير"
4. يختار "نسخة العميل"
5. يختار "ثنائي اللغة"
6. يطبع التقرير أو يحمله كـ PDF
7. يسلم التقرير للعميل

✅ التقرير يحتوي على:
- معلومات البيع
- معلومات الضمان
- سجل الخدمات (بدون تفاصيل حساسة)
```

### سيناريو 2: تقرير داخلي للإدارة
```
1. الموظف يبحث عن جهاز
2. ينقر على "معاينة التقرير"
3. يختار "النسخة الكاملة"
4. يختار "عربي فقط" أو "ثنائي اللغة"
5. يطبع التقرير للمراجعة الداخلية

✅ التقرير يحتوي على:
- جميع معلومات الجهاز
- السجل الكامل للأحداث
- تفاصيل العمليات
- معلومات الموظفين
```

### سيناريو 3: تقرير للمحاسبة
```
1. المحاسب يبحث عن جهاز
2. ينقر على "معاينة التقرير"
3. يختار "النسخة الكاملة"
4. يختار "إنجليزي فقط" (للتدقيق الخارجي)
5. يحمل PDF للأرشيف

✅ التقرير يحتوي على:
- معلومات مالية دقيقة
- تواريخ العمليات
- أرقام الفواتير والعمليات
```

## مؤشرات النجاح:

### ✅ المظهر والتصميم
- [ ] الترويسة تظهر بشكل صحيح
- [ ] الشعار يظهر في المكان المناسب
- [ ] النصوص العربية واضحة ومقروءة
- [ ] النصوص الإنجليزية واضحة ومقروءة
- [ ] التخطيط متجاوب على الأجهزة المختلفة
- [ ] الألوان والتنسيق احترافي

### ✅ الوظائف الأساسية
- [ ] البحث عن الأجهزة يعمل
- [ ] معاينة التقرير تفتح
- [ ] تغيير اللغة يعمل
- [ ] تغيير نوع التقرير يعمل
- [ ] الطباعة تعمل
- [ ] تحميل PDF يعمل

### ✅ المحتوى والبيانات
- [ ] معلومات الجهاز صحيحة
- [ ] الخط الزمني مرتب زمنياً
- [ ] معلومات البيع دقيقة (للعملاء)
- [ ] معلومات الضمان صحيحة (للعملاء)
- [ ] التذييل يحتوي على الطابع الزمني
- [ ] جميع النصوص مترجمة حسب اللغة المختارة

### ✅ الأداء والاستقرار
- [ ] التحميل سريع
- [ ] لا توجد أخطاء في وحدة التحكم
- [ ] الذاكرة لا تتسرب
- [ ] يعمل على المتصفحات المختلفة
- [ ] يعمل على الأجهزة المختلفة

## تقرير الاختبار:

```
تاريخ الاختبار: ___________
المختبر: ___________
النسخة: ___________

النتائج:
✅ الإعدادات: نجح / فشل
✅ البحث: نجح / فشل  
✅ المعاينة: نجح / فشل
✅ الطباعة: نجح / فشل
✅ التصدير: نجح / فشل
✅ اللغات: نجح / فشل

الملاحظات:
_________________________________
_________________________________
_________________________________

التوصيات:
_________________________________
_________________________________
_________________________________
```

---

## 🎯 الخلاصة

بعد إجراء جميع الاختبارات، يجب أن تكون تقارير تتبع الجهاز:
- تعمل بشكل مثالي على جميع المتصفحات
- تدعم اللغات المختلفة
- تطبع وتصدر بجودة عالية
- تعرض المعلومات بدقة
- سهلة الاستخدام للموظفين والعملاء
