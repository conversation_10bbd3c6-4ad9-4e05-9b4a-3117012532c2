
// Required API endpoints:
// - /api/employee-requests
// - /api/users
'use client';

import { useState, useEffect } from 'react';
// Updated to use APIs
import { useRouter } from 'next/navigation';
import { useToast } from '@/hooks/use-toast';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
  DialogClose,
} from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { format } from 'date-fns';
import {
  Eye,
  CheckCircle,
  XCircle,
  Info,
  ExternalLink,
  Clock,
  AlertTriangle,
  FileText,
  User,
  Calendar,
  Users
} from 'lucide-react';
import type { EmployeeRequest } from '@/lib/types';
import { Label } from '@/components/ui/label';
import { DarkModeToggle } from './DarkModeToggle';
import './enhanced-styles.css';

export default function RequestsPage() {
  // State management
  const [isLoading, setIsLoading] = useState(true);
  const [employeeRequests, setEmployeeRequests] = useState<EmployeeRequest[]>([]);
  const [processEmployeeRequest, setProcessEmployeeRequest] = useState([]);
  const [currentUser, setCurrentUser] = useState({
    name: 'المستخدم الحالي',
    permissions: { requests: { view: true, approve: true, reject: true } }
  });

  const router = useRouter();
  const { toast } = useToast();

  const [selectedRequest, setSelectedRequest] =
    useState<EmployeeRequest | null>(null);
  const [adminNotes, setAdminNotes] = useState('');

  // API functions
  const fetchEmployeeRequests = async () => {
    try {
      const response = await fetch('/api/employee-requests-simple');
      if (!response.ok) {
        console.warn('Employee requests API not available, using empty data');
        setEmployeeRequests([]);
        return;
      }
      const data = await response.json();
      console.log('تم تحميل طلبات الموظفين:', data.length, 'طلب');
      setEmployeeRequests(data);
    } catch (error) {
      console.error('خطأ في تحميل طلبات الموظفين:', error);
      // استخدام بيانات فارغة بدلاً من إظهار خطأ للمستخدم
      setEmployeeRequests([]);
      toast({
        variant: 'default',
        title: 'تنبيه',
        description: 'لم يتم تحميل طلبات الموظفين - سيتم استخدام بيانات فارغة'
      });
    }
  };

  const updateRequestStatus = async (requestId: number, status: string, notes?: string) => {
    try {
      const response = await fetch('/api/employee-requests-simple', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ id: requestId, status, adminNotes: notes })
      });

      if (!response.ok) throw new Error('فشل في تحديث حالة الطلب');

      await fetchEmployeeRequests(); // إعادة تحميل البيانات
      toast({
        title: 'تم التحديث',
        description: 'تم تحديث حالة الطلب بنجاح'
      });
    } catch (error) {
      console.error('خطأ في تحديث حالة الطلب:', error);
      toast({
        variant: 'destructive',
        title: 'خطأ',
        description: 'فشل في تحديث حالة الطلب'
      });
    }
  };

  const fetchAllData = async () => {
    setIsLoading(true);
    await fetchEmployeeRequests();
    setIsLoading(false);
  };

  useEffect(() => {
    fetchAllData();
  }, []);

const permissions = currentUser?.permissions.requests;

  const getPriorityBadge = (priority: EmployeeRequest['priority']) => {
    switch (priority) {
      case 'طاريء':
        return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/20 hover:bg-yellow-500/30';
      case 'طاريء جدا':
        return 'bg-red-500/20 text-red-400 border-red-500/20 hover:bg-red-500/30';
      default:
        return 'bg-blue-500/20 text-blue-400 border-blue-500/20 hover:bg-blue-500/30';
    }
  };

  const getStatusBadge = (status: EmployeeRequest['status']) => {
    switch (status) {
      case 'قيد المراجعة':
        return 'bg-orange-500/20 text-orange-400 border-orange-500/20';
      case 'تم التنفيذ':
        return 'bg-green-500/20 text-green-400 border-green-500/20';
      case 'مرفوض':
        return 'bg-red-500/20 text-red-400 border-red-500/20';
      default:
        return 'bg-gray-500/20 text-gray-400 border-gray-500/20';
    }
  };

  const getRequestTypeIcon = (type: EmployeeRequest['requestType']) => {
    switch (type) {
      case 'تعديل':
        return <FileText className="h-4 w-4" />;
      case 'إعادة نظر':
        return <AlertTriangle className="h-4 w-4" />;
      case 'حذف':
        return <XCircle className="h-4 w-4" />;
      default:
        return <Info className="h-4 w-4" />;
    }
  };

  const handleOpenRequest = (request: EmployeeRequest) => {
    setSelectedRequest(request);
    setAdminNotes(request.adminNotes || '');
  };

  const handleProcessRequest = async (status: 'approved' | 'rejected') => {
    if (!selectedRequest) return;
    if (status === 'rejected' && !adminNotes.trim()) {
      toast({
        title: 'مطلوب',
        description: 'يرجى كتابة سبب الرفض في الملاحظات.',
        variant: 'destructive',
      });
      return;
    }

    const newStatus = status === 'approved' ? 'تم التنفيذ' : 'مرفوض';
    await updateRequestStatus(selectedRequest.id, newStatus, adminNotes);
    setSelectedRequest(null);
  };

  const handleNavigateToOrder = () => {
    if (!selectedRequest) return;

    let path = '';
    switch (selectedRequest.relatedOrderType) {
      case 'supply':
        path = '/supply';
        break;
      case 'sale':
        path = '/sales';
        break;
      case 'return':
        path = '/returns';
        break;
      case 'evaluation':
        path = '/grading';
        break;
      case 'maintenance':
        path = '/maintenance';
        break;
      case 'warehouse_transfer':
        path = '/warehouse-transfer';
        break;
    }

    if (path) {
      // This is a simplified navigation. A full solution would need to also pass the order ID
      // and have the target page load it automatically.
      router.push(path);
      setSelectedRequest(null);
    }
  };

  const renderRequestsTable = (requests: EmployeeRequest[]) => (
    <div className="enhanced-scroll-area">
      <table className="enhanced-table">
        <thead>
          <tr>
            <th className="w-16">#</th>
            <th>رقم الطلب</th>
            <th>الموظف</th>
            <th>الأمر المرتبط</th>
            <th>نوع الطلب</th>
            <th>الأولوية</th>
            <th>تاريخ الطلب</th>
            <th>الحالة</th>
            <th className="text-center">إجراء</th>
          </tr>
        </thead>
        <tbody>
          {requests.length === 0 ? (
            <tr>
              <td colSpan={9} className="h-32 text-center">
                <div className="flex flex-col items-center space-y-3 text-muted-foreground">
                  <FileText className="h-12 w-12 icon-enhanced" />
                  <p className="text-lg font-medium">لا توجد طلبات في هذا القسم</p>
                  <p className="text-sm">ستظهر الطلبات هنا عند إضافتها</p>
                </div>
              </td>
            </tr>
          ) : (
            requests.map((req, index) => (
              <tr key={req.id}>
                <td className="row-number">{index + 1}</td>
                <td className="font-medium">
                  <div className="flex items-center space-x-2 space-x-reverse">
                    <FileText className="h-4 w-4 text-primary icon-enhanced" />
                    <span className="text-primary">{req.requestNumber}</span>
                  </div>
                </td>
                <td>
                  <div className="flex items-center space-x-2 space-x-reverse">
                    <User className="h-4 w-4 text-gray-500 icon-enhanced" />
                    <span>{req.employeeName}</span>
                  </div>
                </td>
                <td>
                  <div className="enhanced-badge bg-blue-500/10 text-blue-600 border-blue-500/20">
                    <ExternalLink className="h-4 w-4 ml-1 icon-enhanced" />
                    {req.relatedOrderDisplayId}
                  </div>
                </td>
                <td>
                  <div className="flex items-center space-x-2 space-x-reverse">
                    <div className="icon-enhanced">
                      {getRequestTypeIcon(req.requestType)}
                    </div>
                    <span>{req.requestType}</span>
                  </div>
                </td>
                <td>
                  <div className={`enhanced-badge ${getPriorityBadge(req.priority)}`}>
                    {req.priority === 'طاريء جدا' && <AlertTriangle className="h-3 w-3 ml-1 icon-enhanced" />}
                    {req.priority === 'طاريء' && <Clock className="h-3 w-3 ml-1 icon-enhanced" />}
                    {req.priority}
                  </div>
                </td>
                <td>
                  <div className="flex items-center space-x-2 space-x-reverse text-sm">
                    <Calendar className="h-4 w-4 text-gray-500 icon-enhanced" />
                    <span>{format(new Date(req.requestDate), 'yyyy/MM/dd HH:mm')}</span>
                  </div>
                </td>
                <td>
                  <div className={`enhanced-badge ${getStatusBadge(req.status)}`}>
                    {req.status}
                  </div>
                </td>
                <td className="text-center">
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => handleOpenRequest(req)}
                    className="enhanced-button hover:bg-primary/10 hover:text-primary"
                  >
                    <Eye className="h-4 w-4 icon-enhanced" />
                  </Button>
                </td>
              </tr>
            ))
          )}
        </tbody>
      </table>
    </div>
  );

  if (!permissions?.view) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-destructive/10">
              <XCircle className="h-6 w-6 text-destructive" />
            </div>
            <CardTitle className="text-xl">غير مصرح بالدخول</CardTitle>
            <CardDescription>
              ليس لديك الصلاحيات اللازمة لعرض هذه الصفحة.
            </CardDescription>
          </CardHeader>
        </Card>
      </div>
    );
  }

  const pendingRequests = employeeRequests.filter((r) => r.status === 'قيد المراجعة');
  const approvedRequests = employeeRequests.filter((r) => r.status === 'تم التنفيذ');
  const rejectedRequests = employeeRequests.filter((r) => r.status === 'مرفوض');

  return (
    <div className="page-container">
      {/* Header Section */}
      <div className="header-card p-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight flex items-center space-x-3 space-x-reverse">
              <div className="p-3 rounded-full bg-gradient-to-br from-primary/20 to-primary/10 icon-enhanced">
                <Users className="h-8 w-8 text-primary" />
              </div>
              <span className="bg-gradient-to-r from-primary to-primary/80 bg-clip-text text-transparent">
                طلبات الموظفين
              </span>
            </h1>
            <p className="text-muted-foreground mt-2 text-lg">
              إدارة ومراجعة طلبات الموظفين للتعديل والحذف وإعادة النظر
            </p>
          </div>
          <div className="flex items-center space-x-3 space-x-reverse">
            {/* زر الوضع الليلي */}
            <DarkModeToggle
              size="md"
              variant="outline"
              className="enhanced-button"
            />
            {/* شارات الإحصائيات */}
            <div className="enhanced-badge bg-orange-500/10 text-orange-600 border-orange-500/20">
              <Clock className="h-4 w-4 ml-1 icon-enhanced" />
              قيد المراجعة: {pendingRequests.length}
            </div>
            <div className="enhanced-badge bg-green-500/10 text-green-600 border-green-500/20">
              <CheckCircle className="h-4 w-4 ml-1 icon-enhanced" />
              منفذة: {approvedRequests.length}
            </div>
            <div className="enhanced-badge bg-red-500/10 text-red-600 border-red-500/20">
              <XCircle className="h-4 w-4 ml-1 icon-enhanced" />
              مرفوضة: {rejectedRequests.length}
            </div>
          </div>
        </div>
      </div>

      <Card className="enhanced-card card-primary mt-6">
        <CardContent className="p-6">
          <Tabs defaultValue="pending" className="w-full">
            <TabsList className="grid w-full grid-cols-3 bg-muted/50 enhanced-card">
              <TabsTrigger
                value="pending"
                className="data-[state=active]:bg-orange-500/20 data-[state=active]:text-orange-600 enhanced-button"
              >
                <Clock className="h-4 w-4 ml-2 icon-enhanced" />
                طلبات قيد المراجعة ({pendingRequests.length})
              </TabsTrigger>
              <TabsTrigger
                value="approved"
                className="data-[state=active]:bg-green-500/20 data-[state=active]:text-green-600 enhanced-button"
              >
                <CheckCircle className="h-4 w-4 ml-2 icon-enhanced" />
                طلبات منفذة ({approvedRequests.length})
              </TabsTrigger>
              <TabsTrigger
                value="rejected"
                className="data-[state=active]:bg-red-500/20 data-[state=active]:text-red-600 enhanced-button"
              >
                <XCircle className="h-4 w-4 ml-2 icon-enhanced" />
                طلبات مرفوضة ({rejectedRequests.length})
              </TabsTrigger>
            </TabsList>
            <TabsContent value="pending" className="mt-6">
              {renderRequestsTable(pendingRequests)}
            </TabsContent>
            <TabsContent value="approved" className="mt-6">
              {renderRequestsTable(approvedRequests)}
            </TabsContent>
            <TabsContent value="rejected" className="mt-6">
              {renderRequestsTable(rejectedRequests)}
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      <Dialog
        open={!!selectedRequest}
        onOpenChange={(open) => !open && setSelectedRequest(null)}
      >
        <DialogContent className="enhanced-dialog sm:max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader className="enhanced-dialog-header space-y-3">
            <div className="flex items-center space-x-3 space-x-reverse">
              <div className={`p-3 rounded-full icon-enhanced ${
                selectedRequest?.status === 'قيد المراجعة'
                  ? 'bg-orange-500/20 text-orange-600'
                  : selectedRequest?.status === 'تم التنفيذ'
                  ? 'bg-green-500/20 text-green-600'
                  : 'bg-red-500/20 text-red-600'
              }`}>
                {selectedRequest?.status === 'قيد المراجعة' && <Clock className="h-6 w-6 icon-enhanced" />}
                {selectedRequest?.status === 'تم التنفيذ' && <CheckCircle className="h-6 w-6 icon-enhanced" />}
                {selectedRequest?.status === 'مرفوض' && <XCircle className="h-6 w-6 icon-enhanced" />}
              </div>
              <div>
                <DialogTitle className="enhanced-dialog-title text-xl">
                  مراجعة الطلب: {selectedRequest?.requestNumber}
                </DialogTitle>
                <DialogDescription className="enhanced-dialog-description text-base">
                  من: {selectedRequest?.employeeName} - بتاريخ:{' '}
                  {selectedRequest &&
                    format(new Date(selectedRequest.requestDate), 'yyyy/MM/dd HH:mm')}
                </DialogDescription>
              </div>
            </div>
          </DialogHeader>

          <div className="space-y-6 py-4">
            {/* Request Overview */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Card className="enhanced-card card-info p-4">
                <div className="flex items-center space-x-2 space-x-reverse">
                  <ExternalLink className="h-4 w-4 text-blue-600 icon-enhanced" />
                  <span className="text-sm font-medium text-gray-700">الأمر المرتبط</span>
                </div>
                <p className="text-lg font-semibold mt-2 text-blue-600">
                  {selectedRequest?.relatedOrderDisplayId}
                </p>
              </Card>

              <Card className="enhanced-card card-primary p-4">
                <div className="flex items-center space-x-2 space-x-reverse">
                  <div className="icon-enhanced">
                    {getRequestTypeIcon(selectedRequest?.requestType as any)}
                  </div>
                  <span className="text-sm font-medium text-gray-700">نوع الطلب</span>
                </div>
                <p className="text-lg font-semibold mt-2 text-primary">
                  {selectedRequest?.requestType}
                </p>
              </Card>

              <Card className="enhanced-card card-warning p-4">
                <div className="flex items-center space-x-2 space-x-reverse">
                  <AlertTriangle className="h-4 w-4 text-yellow-600 icon-enhanced" />
                  <span className="text-sm font-medium text-gray-700">الأولوية</span>
                </div>
                <div className="mt-2">
                  <div className={`enhanced-badge ${getPriorityBadge(selectedRequest?.priority as any)} text-sm`}>
                    {selectedRequest?.priority === 'طاريء جدا' && <AlertTriangle className="h-3 w-3 ml-1 icon-enhanced" />}
                    {selectedRequest?.priority === 'طاريء' && <Clock className="h-3 w-3 ml-1 icon-enhanced" />}
                    {selectedRequest?.priority}
                  </div>
                </div>
              </Card>
            </div>
            {/* Request Details */}
            <Card className="enhanced-card card-primary">
              <CardHeader>
                <CardTitle className="text-lg flex items-center space-x-2 space-x-reverse">
                  <FileText className="h-5 w-5 text-primary icon-enhanced" />
                  <span>تفاصيل المشكلة</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-lg p-4 border border-blue-100">
                  <p className="text-sm leading-relaxed whitespace-pre-wrap text-gray-800">
                    {selectedRequest?.notes}
                  </p>
                </div>
                {selectedRequest?.attachmentName && (
                  <div className="flex items-center space-x-2 space-x-reverse text-sm bg-blue-50 rounded-lg p-3 border border-blue-200">
                    <FileText className="h-4 w-4 text-blue-600 icon-enhanced" />
                    <span className="text-blue-800 font-medium">المرفق: {selectedRequest.attachmentName}</span>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Admin Response */}
            {selectedRequest?.status !== 'قيد المراجعة' && (
              <Card
                className={`enhanced-card ${
                  selectedRequest?.status === 'مرفوض'
                    ? 'card-danger'
                    : 'card-success'
                }`}
              >
                <CardHeader>
                  <CardTitle className="text-lg flex items-center space-x-2 space-x-reverse">
                    {selectedRequest?.status === 'مرفوض' ? (
                      <XCircle className="h-5 w-5 text-red-600 icon-enhanced" />
                    ) : (
                      <CheckCircle className="h-5 w-5 text-green-600 icon-enhanced" />
                    )}
                    <span>رد الإدارة</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className={`rounded-lg p-4 border ${
                    selectedRequest?.status === 'مرفوض'
                      ? 'bg-gradient-to-br from-red-50 to-pink-50 border-red-200'
                      : 'bg-gradient-to-br from-green-50 to-emerald-50 border-green-200'
                  }`}>
                    <p className="text-sm leading-relaxed whitespace-pre-wrap text-gray-800">
                      {selectedRequest?.adminNotes || 'لا توجد ملاحظات.'}
                    </p>
                  </div>
                  <div className={`flex items-center space-x-2 space-x-reverse text-xs rounded-lg p-2 ${
                    selectedRequest?.status === 'مرفوض'
                      ? 'bg-red-50 text-red-700'
                      : 'bg-green-50 text-green-700'
                  }`}>
                    <Calendar className="h-3 w-3 icon-enhanced" />
                    <span className="font-medium">
                      الحالة: {selectedRequest?.status} - بتاريخ:{' '}
                      {selectedRequest?.resolutionDate &&
                        format(
                          new Date(selectedRequest.resolutionDate),
                          'yyyy/MM/dd HH:mm',
                        )}
                    </span>
                  </div>
                </CardContent>
              </Card>
            )}
            {/* Admin Notes Input */}
            {selectedRequest?.status === 'قيد المراجعة' && permissions.edit && (
              <Card className="enhanced-card card-warning">
                <CardHeader>
                  <CardTitle className="text-lg flex items-center space-x-2 space-x-reverse">
                    <FileText className="h-5 w-5 text-yellow-600 icon-enhanced" />
                    <span>ملاحظات الإدارة</span>
                  </CardTitle>
                  <CardDescription className="text-gray-600">
                    يرجى كتابة ملاحظاتك حول الطلب. الملاحظات إلزامية عند رفض الطلب.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Textarea
                    id="admin-notes"
                    value={adminNotes}
                    onChange={(e) => setAdminNotes(e.target.value)}
                    placeholder="اكتب ملاحظاتك هنا..."
                    className="min-h-[100px] resize-none enhanced-input"
                  />
                </CardContent>
              </Card>
            )}
          </div>

          <DialogFooter className="flex-col sm:flex-row gap-3 pt-6 border-t border-gray-200">
            <div className="flex gap-3 w-full sm:w-auto">
              <Button
                variant="outline"
                onClick={handleNavigateToOrder}
                className="enhanced-button variant-outline flex-1 sm:flex-none"
              >
                <ExternalLink className="ml-2 h-4 w-4 icon-enhanced" />
                فتح الأمر المرتبط
              </Button>
            </div>

            <div className="flex gap-3 w-full sm:w-auto">
              {selectedRequest?.status === 'قيد المراجعة' && permissions.edit ? (
                <>
                  <Button
                    variant="destructive"
                    onClick={() => handleProcessRequest('rejected')}
                    disabled={!adminNotes.trim()}
                    className="enhanced-button variant-destructive flex-1 sm:flex-none"
                  >
                    <XCircle className="ml-2 h-4 w-4 icon-enhanced" />
                    رفض الطلب
                  </Button>
                  <Button
                    onClick={() => handleProcessRequest('approved')}
                    className="enhanced-button bg-green-600 hover:bg-green-700 text-white flex-1 sm:flex-none"
                  >
                    <CheckCircle className="ml-2 h-4 w-4 icon-enhanced" />
                    تم التنفيذ
                  </Button>
                </>
              ) : (
                <DialogClose asChild>
                  <Button variant="outline" className="enhanced-button variant-outline flex-1 sm:flex-none">
                    إغلاق
                  </Button>
                </DialogClose>
              )}
            </div>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
