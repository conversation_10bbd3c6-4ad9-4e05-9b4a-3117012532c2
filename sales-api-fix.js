// Script to update Sales functions to use API endpoints

const fs = require('fs');
const path = require('path');

// Define the updated functions
const updateSaleFix = `  const updateSale = async (updatedSale: Sale) => {
    try {
      const response = await fetch(\`/api/sales/\${updatedSale.id}\`, {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(updatedSale),
      });

      if (!response.ok) {
        throw new Error("Failed to update sale");
      }

      const updatedSaleResult = await response.json();

      const originalSale = sales.find((s) => s.id === updatedSale.id);
      if (!originalSale) return;

      // Devices removed from the sale should be set back to 'متاح للبيع'
      originalSale.items.forEach((item) => {
        if (
          !updatedSale.items.some((newItem) => newItem.deviceId === item.deviceId)
        ) {
          updateDeviceStatus(item.deviceId, "متاح للبيع");
        }
      });

      // Devices added or kept in the sale should be set to 'مباع'
      updatedSale.items.forEach((item) => {
        updateDeviceStatus(item.deviceId, "مباع");
      });

      setSales((prev) =>
        prev.map((s) => (s.id === updatedSale.id ? updatedSaleResult : s)),
      );
      addActivity({
        type: "sale",
        description: \`تم تحديث الفاتورة \${updatedSale.soNumber}\`,
      });
      
      return updatedSaleResult;
    } catch (error) {
      console.error("Failed to update sale:", error);
      throw error;
    }
  };`;

const deleteSaleFix = `  const deleteSale = async (saleId: number) => {
    try {
      // التحقق من العلاقات أولاً
      const relationCheck = checkSaleRelations(saleId);
      if (!relationCheck.canDelete) {
        throw new Error(
          \`لا يمكن حذف فاتورة المبيعات: \${relationCheck.reason}\${relationCheck.relatedOperations ? "\\nالعمليات المرتبطة: " + relationCheck.relatedOperations.join(", ") : ""}\`,
        );
      }

      const saleToDelete = sales.find((s) => s.id === saleId);
      if (!saleToDelete) return;

      const response = await fetch(\`/api/sales/\${saleId}\`, {
        method: "DELETE",
      });

      if (!response.ok) {
        throw new Error("Failed to delete sale");
      }

      // Revert devices to 'متاح للبيع' status
      saleToDelete.items.forEach((item) => {
        updateDeviceStatus(item.deviceId, "متاح للبيع");
      });

      setSales((prev) => prev.filter((s) => s.id !== saleId));
      addActivity({
        type: "sale",
        description: \`تم حذف الفاتورة \${saleToDelete.soNumber}\`,
      });
    } catch (error) {
      console.error("Failed to delete sale:", error);
      throw error;
    }
  };`;

// Read the store.tsx file
const filePath = path.join(__dirname, 'context', 'store.tsx');
let content = fs.readFileSync(filePath, 'utf8');

// Replace the functions
content = content.replace(/const updateSale = \(updatedSale: Sale\) => \{[\s\S]*?description: `تم تحديث الفاتورة \${updatedSale\.soNumber}`[\s\S]*?\}\);(\s*\}\;)/s, updateSaleFix);
content = content.replace(/const deleteSale = \(saleId: number\) => \{[\s\S]*?description: `تم حذف الفاتورة \${saleToDelete\.soNumber}`[\s\S]*?\}\);(\s*\}\;)/s, deleteSaleFix);

// Write the updated content back to the file
fs.writeFileSync(filePath, content);
console.log('Sales functions updated successfully to use API endpoints.');
