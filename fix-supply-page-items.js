// إصلاح جميع استخدامات order.items في supply page
const fs = require('fs');

const supplyPageFile = 'c:\\Users\\<USER>\\Downloads\\111\\13\\app\\(main)\\supply\\page.tsx';
let content = fs.readFileSync(supplyPageFile, 'utf8');

// قائمة الاستبدالات
const replacements = [
  {
    from: 'order.items.some((item) => item.imei === cleanImei)',
    to: 'ensureItemsArray(order.items).some((item) => item.imei === cleanImei)'
  },
  {
    from: 'returnOrder.items.some(item => item.deviceId === imei)',
    to: 'ensureItemsArray(returnOrder.items).some(item => item.deviceId === imei)'
  },
  {
    from: 'evalOrder.items.some(item => item.deviceId === imei)',
    to: 'ensureItemsArray(evalOrder.items).some(item => item.deviceId === imei)'
  },
  {
    from: 'order.items.some(item => item.id === imei)',
    to: 'ensureItemsArray(order.items).some(item => item.id === imei)'
  },
  {
    from: 'order.items.some(item => item.deviceId === imei)',
    to: 'ensureItemsArray(order.items).some(item => item.deviceId === imei)'
  },
  {
    from: 'order.items.some(item => item.imei === imei)',
    to: 'ensureItemsArray(order.items).some(item => item.imei === imei)'
  },
  {
    from: '{order.items.length}',
    to: '{ensureItemsArray(order.items).length}'
  }
];

let changesMade = 0;

replacements.forEach(replacement => {
  const regex = new RegExp(replacement.from.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g');
  const matches = content.match(regex);
  if (matches) {
    content = content.replace(regex, replacement.to);
    changesMade += matches.length;
    console.log(`✅ استبدال "${replacement.from}" -> "${replacement.to}" (${matches.length} مرة)`);
  }
});

if (changesMade > 0) {
  fs.writeFileSync(supplyPageFile, content, 'utf8');
  console.log(`\\n🎉 تم إجراء ${changesMade} تغيير في supply page`);
} else {
  console.log('⚠️ لم يتم العثور على تغييرات مطلوبة');
}
