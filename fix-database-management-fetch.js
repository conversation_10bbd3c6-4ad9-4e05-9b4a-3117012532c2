const fs = require('fs');
const path = require('path');

// قراءة ملف DatabaseManagement
const filePath = path.join(__dirname, 'components', 'database-management.tsx');
let content = fs.readFileSync(filePath, 'utf8');

console.log('🔧 تحديث جميع استخدامات fetch في database-management.tsx...');

// استبدال جميع fetch POST requests
const postReplacements = [
  {
    from: /await fetch\('([^']+)', \{\s*method: 'POST',\s*headers: \{ 'Content-Type': 'application\/json' \},\s*body: JSON\.stringify\(([^)]+)\),?\s*\}\)/g,
    to: 'await apiClient.post(\'$1\', $2)'
  }
];

// استبدال جميع fetch PUT requests
const putReplacements = [
  {
    from: /await fetch\('([^']+)', \{\s*method: 'PUT',\s*headers: \{ 'Content-Type': 'application\/json' \},\s*body: JSON\.stringify\(([^)]+)\),?\s*\}\)/g,
    to: 'await apiClient.put(\'$1\', $2)'
  }
];

// استبدال جميع fetch DELETE requests
const deleteReplacements = [
  {
    from: /await fetch\('([^']+)', \{\s*method: 'DELETE',\s*headers: \{ 'Content-Type': 'application\/json' \},\s*body: JSON\.stringify\(([^)]+)\),?\s*\}\)/g,
    to: 'await apiClient.delete(\'$1\', $2)'
  }
];

let changesMade = 0;

// تطبيق POST replacements
postReplacements.forEach(replacement => {
  const matches = content.match(replacement.from);
  if (matches) {
    content = content.replace(replacement.from, replacement.to);
    changesMade += matches.length;
    console.log(`✅ تم تحديث ${matches.length} POST request(s)`);
  }
});

// تطبيق PUT replacements
putReplacements.forEach(replacement => {
  const matches = content.match(replacement.from);
  if (matches) {
    content = content.replace(replacement.from, replacement.to);
    changesMade += matches.length;
    console.log(`✅ تم تحديث ${matches.length} PUT request(s)`);
  }
});

// تطبيق DELETE replacements
deleteReplacements.forEach(replacement => {
  const matches = content.match(replacement.from);
  if (matches) {
    content = content.replace(replacement.from, replacement.to);
    changesMade += matches.length;
    console.log(`✅ تم تحديث ${matches.length} DELETE request(s)`);
  }
});

// كتابة الملف المحدث
if (changesMade > 0) {
  fs.writeFileSync(filePath, content, 'utf8');
  console.log(`\n🎉 تم تحديث ${changesMade} تغيير في database-management.tsx`);
  console.log('✅ الآن جميع طلبات API تستخدم apiClient مع headers التفويض الصحيحة');
} else {
  console.log('⚠️ لم يتم العثور على تحديثات مطلوبة أو تمت بالفعل');
}

console.log('\n📝 ملاحظة: قد تحتاج إلى مراجعة يدوية لحالات خاصة');
