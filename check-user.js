const { PrismaClient } = require('@prisma/client');

async function checkUser() {
  const prisma = new PrismaClient();
  
  try {
    const adminUser = await prisma.user.findFirst({
      where: { username: 'admin' }
    });
    
    console.log('Admin user in DB:', {
      id: adminUser?.id,
      username: adminUser?.username,
      role: adminUser?.role,
      email: adminUser?.email
    });
    
    // Test permission check
    function hasPermission(userRole, requiredRole) {
      const roleHierarchy = {
        'admin': 3,
        'manager': 2,
        'user': 1,
        'guest': 0
      };

      const userLevel = roleHierarchy[userRole] || 0;
      const requiredLevel = roleHierarchy[requiredRole] || 0;

      return userLevel >= requiredLevel;
    }
    
    console.log('Permission check:');
    console.log('admin >= user:', hasPermission('admin', 'user'));
    console.log('admin >= manager:', hasPermission('admin', 'manager'));
    console.log('admin >= admin:', hasPermission('admin', 'admin'));
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkUser();
