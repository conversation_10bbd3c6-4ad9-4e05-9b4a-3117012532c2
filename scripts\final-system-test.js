const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function finalSystemTest() {
  console.log('🎯 الاختبار النهائي الشامل للنظام...\n');

  try {
    // 1. اختبار جميع النماذج مع العلاقات
    console.log('📊 اختبار جميع النماذج مع العلاقات...');
    
    const models = [
      { name: 'SupplyOrder', model: 'supplyOrder', description: 'أوامر التوريد' },
      { name: 'Sale', model: 'sale', description: 'المبيعات' },
      { name: 'Return', model: 'return', description: 'المرتجعات' },
      { name: 'EvaluationOrder', model: 'evaluationOrder', description: 'أوامر التقييم' },
      { name: 'MaintenanceOrder', model: 'maintenanceOrder', description: 'أوامر الصيانة' },
      { name: 'DeliveryOrder', model: 'deliveryOrder', description: 'أوامر التسليم' },
      { name: 'MaintenanceReceiptOrder', model: 'maintenanceReceiptOrder', description: 'أوامر استلام الصيانة' }
    ];

    let allModelsWorking = true;

    for (const modelInfo of models) {
      try {
        const records = await prisma[modelInfo.model].findMany({
          include: { items: true },
          take: 2
        });
        
        const itemsCount = records.reduce((total, record) => {
          return total + (Array.isArray(record.items) ? record.items.length : 0);
        }, 0);

        console.log(`✅ ${modelInfo.description}: ${records.length} سجل، ${itemsCount} عنصر`);
      } catch (error) {
        console.log(`❌ ${modelInfo.description}: خطأ - ${error.message}`);
        allModelsWorking = false;
      }
    }

    // 2. اختبار إنشاء سجل جديد مع عناصر
    console.log('\n🆕 اختبار إنشاء سجل جديد مع عناصر...');
    
    try {
      const testOrder = await prisma.supplyOrder.create({
        data: {
          supplyOrderId: `FINAL-TEST-${Date.now()}`,
          supplierId: 1,
          supplyDate: new Date().toISOString(),
          warehouseId: 1,
          employeeName: 'اختبار نهائي',
          notes: 'اختبار النظام النهائي',
          items: {
            create: [
              {
                imei: `FINAL-TEST-${Date.now()}`,
                model: 'Test Model Final',
                manufacturer: 'Test Manufacturer',
                condition: 'جديد'
              },
              {
                imei: `FINAL-TEST-${Date.now() + 1}`,
                model: 'Test Model Final 2',
                manufacturer: 'Test Manufacturer',
                condition: 'مستخدم'
              }
            ]
          }
        },
        include: { items: true }
      });

      console.log(`✅ تم إنشاء أمر توريد: ${testOrder.supplyOrderId} مع ${testOrder.items.length} عنصر`);

      // 3. اختبار تحديث السجل
      const updatedOrder = await prisma.supplyOrder.update({
        where: { id: testOrder.id },
        data: {
          notes: 'تم تحديث الملاحظات - اختبار نهائي',
          items: {
            create: [
              {
                imei: `FINAL-TEST-UPDATE-${Date.now()}`,
                model: 'Updated Model',
                manufacturer: 'Updated Manufacturer',
                condition: 'جديد'
              }
            ]
          }
        },
        include: { items: true }
      });

      console.log(`✅ تم تحديث الأمر: ${updatedOrder.items.length} عنصر إجمالي`);

      // 4. اختبار حذف السجل
      await prisma.supplyOrderItem.deleteMany({
        where: { supplyOrderId: testOrder.id }
      });
      await prisma.supplyOrder.delete({
        where: { id: testOrder.id }
      });

      console.log(`✅ تم حذف السجل الاختباري بنجاح`);

    } catch (error) {
      console.log(`❌ خطأ في اختبار CRUD: ${error.message}`);
      allModelsWorking = false;
    }

    // 5. اختبار العمليات المعقدة
    console.log('\n🔍 اختبار العمليات المعقدة...');
    
    try {
      // اختبار الاستعلامات المعقدة
      const complexQuery = await prisma.supplyOrder.findMany({
        where: {
          items: {
            some: {
              condition: 'جديد'
            }
          }
        },
        include: {
          items: {
            where: {
              condition: 'جديد'
            }
          }
        },
        take: 3
      });

      console.log(`✅ استعلام معقد: ${complexQuery.length} أوامر توريد تحتوي على أجهزة جديدة`);

      // اختبار التجميع
      const aggregation = await prisma.supplyOrderItem.groupBy({
        by: ['condition'],
        _count: {
          id: true
        }
      });

      console.log(`✅ تجميع البيانات: ${aggregation.length} مجموعات حسب الحالة`);
      aggregation.forEach(group => {
        console.log(`   - ${group.condition}: ${group._count.id} عنصر`);
      });

    } catch (error) {
      console.log(`❌ خطأ في العمليات المعقدة: ${error.message}`);
      allModelsWorking = false;
    }

    // 6. اختبار الأداء
    console.log('\n⚡ اختبار الأداء...');
    
    try {
      const startTime = Date.now();
      
      const performanceTest = await Promise.all([
        prisma.supplyOrder.findMany({ include: { items: true }, take: 10 }),
        prisma.sale.findMany({ include: { items: true }, take: 10 }),
        prisma.return.findMany({ include: { items: true }, take: 10 }),
        prisma.maintenanceOrder.findMany({ include: { items: true }, take: 10 })
      ]);
      
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      const totalRecords = performanceTest.reduce((sum, result) => sum + result.length, 0);
      
      console.log(`✅ اختبار الأداء: ${totalRecords} سجل في ${duration}ms`);
      
      if (duration < 2000) {
        console.log(`🚀 الأداء ممتاز: أقل من 2 ثانية`);
      } else if (duration < 5000) {
        console.log(`⚡ الأداء جيد: أقل من 5 ثواني`);
      } else {
        console.log(`⚠️ الأداء بطيء: أكثر من 5 ثواني`);
      }

    } catch (error) {
      console.log(`❌ خطأ في اختبار الأداء: ${error.message}`);
      allModelsWorking = false;
    }

    // النتيجة النهائية
    if (allModelsWorking) {
      console.log('\n🎉 النظام يعمل بشكل مثالي!');
      console.log('✅ جميع النماذج والعلاقات تعمل بشكل صحيح');
      console.log('✅ عمليات CRUD تعمل بشكل صحيح');
      console.log('✅ الاستعلامات المعقدة تعمل بشكل صحيح');
      console.log('✅ الأداء مقبول');
      console.log('\n🚀 النظام جاهز للإنتاج!');
      return true;
    } else {
      console.log('\n⚠️ هناك بعض المشاكل في النظام');
      return false;
    }

  } catch (error) {
    console.error('❌ خطأ عام في الاختبار:', error);
    return false;
  } finally {
    await prisma.$disconnect();
  }
}

if (require.main === module) {
  finalSystemTest()
    .then((success) => {
      process.exit(success ? 0 : 1);
    })
    .catch((error) => {
      console.error('💥 فشل الاختبار النهائي:', error);
      process.exit(1);
    });
}

module.exports = { finalSystemTest };
