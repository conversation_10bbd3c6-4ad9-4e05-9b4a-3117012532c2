const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testMaintenancePageFix() {
  console.log('🔧 اختبار إصلاح صفحة maintenance...\n');

  try {
    // اختبار تحميل أوامر الصيانة مع العناصر
    console.log('📋 اختبار تحميل أوامر الصيانة...');
    const maintenanceOrders = await prisma.maintenanceOrder.findMany({
      include: { items: true },
      take: 5
    });
    
    console.log(`✅ تم تحميل ${maintenanceOrders.length} أوامر صيانة`);
    
    // اختبار العمليات التي كانت تسبب الأخطاء
    console.log('\n🧪 اختبار العمليات المُصلحة...');
    
    // محاكاة devicesReceivedDirectlyInMaintenance
    const directOrders = maintenanceOrders.filter(order => order.source === 'direct');
    const deviceIds = new Set(
      directOrders.flatMap(order => 
        Array.isArray(order.items) ? order.items.map(item => item.id) : []
      )
    );
    console.log(`✅ تم معالجة ${directOrders.length} أوامر مباشرة مع ${deviceIds.size} أجهزة`);

    // اختبار تحميل المبيعات والمرتجعات
    console.log('\n💰 اختبار تحميل المبيعات والمرتجعات...');
    const sales = await prisma.sale.findMany({
      include: { items: true },
      take: 3
    });
    
    const returns = await prisma.return.findMany({
      include: { items: true },
      take: 3
    });

    console.log(`✅ تم تحميل ${sales.length} مبيعات و ${returns.length} مرتجعات`);

    // محاكاة checkDeviceRelations
    const testDeviceId = 'TEST123';
    let relatedOps = [];

    // فحص المبيعات
    const deviceInSales = sales?.some(sale =>
      Array.isArray(sale.items) && sale.items.some(item => item.deviceId === testDeviceId)
    );
    if (deviceInSales) relatedOps.push('مبيعات');

    // فحص المرتجعات
    const deviceInReturns = returns?.some(returnOrder =>
      Array.isArray(returnOrder.items) && returnOrder.items.some(item => item.deviceId === testDeviceId)
    );
    if (deviceInReturns) relatedOps.push('مرتجعات');

    console.log(`✅ تم فحص العلاقات للجهاز ${testDeviceId}: ${relatedOps.length} علاقات موجودة`);

    // اختبار البحث عن الأوامر
    console.log('\n🔍 اختبار البحث عن الأوامر...');
    
    if (maintenanceOrders.length > 0) {
      const firstOrder = maintenanceOrders[0];
      const orderItems = Array.isArray(firstOrder.items) ? firstOrder.items : [];
      
      if (orderItems.length > 0) {
        const testDeviceId = orderItems[0].deviceId;
        
        // البحث عن أمر يحتوي على جهاز معين
        const foundOrder = maintenanceOrders.find(o =>
          Array.isArray(o.items) && o.items.some(i => i.deviceId === testDeviceId)
        );
        
        console.log(`✅ تم العثور على أمر للجهاز ${testDeviceId}: ${foundOrder ? foundOrder.orderNumber : 'غير موجود'}`);
      } else {
        console.log('ℹ️ لا توجد عناصر في الأمر الأول للاختبار');
      }
    }

    // اختبار تحميل أوامر التسليم
    console.log('\n🚚 اختبار تحميل أوامر التسليم...');
    const deliveryOrders = await prisma.deliveryOrder.findMany({
      include: { items: true },
      take: 3
    });
    
    console.log(`✅ تم تحميل ${deliveryOrders.length} أوامر تسليم`);
    
    // اختبار فحص أوامر التسليم
    const hasDeliveryOrder = deliveryOrders.some(order =>
      Array.isArray(order.items) && order.items.some(item => item.deviceId === testDeviceId)
    );
    
    console.log(`✅ تم فحص أوامر التسليم للجهاز ${testDeviceId}: ${hasDeliveryOrder ? 'موجود' : 'غير موجود'}`);

    console.log('\n🎉 جميع الاختبارات نجحت! صفحة maintenance تعمل بشكل صحيح');
    return true;

  } catch (error) {
    console.error('❌ خطأ في الاختبار:', error);
    return false;
  } finally {
    await prisma.$disconnect();
  }
}

if (require.main === module) {
  testMaintenancePageFix()
    .then((success) => {
      process.exit(success ? 0 : 1);
    })
    .catch((error) => {
      console.error('💥 فشل الاختبار:', error);
      process.exit(1);
    });
}

module.exports = { testMaintenancePageFix };
