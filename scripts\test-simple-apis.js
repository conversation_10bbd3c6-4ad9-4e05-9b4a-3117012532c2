const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testSimpleAPIs() {
  console.log('🧪 اختبار APIs المبسطة...\n');

  const testResults = {
    database: { connected: false, models: 0 },
    apis: { tested: 0, working: 0, errors: [] }
  };

  // 1. اختبار الاتصال بقاعدة البيانات
  console.log('🗄️ اختبار الاتصال بقاعدة البيانات...');
  try {
    await prisma.$connect();
    testResults.database.connected = true;
    console.log('   ✅ تم الاتصال بقاعدة البيانات بنجاح');

    // اختبار النماذج الأساسية
    const models = [
      { name: 'warehouse', model: prisma.warehouse },
      { name: 'client', model: prisma.client },
      { name: 'supplier', model: prisma.supplier },
      { name: 'user', model: prisma.user },
      { name: 'device', model: prisma.device }
    ];

    for (const modelInfo of models) {
      try {
        await modelInfo.model.findFirst();
        testResults.database.models++;
        console.log(`   ✅ نموذج ${modelInfo.name} يعمل`);
      } catch (error) {
        console.log(`   ❌ نموذج ${modelInfo.name}: ${error.message}`);
      }
    }

  } catch (error) {
    console.log(`   ❌ فشل الاتصال بقاعدة البيانات: ${error.message}`);
  }

  // 2. اختبار APIs المبسطة
  console.log('\n🔌 اختبار APIs المبسطة...');
  
  const apisToTest = [
    { name: 'warehouses-simple', description: 'المخازن' },
    { name: 'clients-simple', description: 'العملاء' },
    { name: 'suppliers-simple', description: 'الموردين' }
  ];

  for (const api of apisToTest) {
    testResults.apis.tested++;
    
    try {
      // اختبار GET
      console.log(`   🔍 اختبار ${api.description}...`);
      
      // محاكاة طلب GET
      const getResult = await testAPIEndpoint(api.name, 'GET');
      if (getResult.success) {
        console.log(`      ✅ GET ${api.description}: يعمل`);
        testResults.apis.working++;
      } else {
        console.log(`      ❌ GET ${api.description}: ${getResult.error}`);
        testResults.apis.errors.push(`${api.name} GET: ${getResult.error}`);
      }

    } catch (error) {
      console.log(`   ❌ خطأ في اختبار ${api.description}: ${error.message}`);
      testResults.apis.errors.push(`${api.name}: ${error.message}`);
    }
  }

  // 3. اختبار إنشاء وحذف بيانات تجريبية
  console.log('\n🧪 اختبار العمليات CRUD...');
  
  try {
    // إنشاء مخزن تجريبي
    const testWarehouse = await prisma.warehouse.create({
      data: {
        name: `مخزن اختبار ${Date.now()}`,
        location: 'موقع اختبار',
        type: 'main',
        isActive: true
      }
    });
    console.log('   ✅ إنشاء مخزن تجريبي: نجح');

    // تحديث المخزن
    await prisma.warehouse.update({
      where: { id: testWarehouse.id },
      data: { location: 'موقع محدث' }
    });
    console.log('   ✅ تحديث مخزن تجريبي: نجح');

    // حذف المخزن
    await prisma.warehouse.delete({
      where: { id: testWarehouse.id }
    });
    console.log('   ✅ حذف مخزن تجريبي: نجح');

  } catch (error) {
    console.log(`   ❌ خطأ في اختبار CRUD: ${error.message}`);
  }

  // 4. النتائج النهائية
  console.log('\n📊 ملخص النتائج:');
  console.log(`🗄️ قاعدة البيانات:`);
  console.log(`   - الاتصال: ${testResults.database.connected ? '✅ متصل' : '❌ غير متصل'}`);
  console.log(`   - النماذج العاملة: ${testResults.database.models}/5`);

  console.log(`\n🔌 APIs:`);
  console.log(`   - تم اختبارها: ${testResults.apis.tested}`);
  console.log(`   - تعمل بشكل صحيح: ${testResults.apis.working}`);
  console.log(`   - نسبة النجاح: ${Math.round((testResults.apis.working / testResults.apis.tested) * 100)}%`);

  if (testResults.apis.errors.length > 0) {
    console.log(`\n❌ الأخطاء:`);
    testResults.apis.errors.forEach(error => {
      console.log(`   - ${error}`);
    });
  }

  // 5. التقييم العام
  const dbScore = testResults.database.connected ? 30 : 0;
  const modelsScore = (testResults.database.models / 5) * 20;
  const apisScore = (testResults.apis.working / testResults.apis.tested) * 50;
  const totalScore = dbScore + modelsScore + apisScore;

  console.log(`\n🏆 التقييم العام: ${Math.round(totalScore)}%`);

  if (totalScore >= 90) {
    console.log('🎉 ممتاز! جميع APIs تعمل بشكل صحيح');
  } else if (totalScore >= 70) {
    console.log('👍 جيد! معظم APIs تعمل');
  } else if (totalScore >= 50) {
    console.log('⚠️ مقبول! بعض APIs تحتاج إصلاح');
  } else {
    console.log('🔧 يحتاج عمل كبير');
  }

  return totalScore >= 70;
}

// دالة مساعدة لاختبار API endpoint
async function testAPIEndpoint(apiName, method) {
  try {
    // هذه دالة محاكاة - في الواقع ستحتاج لاستخدام fetch أو axios
    // لكن هنا نختبر قاعدة البيانات مباشرة
    
    if (apiName === 'warehouses-simple' && method === 'GET') {
      await prisma.warehouse.findMany();
      return { success: true };
    } else if (apiName === 'clients-simple' && method === 'GET') {
      await prisma.client.findMany();
      return { success: true };
    } else if (apiName === 'suppliers-simple' && method === 'GET') {
      await prisma.supplier.findMany();
      return { success: true };
    }
    
    return { success: false, error: 'API غير مدعوم' };
  } catch (error) {
    return { success: false, error: error.message };
  }
}

if (require.main === module) {
  testSimpleAPIs()
    .then((success) => {
      process.exit(success ? 0 : 1);
    })
    .catch((error) => {
      console.error('💥 فشل الاختبار:', error);
      process.exit(1);
    })
    .finally(() => {
      prisma.$disconnect();
    });
}

module.exports = { testSimpleAPIs };
