// اختبار ظهور قسم إدارة المخازن في قائمة الصلاحيات

console.log('🧪 اختبار ظهور قسم إدارة المخازن');
console.log('=======================================');

// محاكاة قائمة الأقسام المحدثة
const allSections = [
  { id: 'dashboard', label: 'الرئيسية', icon: '📊' },
  { id: 'clients', label: 'العملاء', icon: '👥' },
  { id: 'warehouses', label: 'إدارة المخازن', icon: '🏬' },
  { id: 'inventory', label: 'المخزون', icon: '📦' },
  { id: 'supply', label: 'التوريد', icon: '🚚' },
  { id: 'sales', label: 'المبيعات', icon: '💰' },
];

// صلاحيات مستخدم مدير لديه صلاحية على جميع الأقسام
const adminPermissions = {
  dashboard: { view: true, create: true, edit: true, delete: true },
  clients: { view: true, create: true, edit: true, delete: true },
  warehouses: { view: true, create: true, edit: true, delete: true },
  inventory: { view: true, create: true, edit: true, delete: true },
  supply: { view: true, create: true, edit: true, delete: true },
  sales: { view: true, create: true, edit: true, delete: true }
};

// دالة للتحقق من الأقسام المتاحة
function getAvailableSections(allSections, userPermissions) {
  return allSections.filter(section => {
    const userPermission = userPermissions[section.id];
    return userPermission?.view || userPermission?.create || userPermission?.edit || userPermission?.delete;
  });
}

// تشغيل الاختبار
console.log('1. جميع الأقسام المعرفة:');
allSections.forEach(section => {
  console.log(`   ${section.icon} ${section.label} (${section.id})`);
});

console.log('\n2. صلاحيات المدير:');
Object.entries(adminPermissions).forEach(([key, perm]) => {
  const hasAccess = perm.view || perm.create || perm.edit || perm.delete;
  console.log(`   ${key}: ${hasAccess ? '✅ لديه صلاحية' : '❌ لا يوجد صلاحية'}`);
});

console.log('\n3. الأقسام المتاحة للمدير:');
const availableSections = getAvailableSections(allSections, adminPermissions);
availableSections.forEach(section => {
  console.log(`   ✅ ${section.icon} ${section.label}`);
});

// التحقق من وجود قسم إدارة المخازن
const warehousesSection = availableSections.find(section => section.id === 'warehouses');

console.log('\n4. النتيجة:');
if (warehousesSection) {
  console.log(`   🎉 نجح! قسم "إدارة المخازن" يظهر في القائمة`);
  console.log(`   📋 التفاصيل: ${warehousesSection.icon} ${warehousesSection.label} (${warehousesSection.id})`);
} else {
  console.log('   ❌ فشل! قسم "إدارة المخازن" لا يظهر في القائمة');
}

console.log('\n✅ الاختبار مكتمل!');
