const fs = require('fs');
const path = require('path');
const glob = require('glob');

function fixAllParenthesesIssues() {
  console.log('🔧 إصلاح جميع مشاكل الأقواس في المشروع...\n');

  // البحث عن جميع ملفات TypeScript/JavaScript
  const files = glob.sync('{app,lib,components}/**/*.{ts,tsx,js,jsx}', { 
    ignore: ['node_modules/**', 'dist/**', '.next/**'] 
  });

  console.log(`📁 فحص ${files.length} ملف...\n`);

  let totalFixed = 0;
  let filesFixed = 0;

  files.forEach(filePath => {
    try {
      let content = fs.readFileSync(filePath, 'utf8');
      let hasChanges = false;
      const fileName = path.relative('.', filePath);
      const originalContent = content;

      // 1. إصلاح أقواس مفقودة في if statements
      content = content.replace(/if\s*\(([^)]+)\s+{/g, 'if ($1) {');
      
      // 2. إصلاح أقواس مفقودة في isNaN
      content = content.replace(/isNaN\(([^)]+)\s+{/g, 'isNaN($1)) {');
      
      // 3. إصلاح أقواس مفقودة في getTime()
      content = content.replace(/\.getTime\(\)\s+{/g, '.getTime()) {');
      
      // 4. إصلاح أقواس زائدة في نهاية الملفات
      content = content.replace(/}\s*}\)\s*$/, '}');
      content = content.replace(/}\s*\)\s*$/, '}');
      
      // 5. إصلاح أقواس مفقودة في Array.isArray
      content = content.replace(/Array\.isArray\(([^)]+)\)\s*&&\s*([^)]+)\.some\(\s*([^)]+)\s*=>\s*([^)]+)\s*\);/g, 
        'Array.isArray($1) && $2.some($3 => $4));');

      // 6. إصلاح أقواس في التعبيرات الشرطية
      content = content.replace(/\(\s*Array\.isArray\(([^)]+)\)\s*\?\s*([^:]+):\s*0\)\s*===\s*0/g, 
        '(Array.isArray($1) ? $2 : 0) === 0');

      // 7. إصلاح أقواس مضاعفة
      content = content.replace(/\)\)\)/g, '))');
      content = content.replace(/\(\(\(/g, '((');

      // 8. فحص وإصلاح توازن الأقواس
      const lines = content.split('\n');
      let parenStack = [];
      let braceStack = [];
      let bracketStack = [];
      
      for (let i = 0; i < lines.length; i++) {
        const line = lines[i];
        
        // تجاهل التعليقات والنصوص
        const cleanLine = line.replace(/\/\/.*$/, '').replace(/\/\*[\s\S]*?\*\//g, '').replace(/'[^']*'/g, '').replace(/"[^"]*"/g, '');
        
        for (let char of cleanLine) {
          switch (char) {
            case '(':
              parenStack.push(i + 1);
              break;
            case ')':
              if (parenStack.length === 0) {
                console.log(`⚠️ ${fileName}:${i + 1} - قوس إغلاق زائد`);
              } else {
                parenStack.pop();
              }
              break;
            case '{':
              braceStack.push(i + 1);
              break;
            case '}':
              if (braceStack.length === 0) {
                console.log(`⚠️ ${fileName}:${i + 1} - قوس معقوف إغلاق زائد`);
              } else {
                braceStack.pop();
              }
              break;
            case '[':
              bracketStack.push(i + 1);
              break;
            case ']':
              if (bracketStack.length === 0) {
                console.log(`⚠️ ${fileName}:${i + 1} - قوس مربع إغلاق زائد`);
              } else {
                bracketStack.pop();
              }
              break;
          }
        }
      }

      // تقرير الأقواس المفتوحة
      if (parenStack.length > 0) {
        console.log(`⚠️ ${fileName}: ${parenStack.length} قوس مفتوح في الأسطر: ${parenStack.join(', ')}`);
      }
      if (braceStack.length > 0) {
        console.log(`⚠️ ${fileName}: ${braceStack.length} قوس معقوف مفتوح في الأسطر: ${braceStack.join(', ')}`);
      }
      if (bracketStack.length > 0) {
        console.log(`⚠️ ${fileName}: ${bracketStack.length} قوس مربع مفتوح في الأسطر: ${bracketStack.join(', ')}`);
      }

      if (content !== originalContent) {
        hasChanges = true;
      }

      if (hasChanges) {
        fs.writeFileSync(filePath, content);
        console.log(`✅ ${fileName}: تم إصلاح مشاكل الأقواس`);
        filesFixed++;
        totalFixed++;
      }

    } catch (error) {
      console.log(`❌ خطأ في معالجة ${filePath}: ${error.message}`);
    }
  });

  console.log(`\n📊 ملخص إصلاح الأقواس:`);
  console.log(`   - إجمالي الملفات المفحوصة: ${files.length}`);
  console.log(`   - ملفات تم إصلاحها: ${filesFixed}`);
  console.log(`   - إجمالي المشاكل المُصلحة: ${totalFixed}`);

  if (totalFixed > 0) {
    console.log(`\n🎉 تم إصلاح ${totalFixed} مشكلة أقواس في ${filesFixed} ملف!`);
    return true;
  } else {
    console.log(`\nℹ️ لا توجد مشاكل أقواس تحتاج إصلاح`);
    return true;
  }
}

// دالة لفحص مشاكل محددة
function checkCommonSyntaxIssues() {
  console.log('\n🔍 فحص المشاكل الشائعة...\n');

  const commonIssues = [
    {
      name: 'أقواس مفقودة في if statements',
      pattern: /if\s*\([^)]+\s+{/g,
      fix: 'إضافة قوس إغلاق قبل {'
    },
    {
      name: 'أقواس مفقودة في function calls',
      pattern: /\w+\([^)]*\s+{/g,
      fix: 'إضافة قوس إغلاق قبل {'
    },
    {
      name: 'أقواس زائدة في نهاية الملف',
      pattern: /}\s*}\)\s*$/g,
      fix: 'حذف الأقواس الزائدة'
    }
  ];

  const files = glob.sync('app/**/*.{ts,tsx}');
  
  files.forEach(filePath => {
    const content = fs.readFileSync(filePath, 'utf8');
    const fileName = path.basename(path.dirname(filePath));
    
    commonIssues.forEach(issue => {
      const matches = content.match(issue.pattern);
      if (matches) {
        console.log(`⚠️ ${fileName}: ${issue.name} (${matches.length} مرة) - ${issue.fix}`);
      }
    });
  });
}

if (require.main === module) {
  const success = fixAllParenthesesIssues();
  checkCommonSyntaxIssues();
  process.exit(success ? 0 : 1);
}

module.exports = { fixAllParenthesesIssues, checkCommonSyntaxIssues };
