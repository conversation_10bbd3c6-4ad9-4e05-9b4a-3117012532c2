/**
 * اختبار إنشاء أمر توريد كبير (أكثر من 300 جهاز)
 * هذا الاختبار يحاكي المشكلة التي واجهها المستخدم
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testLargeSupplyOrder() {
  try {
    console.log('🧪 بدء اختبار أمر التوريد الكبير...\n');

    // التحقق من وجود مورد ومخزن للاختبار
    let supplier = await prisma.supplier.findFirst();
    if (!supplier) {
      supplier = await prisma.supplier.create({
        data: {
          name: 'مورد الاختبار',
          phone: '123456789',
          address: 'عنوان الاختبار'
        }
      });
      console.log('✅ تم إنشاء مورد للاختبار');
    }

    let warehouse = await prisma.warehouse.findFirst();
    if (!warehouse) {
      warehouse = await prisma.warehouse.create({
        data: {
          name: 'مخزن الاختبار',
          location: 'موقع الاختبار'
        }
      });
      console.log('✅ تم إنشاء مخزن للاختبار');
    }

    // إنشاء قائمة بـ 350 جهاز للاختبار
    const deviceCount = 350;
    const testDevices = [];
    
    console.log(`📱 إنشاء ${deviceCount} جهاز للاختبار...`);
    
    for (let i = 1; i <= deviceCount; i++) {
      const imei = `TEST${Date.now()}${i.toString().padStart(6, '0')}`;
      testDevices.push({
        imei: imei,
        manufacturer: 'Samsung',
        model: 'Galaxy A54',
        condition: 'جديد',
        notes: `جهاز اختبار رقم ${i}`
      });
    }

    console.log(`✅ تم إنشاء قائمة بـ ${testDevices.length} جهاز`);

    // إنشاء بيانات أمر التوريد
    const supplyOrderData = {
      supplierId: supplier.id,
      warehouseId: warehouse.id,
      employeeName: 'مدير الاختبار',
      invoiceNumber: `TEST-INV-${Date.now()}`,
      supplyDate: new Date().toISOString(),
      notes: `اختبار أمر توريد كبير - ${deviceCount} جهاز`,
      items: testDevices,
      status: 'completed'
    };

    console.log('\n🔄 إرسال أمر التوريد إلى API...');
    const startTime = Date.now();

    // محاكاة استدعاء API
    const response = await fetch('http://localhost:9005/api/supply-batch', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        // يجب إضافة رأس التفويض في التطبيق الحقيقي
      },
      body: JSON.stringify(supplyOrderData)
    });

    const duration = Date.now() - startTime;
    console.log(`⏱️ مدة العملية: ${duration}ms (${(duration / 1000).toFixed(2)} ثانية)`);

    if (response.ok) {
      const result = await response.json();
      console.log('\n✅ نجح إنشاء أمر التوريد!');
      console.log('📊 النتائج:');
      console.log(`   - الأجهزة المُنشأة: ${result.processedDevices?.created || 'غير محدد'}`);
      console.log(`   - الأجهزة الموجودة: ${result.processedDevices?.existing || 'غير محدد'}`);
      console.log(`   - الأخطاء: ${result.processedDevices?.errors || 'غير محدد'}`);
      console.log(`   - المجموع: ${result.processedDevices?.total || 'غير محدد'}`);
      
      if (result.processedDevices?.duration) {
        console.log(`   - مدة المعالجة: ${result.processedDevices.duration}ms`);
      }
      
      if (result.processedDevices?.totalBatches) {
        console.log(`   - عدد الدفعات: ${result.processedDevices.totalBatches}`);
        console.log(`   - حجم الدفعة: ${result.processedDevices.batchSize}`);
      }

      // التحقق من إنشاء الأجهزة في قاعدة البيانات
      const createdDevicesCount = await prisma.device.count({
        where: {
          id: {
            startsWith: 'TEST'
          }
        }
      });
      
      console.log(`\n🔍 التحقق من قاعدة البيانات:`);
      console.log(`   - عدد الأجهزة المُنشأة في قاعدة البيانات: ${createdDevicesCount}`);

    } else {
      const errorText = await response.text();
      console.error('\n❌ فشل في إنشاء أمر التوريد:');
      console.error(`   - كود الخطأ: ${response.status}`);
      console.error(`   - رسالة الخطأ: ${errorText}`);
      
      // محاولة تحليل الخطأ
      try {
        const errorJson = JSON.parse(errorText);
        if (errorJson.details) {
          console.error(`   - تفاصيل الخطأ: ${errorJson.details}`);
        }
      } catch (parseError) {
        // تجاهل خطأ التحليل
      }
    }

  } catch (error) {
    console.error('\n💥 خطأ في الاختبار:', error);
    
    if (error.code === 'ECONNREFUSED') {
      console.error('🔌 تأكد من تشغيل الخادم على المنفذ 9005');
    }
    
  } finally {
    // تنظيف بيانات الاختبار
    console.log('\n🧹 تنظيف بيانات الاختبار...');
    
    try {
      // حذف الأجهزة التجريبية
      const deletedDevices = await prisma.device.deleteMany({
        where: {
          id: {
            startsWith: 'TEST'
          }
        }
      });
      console.log(`✅ تم حذف ${deletedDevices.count} جهاز تجريبي`);
      
      // حذف أوامر التوريد التجريبية
      const deletedOrders = await prisma.supplyOrder.deleteMany({
        where: {
          notes: {
            contains: 'اختبار أمر توريد كبير'
          }
        }
      });
      console.log(`✅ تم حذف ${deletedOrders.count} أمر توريد تجريبي`);
      
    } catch (cleanupError) {
      console.error('⚠️ خطأ في تنظيف البيانات:', cleanupError);
    }
    
    await prisma.$disconnect();
    console.log('\n🏁 انتهى الاختبار');
  }
}

// تشغيل الاختبار
if (require.main === module) {
  testLargeSupplyOrder();
}

module.exports = { testLargeSupplyOrder };
