// اختبار سريع لإنشاء أمر توريد
const userToken = Buffer.from('user:admin:admin').toString('base64');

async function testSupplyOrderCreation() {
  console.log('🧪 اختبار إنشاء أمر توريد...\n');

  try {
    const testOrder = {
      supplierId: 1,
      warehouseId: 1,
      employeeName: 'admin',
      invoiceNumber: `TEST-${Date.now()}`,
      supplyDate: new Date().toISOString(),
      notes: 'اختبار إنشاء أمر توريد',
      items: [
        {
          manufacturer: 'Apple',
          model: 'iPhone 13',
          imei: `IMEI${Date.now()}`,
          condition: 'جديد',
          quantity: 1
        },
        {
          manufacturer: 'Samsung',
          model: 'Galaxy S21',
          imei: `IMEI${Date.now() + 1}`,
          condition: 'جديد',
          quantity: 1
        }
      ],
      status: 'completed'
    };

    console.log('📝 بيانات الأمر:', JSON.stringify(testOrder, null, 2));

    const response = await fetch('http://localhost:9005/api/supply', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${userToken}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(testOrder)
    });

    console.log(`الحالة: ${response.status} ${response.statusText}`);
    
    if (response.ok) {
      const result = await response.json();
      console.log('✅ تم إنشاء الأمر بنجاح:');
      console.log(`   ID: ${result.id}`);
      console.log(`   Order ID: ${result.supplyOrderId}`);
      console.log(`   Items: ${typeof result.items === 'string' ? 'JSON String' : 'Array'}`);
      
      if (typeof result.items === 'string') {
        console.log('   Items Content:', JSON.parse(result.items));
      } else {
        console.log('   Items Content:', result.items);
      }
    } else {
      const error = await response.text();
      console.log('❌ فشل:', error);
    }

  } catch (error) {
    console.error('❌ خطأ في الاختبار:', error.message);
  }
}

testSupplyOrderCreation();
