/**  حفظ أمر صيانة (مسوّدة أو فعلي) مع الحفاظ على تسلسل رقم الأمر وعدم تكراره */
const addMaintenanceOrder = async (
  order: Omit<MaintenanceOrder, "id" | "createdAt"> & {
    id?: number;
    status?: "wip" | "completed" | "draft";
  },
) => {
  try {
    // إذا لم يكن هناك رقم أمر، نقوم بإنشاء واحد
    if (!order.orderNumber) {
      const allExisting = [...maintenanceOrders];
      const useId =
        order.id && order.id > 0
          ? order.id
          : Math.max(0, ...allExisting.map((o) => o.id)) + 1;
      order.orderNumber = `MAINT-${useId}`;
    }
    
    // إرسال الأمر إلى API
    const response = await fetch('/api/maintenance-orders', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        ...order,
        status: order.status || 'wip',
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to create maintenance order');
    }

    // استقبال الأمر الذي تم إنشاؤه من API
    const newOrder = await response.json();

    // تحديث حالة التطبيق
    setMaintenanceOrders((prev) => [newOrder, ...prev]);

    // إضافة نشاط
    addActivity({
      type: "maintenance",
      description: `تم إنشاء أمر صيانة ${newOrder.orderNumber}.`,
    });
    
    return newOrder;
  } catch (error) {
    console.error('Failed to add maintenance order:', error);
    addActivity({
      type: "maintenance",
      description: `⚠️ فشل في إنشاء أمر صيانة: ${error instanceof Error ? error.message : String(error)}`,
    });
    throw error;
  }
};
