// Test script for supply order deletion logic
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testDeletionLogic() {
  console.log('🧪 اختبار منطق حذف أوامر التوريد...');
  
  try {
    // 1. Create a test supply order
    console.log('\n1️⃣ إنشاء أمر توريد للاختبار...');
    const testSupplyOrder = await prisma.supplyOrder.create({
      data: {
        supplyOrderId: `DELETE-LOGIC-${Date.now()}`,
        supplierId: 1,
        invoiceNumber: null,
        supplyDate: new Date().toISOString(),
        warehouseId: 1,
        employeeName: 'Logic Test',
        items: JSON.stringify([
          {
            imei: `LOGIC-DEVICE-1-${Date.now()}`,
            manufacturer: 'Test Brand',
            model: 'Test Model 1',
            condition: 'جديد'
          },
          {
            imei: `LOGIC-DEVICE-2-${Date.now()}`,
            manufacturer: 'Test Brand',
            model: 'Test Model 2',
            condition: 'جديد'
          }
        ]),
        notes: 'أمر اختبار منطق الحذف',
        status: 'completed'
      }
    });
    
    console.log(`✅ تم إنشاء أمر التوريد: ${testSupplyOrder.supplyOrderId}`);
    
    // 2. Test deletion when no related operations exist
    console.log('\n2️⃣ اختبار الحذف عند عدم وجود عمليات مرتبطة...');
    let deleteResponse = await fetch('http://localhost:9005/api/supply', {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + btoa('user:admin:admin')
      },
      body: JSON.stringify({ id: testSupplyOrder.id })
    });
    
    if (deleteResponse.ok) {
      console.log('✅ تم الحذف بنجاح - منطق صحيح');
    } else {
      const error = await deleteResponse.text();
      console.log('❌ فشل الحذف:', error);
    }
    
    // 3. Create another test order and simulate related operations
    console.log('\n3️⃣ إنشاء أمر جديد واختبار مع عمليات مرتبطة...');
    const testDevice1 = `PROTECTED-DEVICE-1-${Date.now()}`;
    const testDevice2 = `PROTECTED-DEVICE-2-${Date.now()}`;
    
    const protectedSupplyOrder = await prisma.supplyOrder.create({
      data: {
        supplyOrderId: `PROTECTED-ORDER-${Date.now()}`,
        supplierId: 1,
        invoiceNumber: null,
        supplyDate: new Date().toISOString(),
        warehouseId: 1,
        employeeName: 'Protected Test',
        items: JSON.stringify([
          {
            imei: testDevice1,
            manufacturer: 'Protected Brand',
            model: 'Protected Model 1',
            condition: 'جديد'
          },
          {
            imei: testDevice2,
            manufacturer: 'Protected Brand',
            model: 'Protected Model 2',
            condition: 'جديد'
          }
        ]),
        notes: 'أمر محمي بعمليات مرتبطة',
        status: 'completed'
      }
    });
    
    console.log(`✅ تم إنشاء أمر محمي: ${protectedSupplyOrder.supplyOrderId}`);
    
    // 4. Create a sale using one of the devices
    console.log('\n4️⃣ إنشاء فاتورة مبيعات تستخدم أحد الأجهزة...');
    const testSale = await prisma.sale.create({
      data: {
        soNumber: `SO-${Date.now()}`,
        opNumber: `OP-${Date.now()}`,
        date: new Date().toISOString(),
        clientName: 'Test Client',
        warehouseName: 'Main Warehouse',
        items: JSON.stringify([
          {
            deviceId: testDevice1,
            price: 1000,
            warranty: '12 شهر'
          }
        ]),
        notes: 'فاتورة اختبار',
        warrantyPeriod: '12 شهر'
      }
    });
    
    console.log(`✅ تم إنشاء فاتورة مبيعات: ${testSale.soNumber}`);
    
    // 5. Now try to delete the protected supply order
    console.log('\n5️⃣ محاولة حذف الأمر المحمي...');
    deleteResponse = await fetch('http://localhost:9005/api/supply', {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + btoa('user:admin:admin')
      },
      body: JSON.stringify({ id: protectedSupplyOrder.id })
    });
    
    if (deleteResponse.ok) {
      console.log('❌ تم الحذف - هذا خطأ! يجب منع الحذف');
    } else {
      const error = await deleteResponse.text();
      console.log('✅ تم منع الحذف بنجاح:', error);
    }
    
    // 6. Clean up test data
    console.log('\n6️⃣ تنظيف بيانات الاختبار...');
    await prisma.sale.delete({ where: { id: testSale.id } });
    await prisma.supplyOrder.delete({ where: { id: protectedSupplyOrder.id } });
    console.log('✅ تم تنظيف بيانات الاختبار');
    
  } catch (error) {
    console.error('❌ خطأ في الاختبار:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

testDeletionLogic();
