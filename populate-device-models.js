// Script to populate device models table from existing data
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

// Initial device models data
const initialDeviceModels = [
  { id: 1, name: "iPhone 15 Pro Max", manufacturerId: 1, category: "هاتف ذكي" },
  { id: 2, name: "iPhone 15 Pro", manufacturerId: 1, category: "هاتف ذكي" },
  { id: 3, name: "iPhone 15", manufacturerId: 1, category: "هاتف ذكي" },
  { id: 4, name: "iPhone 14 Pro Max", manufacturerId: 1, category: "هاتف ذكي" },
  { id: 5, name: "iPhone 14 Pro", manufacturerId: 1, category: "هاتف ذكي" },
  { id: 6, name: "iPhone 14", manufacturerId: 1, category: "هاتف ذكي" },
  { id: 7, name: "iPhone 13 Pro Max", manufacturerId: 1, category: "هاتف ذكي" },
  { id: 8, name: "iPhone 13 Pro", manufacturerId: 1, category: "هاتف ذكي" },
  { id: 9, name: "iPhone 13", manufacturerId: 1, category: "هاتف ذكي" },
  { id: 10, name: "iPhone 12 Pro Max", manufacturerId: 1, category: "هاتف ذكي" },
  { id: 11, name: "iPhone 12 Pro", manufacturerId: 1, category: "هاتف ذكي" },
  { id: 12, name: "iPhone 12", manufacturerId: 1, category: "هاتف ذكي" },
  { id: 13, name: "Galaxy S24 Ultra", manufacturerId: 2, category: "هاتف ذكي" },
  { id: 14, name: "Galaxy S24+", manufacturerId: 2, category: "هاتف ذكي" },
  { id: 15, name: "Galaxy S24", manufacturerId: 2, category: "هاتف ذكي" },
  { id: 16, name: "Galaxy S23 Ultra", manufacturerId: 2, category: "هاتف ذكي" },
  { id: 17, name: "Galaxy S23+", manufacturerId: 2, category: "هاتف ذكي" },
  { id: 18, name: "Galaxy S23", manufacturerId: 2, category: "هاتف ذكي" },
  { id: 19, name: "Galaxy Note 20 Ultra", manufacturerId: 2, category: "هاتف ذكي" },
  { id: 20, name: "Galaxy A54", manufacturerId: 2, category: "هاتف ذكي" },
  { id: 21, name: "Galaxy A34", manufacturerId: 2, category: "هاتف ذكي" },
  { id: 22, name: "Pixel 8 Pro", manufacturerId: 3, category: "هاتف ذكي" },
  { id: 23, name: "Pixel 8", manufacturerId: 3, category: "هاتف ذكي" },
  { id: 24, name: "Pixel 7 Pro", manufacturerId: 3, category: "هاتف ذكي" },
  { id: 25, name: "Pixel 7", manufacturerId: 3, category: "هاتف ذكي" },
  { id: 26, name: "Pixel 6a", manufacturerId: 3, category: "هاتف ذكي" },
  { id: 27, name: "OnePlus 12", manufacturerId: 4, category: "هاتف ذكي" },
  { id: 28, name: "OnePlus 11", manufacturerId: 4, category: "هاتف ذكي" },
  { id: 29, name: "OnePlus Nord 3", manufacturerId: 4, category: "هاتف ذكي" },
  { id: 30, name: "Mi 14 Ultra", manufacturerId: 5, category: "هاتف ذكي" },
  { id: 31, name: "Mi 14 Pro", manufacturerId: 5, category: "هاتف ذكي" },
  { id: 32, name: "Mi 14", manufacturerId: 5, category: "هاتف ذكي" },
  { id: 33, name: "Redmi Note 13 Pro", manufacturerId: 5, category: "هاتف ذكي" },
  { id: 34, name: "P60 Pro", manufacturerId: 6, category: "هاتف ذكي" },
  { id: 35, name: "Mate 60 Pro", manufacturerId: 6, category: "هاتف ذكي" },
  { id: 36, name: "Nova 12", manufacturerId: 6, category: "هاتف ذكي" },
  { id: 37, name: "Find X7 Ultra", manufacturerId: 7, category: "هاتف ذكي" },
  { id: 38, name: "Reno 11 Pro", manufacturerId: 7, category: "هاتف ذكي" },
  { id: 39, name: "A3 Pro", manufacturerId: 7, category: "هاتف ذكي" },
  { id: 40, name: "V30", manufacturerId: 8, category: "هاتف ذكي" },
  { id: 41, name: "Y78", manufacturerId: 8, category: "هاتف ذكي" },
  { id: 42, name: "iPad Pro 12.9", manufacturerId: 1, category: "تابلت" },
  { id: 43, name: "iPad Pro 11", manufacturerId: 1, category: "تابلت" },
  { id: 44, name: "iPad Air", manufacturerId: 1, category: "تابلت" },
  { id: 45, name: "iPad", manufacturerId: 1, category: "تابلت" },
  { id: 46, name: "Galaxy Tab S9 Ultra", manufacturerId: 2, category: "تابلت" },
  { id: 47, name: "Galaxy Tab S9+", manufacturerId: 2, category: "تابلت" },
  { id: 48, name: "Galaxy Tab S9", manufacturerId: 2, category: "تابلت" },
  { id: 49, name: "MatePad Pro", manufacturerId: 6, category: "تابلت" },
  { id: 50, name: "Pad 6", manufacturerId: 5, category: "تابلت" }
];

async function populateDeviceModels() {
  console.log('📱 تعبئة جدول موديلات الأجهزة...');
  
  try {
    // Check if table is empty
    const existingModels = await prisma.deviceModel.findMany();
    
    if (existingModels.length > 0) {
      console.log(`ℹ️ يوجد بالفعل ${existingModels.length} موديل في قاعدة البيانات`);
      return;
    }
    
    console.log(`🔄 إضافة ${initialDeviceModels.length} موديل...`);
    
    for (const model of initialDeviceModels) {
      await prisma.deviceModel.create({
        data: {
          name: model.name,
          manufacturerId: BigInt(model.manufacturerId),
          category: model.category
        }
      });
    }
    
    console.log('✅ تم تعبئة جدول الموديلات بنجاح!');
    
    // Display summary
    const finalCount = await prisma.deviceModel.count();
    console.log(`📊 إجمالي الموديلات: ${finalCount}`);
    
    // Group by manufacturer
    const modelsByManufacturer = await prisma.deviceModel.groupBy({
      by: ['manufacturerId'],
      _count: {
        id: true
      }
    });
    
    console.log('\n📋 توزيع الموديلات حسب المصنع:');
    for (const group of modelsByManufacturer) {
      console.log(`المصنع ${group.manufacturerId}: ${group._count.id} موديل`);
    }
    
  } catch (error) {
    console.error('❌ خطأ في تعبئة جدول الموديلات:', error);
  } finally {
    await prisma.$disconnect();
  }
}

populateDeviceModels();
