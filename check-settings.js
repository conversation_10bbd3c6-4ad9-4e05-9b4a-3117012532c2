const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkSettings() {
  try {
    console.log('🔍 فحص إعدادات النظام...');
    
    const settings = await prisma.systemSetting.findFirst();
    
    if (settings) {
      console.log('✅ الإعدادات موجودة:');
      console.log('   ID:', settings.id);
      console.log('   اسم الشركة (عربي):', settings.companyNameAr);
      console.log('   اسم الشركة (إنجليزي):', settings.companyNameEn);
    } else {
      console.log('❌ لا توجد إعدادات في قاعدة البيانات');
      
      // إنشاء إعدادات افتراضية
      console.log('🔧 إنشاء إعدادات افتراضية...');
      const newSettings = await prisma.systemSetting.create({
        data: {
          logoUrl: '',
          companyNameAr: 'نظام إدارة الأجهزة',
          companyNameEn: 'Device Management System',
          addressAr: 'العنوان باللغة العربية',
          addressEn: 'Address in English',
          phone: '+967-1-234567',
          email: '<EMAIL>',
          website: 'www.devicemanagement.com',
          footerTextAr: 'جميع الحقوق محفوظة',
          footerTextEn: 'All Rights Reserved'
        }
      });
      
      console.log('✅ تم إنشاء الإعدادات:', newSettings.companyNameAr);
    }
    
  } catch (error) {
    console.error('❌ خطأ:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

checkSettings();
