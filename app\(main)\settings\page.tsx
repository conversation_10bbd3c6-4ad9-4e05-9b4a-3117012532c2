'use client';

import { useState, useRef, useEffect } from 'react';
import { useStore } from '@/context/store';
import { useToast } from '@/hooks/use-toast';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
  CardFooter,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Save,
  Database,
  UploadCloud,
  DownloadCloud,
  Trash2,
  PlusCircle,
  RotateCw,
  Palette,
  Network,
} from 'lucide-react';
import { Switch } from '@/components/ui/switch';
import AppearanceSettings from './appearance-settings';
import { ConnectionManager } from '@/components/connection-manager';
import { DatabaseManagement } from '@/components/database-management';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { cn } from '@/lib/utils';

// --- Helper Functions for Color Conversion ---
const parseHsl = (hslStr: string): { h: number; s: number; l: number } => {
  if (!hslStr || typeof hslStr !== 'string') return { h: 0, s: 0, l: 0 };
  const [h, s, l] = hslStr.split(' ').map(parseFloat);
  return { h: h || 0, s: s || 0, l: l || 0 };
};

const formatHsl = (hslObj: { h: number; s: number; l: number }): string => {
  return `${hslObj.h.toFixed(1)} ${hslObj.s.toFixed(1)}% ${hslObj.l.toFixed(1)}%`;
};

const hslToHex = (h: number, s: number, l: number): string => {
  s /= 100;
  l /= 100;
  const a = s * Math.min(l, 1 - l);
  const f = (n: number) => {
    const k = (n + h / 30) % 12;
    const color = l - a * Math.max(Math.min(k - 3, 9 - k, 1), -1);
    return Math.round(255 * color)
      .toString(16)
      .padStart(2, '0');
  };
  return `#${f(0)}${f(8)}${f(4)}`;
};

const hexToHsl = (hex: string): { h: number; s: number; l: number } => {
  let r = 0,
    g = 0,
    b = 0;
  if (hex.length === 4) {
    r = parseInt(hex[1] + hex[1], 16);
    g = parseInt(hex[2] + hex[2], 16);
    b = parseInt(hex[3] + hex[3], 16);
  } else if (hex.length === 7) {
    r = parseInt(hex.substring(1, 3), 16);
    g = parseInt(hex.substring(3, 5), 16);
    b = parseInt(hex.substring(5, 7), 16);
  }
  r /= 255;
  g /= 255;
  b /= 255;
  const cmin = Math.min(r, g, b),
    cmax = Math.max(r, g, b),
    delta = cmax - cmin;
  let h = 0,
    s = 0,
    l = 0;

  if (delta === 0) h = 0;
  else if (cmax === r) h = ((g - b) / delta) % 6;
  else if (cmax === g) h = (b - r) / delta + 2;
  else h = (r - g) / delta + 4;
  h = Math.round(h * 60);
  if (h < 0) h += 360;
  l = (cmax + cmin) / 2;
  s = delta === 0 ? 0 : delta / (1 - Math.abs(2 * l - 1));
  s = +(s * 100);
  l = +(l * 100);
  return { h, s, l };
};
// --- End Helper Functions ---

function ColorPicker({
  label,
  value, // HSL string: "h s% l%"
  onChange,
}: {
  label: string;
  value: string;
  onChange: (value: string) => void;
}) {
  const [hsl, setHsl] = useState(parseHsl(value));

  useEffect(() => {
    setHsl(parseHsl(value));
  }, [value]);

  const handleColorInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newHex = e.target.value;
    const newHsl = hexToHsl(newHex);
    setHsl(newHsl);
    onChange(
      `${newHsl.h.toFixed(1)} ${newHsl.s.toFixed(1)}% ${newHsl.l.toFixed(1)}%`
    );
  };

  const hexValue = hslToHex(hsl.h, hsl.s, hsl.l);

  return (
    <div className="space-y-2 p-4 border rounded-lg">
      <div className="flex justify-between items-center">
        <Label>{label}</Label>
        <div className="relative">
          <Input
            type="color"
            value={hexValue}
            onChange={handleColorInputChange}
            className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
          />
          <div
            className="w-8 h-8 rounded-full border"
            style={{ backgroundColor: hexValue }}
          />
        </div>
      </div>
      <div className="text-xs text-muted-foreground text-center font-mono">
        {hexValue}
      </div>
    </div>
  );
}

export default function SettingsPage() {
  const {
    currentUser,
  } = useStore();
  const { toast } = useToast();

  const permissions = currentUser?.permissions.settings;

  const [themeColors, setThemeColors] = useState({
    background: '',
    foreground: '',
    primary: '',
    accent: '',
  });

  const handleColorChange = (
    colorName: keyof typeof themeColors,
    value: string,
  ) => {
    setThemeColors((prev) => ({ ...prev, [colorName]: value }));
  };

  const applyTheme = () => {
    const root = window.document.documentElement;
    root.style.setProperty('--background', themeColors.background);
    root.style.setProperty('--foreground', themeColors.foreground);
    root.style.setProperty('--primary', themeColors.primary);
    root.style.setProperty('--accent', themeColors.accent);

    toast({
      title: 'تم تطبيق المظهر',
      description: 'تم تحديث ألوان النظام بنجاح.',
    });
  };

  useEffect(() => {
    const root = window.document.documentElement;
    const style = getComputedStyle(root);
    setThemeColors({
      background: style.getPropertyValue('--background').trim(),
      foreground: style.getPropertyValue('--foreground').trim(),
      primary: style.getPropertyValue('--primary').trim(),
      accent: style.getPropertyValue('--accent').trim(),
    });
  }, []);

  if (!permissions?.view) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>غير مصرح بالدخول</CardTitle>
        </CardHeader>
        <CardContent>
          <p>ليس لديك الصلاحيات اللازمة لعرض هذه الصفحة.</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Tabs defaultValue="appearance" className="w-full max-w-6xl mx-auto">
      <TabsList className="grid w-full grid-cols-4">
        <TabsTrigger value="appearance">المظهر</TabsTrigger>
        <TabsTrigger value="colors">الألوان والمظهر</TabsTrigger>
        <TabsTrigger value="connection">إدارة الاتصال</TabsTrigger>
        <TabsTrigger value="database">قواعد البيانات</TabsTrigger>
      </TabsList>
      <TabsContent value="appearance" className="mt-4">
        <AppearanceSettings />
      </TabsContent>
      <TabsContent value="colors" className="mt-4">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Palette />
              الألوان والمظهر
            </CardTitle>
            <CardDescription>
              تخصيص ألوان النظام والتحكم في الوضع الليلي والنهاري.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <ColorPicker
                label="اللون الأساسي"
                value={themeColors.primary}
                onChange={(value) => handleColorChange('primary', value)}
              />
              <ColorPicker
                label="لون الخلفية"
                value={themeColors.background}
                onChange={(value) => handleColorChange('background', value)}
              />
              <ColorPicker
                label="لون النص"
                value={themeColors.foreground}
                onChange={(value) => handleColorChange('foreground', value)}
              />
              <ColorPicker
                label="لون التمييز"
                value={themeColors.accent}
                onChange={(value) => handleColorChange('accent', value)}
              />
            </div>
          </CardContent>
          <CardFooter className="flex justify-end">
            <Button onClick={applyTheme}>
              <Save className="ml-2 h-4 w-4" />
              تطبيق الألوان
            </Button>
          </CardFooter>
        </Card>
      </TabsContent>
      <TabsContent value="connection" className="mt-4">
        <ConnectionManager />
      </TabsContent>
      <TabsContent value="database" className="mt-4">
        <DatabaseManagement />
      </TabsContent>
    </Tabs>
  );
}
