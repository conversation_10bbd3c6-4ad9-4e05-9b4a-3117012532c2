import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

export async function GET(request: NextRequest) {
  try {
    const sales = await prisma.sale.findMany({
      include: {
        items: true
      },
      orderBy: { id: 'desc' }
    });

    return NextResponse.json(sales);
  } catch (error) {
    console.error('Failed to fetch sales:', error);
    return NextResponse.json({ error: 'Failed to fetch sales' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const newSale = await request.json();

    // Basic validation
    if (!newSale.clientId || !newSale.items || newSale.items.length === 0) {
      return NextResponse.json(
        { error: 'Client ID and items are required' },
        { status: 400 }
      );
    }

    const sale = await prisma.sale.create({
      data: {
        orderId: newSale.orderId,
        clientId: newSale.clientId,
        warehouseId: newSale.warehouseId || null,
        totalAmount: newSale.totalAmount || 0,
        notes: newSale.notes || null,
        saleDate: new Date(newSale.saleDate || Date.now()),
        items: {
          create: newSale.items.map((item: any) => ({
            deviceId: item.deviceId,
            sellingPrice: item.sellingPrice || 0,
            notes: item.notes || null
          }))
        }
      },
      include: {
        items: true
      }
    });

    return NextResponse.json(sale, { status: 201 });
  } catch (error) {
    console.error('Failed to create sale:', error);
    return NextResponse.json({ error: 'Failed to create sale' }, { status: 500 });
  }
}

export async function PUT(request: NextRequest) {
  try {
    const updatedSale = await request.json();

    if (!updatedSale.id) {
      return NextResponse.json(
        { error: 'Sale ID is required' },
        { status: 400 }
      );
    }

    const sale = await prisma.sale.update({
      where: { id: updatedSale.id },
      data: {
        ...(updatedSale.clientId && { clientId: updatedSale.clientId }),
        ...(updatedSale.warehouseId !== undefined && { warehouseId: updatedSale.warehouseId }),
        ...(updatedSale.totalAmount !== undefined && { totalAmount: updatedSale.totalAmount }),
        ...(updatedSale.notes !== undefined && { notes: updatedSale.notes }),
        ...(updatedSale.saleDate && { saleDate: new Date(updatedSale.saleDate) })
      },
      include: {
        items: true
      }
    });

    return NextResponse.json(sale);
  } catch (error) {
    console.error('Failed to update sale:', error);
    return NextResponse.json({ error: 'Failed to update sale' }, { status: 500 });
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { id } = await request.json();

    if (!id) {
      return NextResponse.json(
        { error: 'Sale ID is required' },
        { status: 400 }
      );
    }

    // Delete sale items first, then the sale
    await prisma.saleItem.deleteMany({
      where: { saleId: id }
    });

    await prisma.sale.delete({
      where: { id }
    });

    return NextResponse.json({ message: 'Sale deleted successfully' });
  } catch (error) {
    console.error('Failed to delete sale:', error);
    return NextResponse.json({ error: 'Failed to delete sale' }, { status: 500 });
  }
}
