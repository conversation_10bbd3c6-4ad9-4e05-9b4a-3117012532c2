const fs = require('fs');
const path = require('path');

// تحديث API endpoints للنماذج المتبقية
const apiUpdates = [
  {
    file: 'app/api/maintenance-orders/route.ts',
    model: 'maintenanceOrder',
    itemModel: 'maintenanceOrderItem',
    itemFields: ['deviceId', 'model', 'fault', 'notes']
  },
  {
    file: 'app/api/evaluations/route.ts',
    model: 'evaluationOrder',
    itemModel: 'evaluationOrderItem',
    itemFields: ['deviceId', 'model', 'externalGrade', 'screenGrade', 'networkGrade', 'finalGrade', 'fault', 'damageType']
  },
  {
    file: 'app/api/delivery-orders/route.ts',
    model: 'deliveryOrder',
    itemModel: 'deliveryOrderItem',
    itemFields: ['deviceId', 'model', 'result', 'fault', 'damage', 'notes']
  },
  {
    file: 'app/api/maintenance-receipts/route.ts',
    model: 'maintenanceReceiptOrder',
    itemModel: 'maintenanceReceiptOrderItem',
    itemFields: ['deviceId', 'model', 'result', 'fault', 'damage', 'notes']
  }
];

function updateApiFile(apiConfig) {
  const filePath = apiConfig.file;
  
  if (!fs.existsSync(filePath)) {
    console.log(`⚠️ File not found: ${filePath}`);
    return;
  }

  let content = fs.readFileSync(filePath, 'utf8');
  
  // تحديث GET endpoint
  content = content.replace(
    new RegExp(`await prisma\\.${apiConfig.model}\\.findMany\\(\\{[^}]*\\}\\)`, 'g'),
    `await prisma.${apiConfig.model}.findMany({
      include: {
        items: true
      },
      orderBy: { id: 'desc' }
    })`
  );

  // إزالة معالجة JSON القديمة
  content = content.replace(
    /\/\/ تحويل حقول JSON من strings إلى objects[\s\S]*?return NextResponse\.json\(processed[^)]+\);/g,
    'return NextResponse.json(orders);'
  );

  // تحديث POST endpoint - إزالة items من create
  content = content.replace(
    /items:\s*typeof\s+[^,]+,/g,
    ''
  );

  // إضافة إنشاء العناصر الوسيطة
  const createItemsCode = `
      // Create ${apiConfig.model} items
      if (newOrder.items && Array.isArray(newOrder.items)) {
        for (const item of newOrder.items) {
          await tx.${apiConfig.itemModel}.create({
            data: {
              ${apiConfig.model}Id: order.id,
              ${apiConfig.itemFields.map(field => `${field}: item.${field} || ${field === 'deviceId' || field === 'model' ? "''" : field.includes('Grade') || field === 'result' ? "''" : 'null'}`).join(',\n              ')}
            }
          });
        }
      }`;

  // البحث عن مكان إدراج الكود
  const insertPoint = content.indexOf('// إنشاء audit log');
  if (insertPoint !== -1) {
    content = content.slice(0, insertPoint) + createItemsCode + '\n\n      ' + content.slice(insertPoint);
  }

  // تحديث الإرجاع لتضمين العناصر
  content = content.replace(
    /return order;/g,
    `// Return order with items
      const orderWithItems = await tx.${apiConfig.model}.findUnique({
        where: { id: order.id },
        include: { items: true }
      });

      return orderWithItems;`
  );

  // إزالة معالجة JSON من النتيجة
  content = content.replace(
    /\/\/ معالجة حقول JSON قبل الإرسال[\s\S]*?return NextResponse\.json\(processed[^)]+\);/g,
    'return NextResponse.json(result);'
  );

  fs.writeFileSync(filePath, content);
  console.log(`✅ Updated ${filePath}`);
}

// تحديث جميع الملفات
apiUpdates.forEach(updateApiFile);

console.log('🎉 All API endpoints updated successfully!');
