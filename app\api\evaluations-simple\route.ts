import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

export async function GET(request: NextRequest) {
  try {
    const evaluations = await prisma.evaluationOrder.findMany({
      orderBy: { id: 'desc' }
    });

    return NextResponse.json(evaluations);
  } catch (error) {
    console.error('Failed to fetch evaluations:', error);
    return NextResponse.json({ error: 'Failed to fetch evaluations' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const newEvaluation = await request.json();

    // Basic validation
    if (!newEvaluation.orderId || !newEvaluation.items) {
      return NextResponse.json(
        { error: 'Order ID and items are required' },
        { status: 400 }
      );
    }

    const evaluation = await prisma.evaluationOrder.create({
      data: {
        orderId: newEvaluation.orderId,
        items: newEvaluation.items,
        notes: newEvaluation.notes || null,
        createdBy: newEvaluation.createdBy || 'غير محدد',
        totalDevices: newEvaluation.totalDevices || 0
      }
    });

    return NextResponse.json(evaluation, { status: 201 });
  } catch (error) {
    console.error('Failed to create evaluation:', error);
    return NextResponse.json({ error: 'Failed to create evaluation' }, { status: 500 });
  }
}

export async function PUT(request: NextRequest) {
  try {
    const updatedEvaluation = await request.json();

    if (!updatedEvaluation.id) {
      return NextResponse.json(
        { error: 'Evaluation ID is required' },
        { status: 400 }
      );
    }

    const evaluation = await prisma.evaluationOrder.update({
      where: { id: updatedEvaluation.id },
      data: {
        ...(updatedEvaluation.orderId && { orderId: updatedEvaluation.orderId }),
        ...(updatedEvaluation.items && { items: updatedEvaluation.items }),
        ...(updatedEvaluation.notes !== undefined && { notes: updatedEvaluation.notes }),
        ...(updatedEvaluation.createdBy && { createdBy: updatedEvaluation.createdBy }),
        ...(updatedEvaluation.totalDevices !== undefined && { totalDevices: updatedEvaluation.totalDevices })
      }
    });

    return NextResponse.json(evaluation);
  } catch (error) {
    console.error('Failed to update evaluation:', error);
    return NextResponse.json({ error: 'Failed to update evaluation' }, { status: 500 });
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { id } = await request.json();

    if (!id) {
      return NextResponse.json(
        { error: 'Evaluation ID is required' },
        { status: 400 }
      );
    }

    await prisma.evaluationOrder.delete({
      where: { id }
    });

    return NextResponse.json({ message: 'Evaluation deleted successfully' });
  } catch (error) {
    console.error('Failed to delete evaluation:', error);
    return NextResponse.json({ error: 'Failed to delete evaluation' }, { status: 500 });
  }
}
