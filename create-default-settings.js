const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function createDefaultSettings() {
  try {
    console.log('🔧 إنشاء إعدادات النظام الافتراضية...\n');

    // التحقق من وجود إعدادات
    const existingSettings = await prisma.systemSetting.findFirst();
    
    if (existingSettings) {
      console.log('⏭️ الإعدادات موجودة بالفعل:', existingSettings.companyNameAr);
      return;
    }

    // إنشاء إعدادات افتراضية
    const defaultSettings = await prisma.systemSetting.create({
      data: {
        id: 1,
        logoUrl: '',
        companyNameAr: 'نظام إدارة الأجهزة',
        companyNameEn: 'Device Management System',
        addressAr: 'العنوان باللغة العربية',
        addressEn: 'Address in English',
        phone: '+967-1-234567',
        email: '<EMAIL>',
        website: 'www.devicemanagement.com',
        footerTextAr: 'جميع الحقوق محفوظة',
        footerTextEn: 'All Rights Reserved'
      }
    });

    console.log('✅ تم إنشاء إعدادات النظام الافتراضية:');
    console.log('   اسم الشركة (عربي):', defaultSettings.companyNameAr);
    console.log('   اسم الشركة (إنجليزي):', defaultSettings.companyNameEn);
    console.log('   الهاتف:', defaultSettings.phone);
    console.log('   البريد الإلكتروني:', defaultSettings.email);

  } catch (error) {
    console.error('❌ خطأ في إنشاء إعدادات النظام:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createDefaultSettings();
