/**
 * اختبار سريع لتوليد أرقام أوامر التوريد
 */

// محاكاة أوامر التوريد الموجودة
const mockSupplyOrders = [
  { supplyOrderId: 'SUP-1', id: 1 },
  { supplyOrderId: 'SUP-2', id: 2 },
  { supplyOrderId: 'SUP-5', id: 3 }, // رقم غير متسلسل
  { supplyOrderId: 'SUP-3', id: 4 }, // ترتيب مختلف
];

// دالة توليد الرقم الفريد
function generateUniqueSupplyId(supplyOrders) {
  let maxNumber = 0;
  supplyOrders.forEach(order => {
    if (order.supplyOrderId && order.supplyOrderId.startsWith('SUP-')) {
      const numberPart = parseInt(order.supplyOrderId.replace('SUP-', ''));
      if (!isNaN(numberPart) && numberPart > maxNumber) {
        maxNumber = numberPart;
      }
    }
  });
  
  return `SUP-${maxNumber + 1}`;
}

console.log('🧪 اختبار توليد أرقام أوامر التوريد\n');

console.log('📊 الأوامر الموجودة:');
mockSupplyOrders.forEach(order => {
  console.log(`  - ${order.supplyOrderId} (ID: ${order.id})`);
});

console.log('\n🔢 الرقم الجديد المولد:', generateUniqueSupplyId(mockSupplyOrders));

// اختبار مع قائمة فارغة
console.log('\n📝 اختبار مع قائمة فارغة:');
console.log('🔢 الرقم المولد:', generateUniqueSupplyId([]));

// اختبار مع أرقام غير منتظمة
const irregularOrders = [
  { supplyOrderId: 'SUP-10', id: 1 },
  { supplyOrderId: 'SUP-1', id: 2 },
  { supplyOrderId: 'SUP-15', id: 3 },
];

console.log('\n📊 أوامر غير منتظمة:');
irregularOrders.forEach(order => {
  console.log(`  - ${order.supplyOrderId}`);
});

console.log('🔢 الرقم المولد:', generateUniqueSupplyId(irregularOrders));

console.log('\n✅ الاختبار مكتمل');
