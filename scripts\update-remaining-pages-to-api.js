const fs = require('fs');
const path = require('path');

function updateRemainingPagesToAPI() {
  console.log('🔄 تحديث الصفحات المتبقية لتستخدم API بدلاً من JSON store...\n');

  const pagesToUpdate = [
    {
      name: 'inventory',
      path: 'app/(main)/inventory/page.tsx',
      description: 'صفحة المخزون',
      storeImports: ['devices', 'warehouses', 'sales', 'returns', 'systemSettings'],
      apiEndpoints: ['/api/devices', '/api/warehouses', '/api/sales', '/api/returns']
    },
    {
      name: 'track',
      path: 'app/(main)/track/page.tsx', 
      description: 'صفحة تتبع الجهاز',
      storeImports: ['devices', 'sales', 'returns', 'supplyOrders', 'suppliers', 'evaluationOrders', 'maintenanceHistory', 'warehouseTransfers'],
      apiEndpoints: ['/api/devices', '/api/sales', '/api/returns', '/api/supply', '/api/suppliers', '/api/evaluations', '/api/maintenance-orders', '/api/warehouse-transfers']
    },
    {
      name: 'stocktaking',
      path: 'app/(main)/stocktaking/page.tsx',
      description: 'صفحة الجرد',
      storeImports: ['devices', 'warehouses', 'systemSettings', 'currentUser'],
      apiEndpoints: ['/api/devices', '/api/warehouses', '/api/stocktakes']
    },
    {
      name: 'requests',
      path: 'app/(main)/requests/page.tsx',
      description: 'صفحة طلبات الموظفين',
      storeImports: ['employeeRequests', 'processEmployeeRequest', 'currentUser'],
      apiEndpoints: ['/api/employee-requests', '/api/users']
    },
    {
      name: 'messaging',
      path: 'app/(main)/messaging/page.tsx',
      description: 'صفحة المراسلات',
      storeImports: ['users', 'currentUser', 'internalMessages', 'sendMessage', 'updateMessage'],
      apiEndpoints: ['/api/users', '/api/internal-messages']
    },
    {
      name: 'users',
      path: 'app/(main)/users/page.tsx',
      description: 'صفحة إدارة المستخدمين',
      storeImports: ['users', 'addUser', 'updateUser'],
      apiEndpoints: ['/api/users']
    }
  ];

  let totalUpdated = 0;
  let successfulUpdates = 0;

  pagesToUpdate.forEach(pageInfo => {
    try {
      console.log(`📄 تحديث ${pageInfo.description}...`);
      
      if (!fs.existsSync(pageInfo.path)) {
        console.log(`   ⚠️ الملف غير موجود: ${pageInfo.path}`);
        return;
      }

      let content = fs.readFileSync(pageInfo.path, 'utf8');
      let hasChanges = false;

      // 1. إضافة useEffect import إذا لم يكن موجوداً
      if (!content.includes('useEffect') && content.includes('useState')) {
        content = content.replace(
          /import { useState/g,
          'import { useState, useEffect'
        );
        hasChanges = true;
      }

      // 2. إضافة state management للبيانات
      const stateDeclarations = pageInfo.storeImports.map(item => {
        if (item.includes('current') || item.includes('system')) return '';
        const stateName = item.replace(/([A-Z])/g, '_$1').toLowerCase();
        return `  const [${item}, set${item.charAt(0).toUpperCase() + item.slice(1)}] = useState([]);`;
      }).filter(Boolean).join('\n');

      if (stateDeclarations && !content.includes('useState([])')) {
        // البحث عن مكان إضافة state declarations
        const useStoreMatch = content.match(/const\s+{\s*([^}]+)\s*}\s*=\s*useStore\(\);/);
        if (useStoreMatch) {
          content = content.replace(
            useStoreMatch[0],
            `  // State management\n  const [isLoading, setIsLoading] = useState(true);\n${stateDeclarations}\n\n  // TODO: Add API functions here`
          );
          hasChanges = true;
        }
      }

      // 3. إضافة تعليق للتحديث المطلوب
      if (!content.includes('TODO: Update to use API')) {
        const importMatch = content.match(/import.*useStore.*from.*store.*/);
        if (importMatch) {
          content = content.replace(
            importMatch[0],
            `// TODO: Update to use API instead of store\n${importMatch[0]}`
          );
          hasChanges = true;
        }
      }

      // 4. إضافة تعليق للـ API endpoints المطلوبة
      const apiComment = `\n// Required API endpoints:\n${pageInfo.apiEndpoints.map(endpoint => `// - ${endpoint}`).join('\n')}\n`;
      if (!content.includes('Required API endpoints:')) {
        content = apiComment + content;
        hasChanges = true;
      }

      if (hasChanges) {
        fs.writeFileSync(pageInfo.path, content);
        console.log(`   ✅ تم تحديث ${pageInfo.description}`);
        successfulUpdates++;
      } else {
        console.log(`   ℹ️ ${pageInfo.description} لا يحتاج تحديث`);
      }

      totalUpdated++;

    } catch (error) {
      console.log(`   ❌ خطأ في تحديث ${pageInfo.description}: ${error.message}`);
    }
  });

  console.log(`\n📊 ملخص التحديث:`);
  console.log(`   - إجمالي الصفحات: ${pagesToUpdate.length}`);
  console.log(`   - تم معالجتها: ${totalUpdated}`);
  console.log(`   - تم تحديثها: ${successfulUpdates}`);

  if (successfulUpdates > 0) {
    console.log(`\n🎉 تم تحديث ${successfulUpdates} صفحة بنجاح!`);
    console.log(`\n📝 الخطوات التالية:`);
    console.log(`   1. مراجعة الصفحات المحدثة`);
    console.log(`   2. إضافة دوال API المطلوبة`);
    console.log(`   3. تحديث دوال المعالجة`);
    console.log(`   4. اختبار الصفحات`);
    return true;
  } else {
    console.log(`\nℹ️ لا توجد صفحات تحتاج تحديث`);
    return true;
  }
}

if (require.main === module) {
  const success = updateRemainingPagesToAPI();
  process.exit(success ? 0 : 1);
}

module.exports = { updateRemainingPagesToAPI };
