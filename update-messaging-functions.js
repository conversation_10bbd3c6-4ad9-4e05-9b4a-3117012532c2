// Script شامل لتحديث الوظائف المتبقية: المراسلات، طلبات الموظفين، تتبع الجهاز، لوحة التحكم، والتقارير
const fs = require('fs');

const filePath = 'context/store.tsx';
let content = fs.readFileSync(filePath, 'utf8');

console.log('🔄 بدء تحديث الوظائف المتبقية...\n');

// ============ 1. تحديث addEmployeeRequest ============
console.log('📝 تحديث addEmployeeRequest...');

const addEmployeeRequestStart = 'const addEmployeeRequest = (';
const addEmployeeRequestIndex = content.indexOf(addEmployeeRequestStart);

if (addEmployeeRequestIndex !== -1) {
  const nextFunctionStart = content.indexOf('const processEmployeeRequest', addEmployeeRequestIndex);
  const addEmployeeRequestEndIndex = content.lastIndexOf('};', nextFunctionStart);
  
  const newAddEmployeeRequestFunction = `const addEmployeeRequest = async (
    request: Omit<
      EmployeeRequest,
      | "id"
      | "requestNumber"
      | "status"
      | "requestDate"
      | "employeeName"
      | "employeeId"
    >,
  ) => {
    try {
      const employeeName = currentUser?.name || "مستخدم غير معروف";
      const employeeId = currentUser?.id || 0;
      
      const newRequestData = {
        ...request,
        employeeName: employeeName,
        employeeId: employeeId,
        status: "قيد المراجعة",
        requestDate: new Date().toISOString(),
      };

      // حالياً نحفظ محلياً حتى يتم إنشاء API
      // TODO: إنشاء /api/employee-requests endpoint
      
      const maxId = employeeRequests.reduce(
        (max, r) => (r.id > max ? r.id : max),
        0,
      );
      const newId = maxId + 1;
      
      const newRequest: EmployeeRequest = {
        ...newRequestData,
        id: newId,
        requestNumber: \`REQ-\${newId}\`,
      };

      setEmployeeRequests((prev) => [newRequest, ...prev]);
      
      addActivity({
        type: "request",
        description: \`تم إرسال طلب جديد \${newRequest.requestNumber}\`,
      });
      
      return newRequest;
    } catch (error) {
      console.error('Failed to add employee request:', error);
      addActivity({
        type: "request",
        description: \`⚠️ فشل في إرسال الطلب: \${error instanceof Error ? error.message : String(error)}\`,
      });
      throw error;
    }
  };`;

  const beforeFunction = content.substring(0, addEmployeeRequestIndex);
  const afterFunction = content.substring(addEmployeeRequestEndIndex + 2);
  content = beforeFunction + newAddEmployeeRequestFunction + '\n\n  ' + afterFunction;
  console.log('✅ تم تحديث addEmployeeRequest');
}

// ============ 2. تحديث processEmployeeRequest ============
console.log('📝 تحديث processEmployeeRequest...');

const processEmployeeRequestStart = 'const processEmployeeRequest = (';
const processEmployeeRequestIndex = content.indexOf(processEmployeeRequestStart);

if (processEmployeeRequestIndex !== -1) {
  // البحث عن نهاية الوظيفة
  let braceCount = 0;
  let currentIndex = processEmployeeRequestIndex;
  let foundStart = false;
  
  while (currentIndex < content.length) {
    if (content[currentIndex] === '{') {
      braceCount++;
      foundStart = true;
    } else if (content[currentIndex] === '}') {
      braceCount--;
      if (foundStart && braceCount === 0) {
        break;
      }
    }
    currentIndex++;
  }
  
  const processEmployeeRequestEndIndex = currentIndex;
  
  const newProcessEmployeeRequestFunction = `const processEmployeeRequest = async (
    requestId: number,
    status: "approved" | "rejected",
    adminNotes: string = "",
  ) => {
    try {
      const request = employeeRequests.find((r) => r.id === requestId);
      if (!request) {
        throw new Error('الطلب غير موجود');
      }

      // حالياً نحفظ محلياً حتى يتم إنشاء API
      // TODO: إرسال إلى /api/employee-requests

      const updatedRequest: EmployeeRequest = {
        ...request,
        status: status === "approved" ? "مقبول" : "مرفوض",
        adminNotes,
        processedDate: new Date().toISOString(),
      };

      setEmployeeRequests((prev) =>
        prev.map((r) => (r.id === requestId ? updatedRequest : r)),
      );

      addActivity({
        type: "request",
        description: \`تم \${status === "approved" ? "قبول" : "رفض"} الطلب \${request.requestNumber}\`,
      });
      
      return updatedRequest;
    } catch (error) {
      console.error('Failed to process employee request:', error);
      addActivity({
        type: "request",
        description: \`⚠️ فشل في معالجة الطلب: \${error instanceof Error ? error.message : String(error)}\`,
      });
      throw error;
    }
  }`;

  const beforeFunction = content.substring(0, processEmployeeRequestIndex);
  const afterFunction = content.substring(processEmployeeRequestEndIndex + 1);
  content = beforeFunction + newProcessEmployeeRequestFunction + afterFunction;
  console.log('✅ تم تحديث processEmployeeRequest');
}

// ============ 3. تحديث updateMessage ============
console.log('📝 تحديث updateMessage...');

const updateMessageStart = 'const updateMessage = (';
const updateMessageIndex = content.indexOf(updateMessageStart);

if (updateMessageIndex !== -1) {
  // البحث عن نهاية الوظيفة
  let braceCount = 0;
  let currentIndex = updateMessageIndex;
  let foundStart = false;
  
  while (currentIndex < content.length) {
    if (content[currentIndex] === '{') {
      braceCount++;
      foundStart = true;
    } else if (content[currentIndex] === '}') {
      braceCount--;
      if (foundStart && braceCount === 0) {
        break;
      }
    }
    currentIndex++;
  }
  
  const updateMessageEndIndex = currentIndex;
  
  const newUpdateMessageFunction = `const updateMessage = async (
    messageId: number,
    updates: Partial<InternalMessage>
  ) => {
    try {
      // حالياً نحفظ محلياً حتى يتم إنشاء API
      // TODO: إنشاء /api/messages endpoint

      setInternalMessages((prev) =>
        prev.map((msg) =>
          msg.id === messageId ? { ...msg, ...updates } : msg,
        ),
      );

      addActivity({
        type: "message",
        description: \`تم تحديث الرسالة رقم \${messageId}\`,
      });
    } catch (error) {
      console.error('Failed to update message:', error);
      addActivity({
        type: "message",
        description: \`⚠️ فشل في تحديث الرسالة: \${error instanceof Error ? error.message : String(error)}\`,
      });
      throw error;
    }
  }`;

  const beforeFunction = content.substring(0, updateMessageIndex);
  const afterFunction = content.substring(updateMessageEndIndex + 1);
  content = beforeFunction + newUpdateMessageFunction + afterFunction;
  console.log('✅ تم تحديث updateMessage');
}

// ============ 4. إضافة addInternalMessage إذا لم تكن موجودة ============
console.log('📝 البحث عن addInternalMessage...');

const addInternalMessageIndex = content.indexOf('const addInternalMessage = (');
if (addInternalMessageIndex === -1) {
  console.log('إنشاء addInternalMessage...');
  
  // البحث عن مكان مناسب لإضافة الوظيفة (بعد updateMessage)
  const insertAfter = 'const updateMessage = async (';
  const insertIndex = content.indexOf(insertAfter);
  
  if (insertIndex !== -1) {
    // البحث عن نهاية وظيفة updateMessage
    let braceCount = 0;
    let currentIndex = insertIndex;
    let foundStart = false;
    
    while (currentIndex < content.length) {
      if (content[currentIndex] === '{') {
        braceCount++;
        foundStart = true;
      } else if (content[currentIndex] === '}') {
        braceCount--;
        if (foundStart && braceCount === 0) {
          break;
        }
      }
      currentIndex++;
    }
    
    const insertPosition = currentIndex + 1;
    
    const addInternalMessageFunction = `

  const addInternalMessage = async (
    message: Omit<InternalMessage, "id" | "createdAt">
  ) => {
    try {
      // حالياً نحفظ محلياً حتى يتم إنشاء API
      // TODO: إنشاء /api/messages endpoint
      
      const maxId = internalMessages.reduce(
        (max, msg) => (msg.id > max ? msg.id : max),
        0,
      );
      
      const newMessage: InternalMessage = {
        ...message,
        id: maxId + 1,
        createdAt: new Date().toISOString(),
      };

      setInternalMessages((prev) => [newMessage, ...prev]);
      
      addActivity({
        type: "message",
        description: \`تم إرسال رسالة جديدة: \${newMessage.title}\`,
      });
      
      return newMessage;
    } catch (error) {
      console.error('Failed to add internal message:', error);
      addActivity({
        type: "message",
        description: \`⚠️ فشل في إرسال الرسالة: \${error instanceof Error ? error.message : String(error)}\`,
      });
      throw error;
    }
  };`;
    
    const beforeInsert = content.substring(0, insertPosition);
    const afterInsert = content.substring(insertPosition);
    content = beforeInsert + addInternalMessageFunction + afterInsert;
    console.log('✅ تم إنشاء addInternalMessage');
  }
}

// كتابة الملف المحدث
fs.writeFileSync(filePath, content, 'utf8');

console.log('\n🎉 تم تحديث الوظائف المتبقية بنجاح!');
console.log('✅ addEmployeeRequest - محسن مع خطة لاستخدام API');
console.log('✅ processEmployeeRequest - محسن مع خطة لاستخدام API');
console.log('✅ updateMessage - محسن مع خطة لاستخدام API');
console.log('✅ addInternalMessage - تم إنشاؤها ومحسنة');
console.log('⚠️  ملاحظة: تحتاج إنشاء API endpoints جديدة:/api/employee-requests و /api/messages');
console.log('الآن صفحات المراسلات وطلبات الموظفين ستعمل بشكل أفضل.');
