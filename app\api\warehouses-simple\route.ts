import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// GET - جلب جميع المخازن (بدون authentication للاختبار)
export async function GET() {
  try {
    const warehouses = await prisma.warehouse.findMany({
      orderBy: { name: 'asc' }
    });

    return NextResponse.json(warehouses);
  } catch (error) {
    console.error('خطأ في جلب المخازن:', error);
    return NextResponse.json(
      { error: 'فشل في جلب المخازن' },
      { status: 500 }
    );
  }
}

// POST - إنشاء مخزن جديد
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { name, location, type } = body;

    // التحقق من البيانات المطلوبة
    if (!name || !location || !type) {
      return NextResponse.json(
        { error: 'الاسم والموقع والنوع مطلوبة' },
        { status: 400 }
      );
    }

    // التحقق من عدم تكرار الاسم
    const existingWarehouse = await prisma.warehouse.findFirst({
      where: { name }
    });

    if (existingWarehouse) {
      return NextResponse.json(
        { error: 'يوجد مخزن بهذا الاسم بالفعل' },
        { status: 400 }
      );
    }

    const warehouse = await prisma.warehouse.create({
      data: {
        name,
        location,
        type
      }
    });

    return NextResponse.json(warehouse, { status: 201 });
  } catch (error) {
    console.error('خطأ في إنشاء المخزن:', error);
    return NextResponse.json(
      { error: 'فشل في إنشاء المخزن' },
      { status: 500 }
    );
  }
}

// PUT - تحديث مخزن
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { id, name, location, type } = body;

    if (!id) {
      return NextResponse.json(
        { error: 'معرف المخزن مطلوب' },
        { status: 400 }
      );
    }

    // التحقق من وجود المخزن
    const existingWarehouse = await prisma.warehouse.findUnique({
      where: { id: parseInt(id) }
    });

    if (!existingWarehouse) {
      return NextResponse.json(
        { error: 'المخزن غير موجود' },
        { status: 404 }
      );
    }

    // التحقق من عدم تكرار الاسم (إذا تم تغييره)
    if (name && name !== existingWarehouse.name) {
      const duplicateWarehouse = await prisma.warehouse.findFirst({
        where: { 
          name,
          id: { not: parseInt(id) }
        }
      });

      if (duplicateWarehouse) {
        return NextResponse.json(
          { error: 'يوجد مخزن آخر بهذا الاسم' },
          { status: 400 }
        );
      }
    }

    const warehouse = await prisma.warehouse.update({
      where: { id: parseInt(id) },
      data: {
        ...(name && { name }),
        ...(location && { location }),
        ...(type && { type })
      }
    });

    return NextResponse.json(warehouse);
  } catch (error) {
    console.error('خطأ في تحديث المخزن:', error);
    return NextResponse.json(
      { error: 'فشل في تحديث المخزن' },
      { status: 500 }
    );
  }
}

// DELETE - حذف مخزن
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json(
        { error: 'معرف المخزن مطلوب' },
        { status: 400 }
      );
    }

    // التحقق من وجود المخزن
    const existingWarehouse = await prisma.warehouse.findUnique({
      where: { id: parseInt(id) }
    });

    if (!existingWarehouse) {
      return NextResponse.json(
        { error: 'المخزن غير موجود' },
        { status: 404 }
      );
    }

    // التحقق من وجود أجهزة في المخزن
    const devicesCount = await prisma.device.count({
      where: { warehouseId: parseInt(id) }
    });

    if (devicesCount > 0) {
      return NextResponse.json(
        { error: `لا يمكن حذف المخزن لأنه يحتوي على ${devicesCount} جهاز` },
        { status: 400 }
      );
    }

    // التحقق من وجود أوامر مرتبطة
    const supplyOrdersCount = await prisma.supplyOrder.count({
      where: { warehouseId: parseInt(id) }
    });

    if (supplyOrdersCount > 0) {
      return NextResponse.json(
        { error: `لا يمكن حذف المخزن لأنه مرتبط بـ ${supplyOrdersCount} أمر توريد` },
        { status: 400 }
      );
    }

    await prisma.warehouse.delete({
      where: { id: parseInt(id) }
    });

    return NextResponse.json({ message: 'تم حذف المخزن بنجاح' });
  } catch (error) {
    console.error('خطأ في حذف المخزن:', error);
    return NextResponse.json(
      { error: 'فشل في حذف المخزن' },
      { status: 500 }
    );
  }
}
