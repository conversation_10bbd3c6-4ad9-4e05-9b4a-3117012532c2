const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testAuth() {
  console.log('🔧 اختبار نظام التفويض والمستخدمين...');
  
  try {
    // 1. التأكد من وجود مستخدم admin
    let adminUser = await prisma.user.findFirst({
      where: { username: 'admin' }
    });
    
    if (!adminUser) {
      adminUser = await prisma.user.create({
        data: {
          username: 'admin',
          email: '<EMAIL>',
          name: 'System Administrator',
          role: 'admin',
          status: 'Active'
        }
      });
      console.log('✅ تم إنشاء مستخدم admin:', adminUser);
    } else {
      console.log('✅ مستخدم admin موجود:', { id: adminUser.id, username: adminUser.username, role: adminUser.role });
    }

    // 2. اختبار إنشاء token
    const tokenData = `user:admin:admin`;
    const token = Buffer.from(tokenData).toString('base64');
    console.log('✅ Token للاختبار:', token);
    
    // 3. عرض معلومات AuditLog schema
    const auditLogs = await prisma.auditLog.findMany({ take: 5 });
    console.log('✅ AuditLogs في قاعدة البيانات:', auditLogs.length);
    
    // 4. اختبار createAuditLog مباشرة
    try {
      const testLog = await prisma.auditLog.create({
        data: {
          userId: adminUser.id,
          username: adminUser.username,
          operation: 'TEST',
          details: 'Test audit log creation',
          timestamp: new Date()
        }
      });
      console.log('✅ تم إنشاء audit log بنجاح:', testLog.id);
    } catch (error) {
      console.error('❌ خطأ في إنشاء audit log:', error.message);
    }
    
    console.log('\n📋 ملخص:');
    console.log('- admin user ID:', adminUser.id, '(type:', typeof adminUser.id, ')');
    console.log('- admin username:', adminUser.username);
    console.log('- admin role:', adminUser.role);
    console.log('- Test token:', token);
    console.log('\n💡 استخدم هذا Token في Authorization header:');
    console.log(`Authorization: Bearer ${token}`);
    
  } catch (error) {
    console.error('❌ خطأ في الاختبار:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testAuth();
