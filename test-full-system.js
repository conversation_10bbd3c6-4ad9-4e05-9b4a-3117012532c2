// Final test script for supply order system
const fs = require('fs').promises;

async function testSupplySystem() {
  console.log('🧪 اختبار شامل لنظام أوامر التوريد...');
  
  const baseUrl = 'http://localhost:9005';
  
  try {
    // 1. Test device models API
    console.log('\n1️⃣ اختبار API الموديلات...');
    const modelsResponse = await fetch(`${baseUrl}/api/device-models`);
    if (modelsResponse.ok) {
      const models = await modelsResponse.json();
      console.log(`✅ تم جلب ${models.length} موديل من قاعدة البيانات`);
    } else {
      console.log('❌ فشل في جلب الموديلات:', modelsResponse.status);
    }
    
    // 2. Test suppliers API
    console.log('\n2️⃣ اختبار API الموردين...');
    const suppliersResponse = await fetch(`${baseUrl}/api/suppliers`);
    if (suppliersResponse.ok) {
      const suppliers = await suppliersResponse.json();
      console.log(`✅ تم جلب ${suppliers.length} مورد`);
    } else {
      console.log('❌ فشل في جلب الموردين:', suppliersResponse.status);
    }
    
    // 3. Test warehouses API
    console.log('\n3️⃣ اختبار API المخازن...');
    const warehousesResponse = await fetch(`${baseUrl}/api/warehouses`);
    if (warehousesResponse.ok) {
      const warehouses = await warehousesResponse.json();
      console.log(`✅ تم جلب ${warehouses.length} مخزن`);
    } else {
      console.log('❌ فشل في جلب المخازن:', warehousesResponse.status);
    }
    
    // 4. Test supply orders creation
    console.log('\n4️⃣ اختبار إنشاء أمر توريد...');
    const testOrder = {
      supplierId: 1,
      warehouseId: 1,
      employeeName: 'System Administrator',
      invoiceNumber: null, // اختبار القيمة الفارغة
      supplyDate: new Date().toISOString().slice(0, 16),
      notes: 'اختبار نهائي للنظام',
      items: [
        {
          imei: `TEST${Date.now()}`,
          manufacturer: 'Samsung',
          model: 'Galaxy S24',
          condition: 'جديد'
        }
      ],
      referenceNumber: `REF-${Date.now()}`,
      status: 'completed'
    };
    
    const createResponse = await fetch(`${baseUrl}/api/supply`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + btoa('user:admin:admin')
      },
      body: JSON.stringify(testOrder)
    });
    
    if (createResponse.ok) {
      const result = await createResponse.json();
      console.log(`✅ تم إنشاء أمر التوريد: ${result.supplyOrderId}`);
      
      // 5. Test supply orders listing
      console.log('\n5️⃣ اختبار قائمة أوامر التوريد...');
      const listResponse = await fetch(`${baseUrl}/api/supply`);
      if (listResponse.ok) {
        const orders = await listResponse.json();
        console.log(`✅ تم جلب ${orders.length} أمر توريد`);
        
        // Show latest orders
        console.log('\n📋 آخر 3 أوامر توريد:');
        orders.slice(0, 3).forEach((order, index) => {
          console.log(`${index + 1}. ${order.supplyOrderId} - ${order.employeeName} - ${new Date(order.createdAt).toLocaleDateString()}`);
        });
      }
    } else {
      const errorText = await createResponse.text();
      console.log('❌ فشل في إنشاء أمر التوريد:', createResponse.status);
      console.log('تفاصيل الخطأ:', errorText);
    }
    
    console.log('\n🎉 انتهى الاختبار الشامل!');
    
  } catch (error) {
    console.error('❌ خطأ في الاختبار:', error.message);
  }
}

testSupplySystem();
