async function testUploadWithAuth() {
  try {
    console.log('اختبار رفع المرفقات مع التفويض...');

    // إنشاء ملف تجريبي
    const testFileContent = new Blob(['هذا ملف تجريبي للاختبار لأوامر التوريد'], { type: 'text/plain' });
    const testFile = new File([testFileContent], 'test-supply-attachment.txt', { type: 'text/plain' });

    // إنشاء FormData
    const formData = new FormData();
    formData.append('files', testFile);
    formData.append('section', 'supply');

    // إنشاء تفويض وهمي للاختبار (admin:admin)
    const authToken = btoa('user:admin:admin'); // base64 encode

    console.log('محاولة رفع الملف إلى قسم supply مع التفويض...');

    // محاولة رفع الملف مع التفويض
    const response = await fetch('http://localhost:9005/api/upload', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${authToken}`
      },
      body: formData,
    });

    const result = await response.json();

    if (response.ok && result.success) {
      console.log('✅ تم رفع الملف بنجاح!');
      console.log('✅ الرسالة:', result.message);
      console.log('✅ الملفات المرفوعة:', result.files.length);
      
      // عرض تفاصيل الملفات
      result.files.forEach((file, index) => {
        console.log(`  الملف ${index + 1}:`);
        console.log(`    الاسم الأصلي: ${file.originalName}`);
        console.log(`    اسم الملف الجديد: ${file.fileName}`);
        console.log(`    المسار: ${file.filePath}`);
        console.log(`    الحجم: ${file.size} بايت`);
        console.log(`    النوع: ${file.type}`);
      });

      console.log('\n🎉 تم حل مشكلة رفع المرفقات بنجاح!');
      console.log('   ✅ قسم "supply" تم إضافته للقائمة المسموحة');
      console.log('   ✅ API يتعامل بشكل صحيح مع ملفات التوريد');
      
    } else {
      console.log('❌ فشل في رفع الملف');
      console.log('❌ حالة الاستجابة:', response.status);
      console.log('❌ رسالة الخطأ:', result.error || result.message);
      
      if (result.error === 'قسم غير صالح') {
        console.log('🚨 المشكلة: قسم "supply" لا يزال غير مدرج في القائمة المسموحة!');
      } else if (result.error.includes('authorization')) {
        console.log('🔒 المشكلة: مشكلة في التفويض');
      }
    }

  } catch (error) {
    console.error('❌ خطأ في الاختبار:', error.message);
  }
}

// تشغيل الاختبار
testUploadWithAuth();
