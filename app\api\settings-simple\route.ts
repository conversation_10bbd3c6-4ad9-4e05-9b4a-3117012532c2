import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

export async function GET(request: NextRequest) {
  try {
    // محاولة جلب الإعدادات من قاعدة البيانات
    const settings = await prisma.systemSetting.findFirst();
    
    // إذا لم توجد إعدادات، إرجاع إعدادات افتراضية
    if (!settings) {
      const defaultSettings = {
        id: 1,
        logoUrl: '',
        companyNameAr: 'نظام إدارة الأجهزة',
        companyNameEn: 'Device Management System',
        addressAr: '',
        addressEn: '',
        phone: '',
        email: '',
        website: '',
        footerTextAr: '',
        footerTextEn: '',
        updatedAt: new Date(),
        createdAt: new Date()
      };
      
      return NextResponse.json(defaultSettings);
    }
    
    return NextResponse.json(settings);
  } catch (error) {
    console.error('Failed to fetch settings:', error);
    
    // في حالة الخطأ، إرجاع إعدادات افتراضية
    const defaultSettings = {
      id: 1,
      logoUrl: '',
      companyNameAr: 'نظام إدارة الأجهزة',
      companyNameEn: 'Device Management System',
      addressAr: '',
      addressEn: '',
      phone: '',
      email: '',
      website: '',
      footerTextAr: '',
      footerTextEn: '',
      updatedAt: new Date(),
      createdAt: new Date()
    };
    
    return NextResponse.json(defaultSettings);
  }
}

export async function POST(request: NextRequest) {
  try {
    const newSettings = await request.json();

    // Basic validation
    if (!newSettings.companyNameAr && !newSettings.companyNameEn) {
      return NextResponse.json(
        { error: 'Company name is required' },
        { status: 400 }
      );
    }

    // Create or update settings
    const settings = await prisma.systemSetting.upsert({
      where: { id: 1 },
      update: {
        logoUrl: newSettings.logoUrl || '',
        companyNameAr: newSettings.companyNameAr || 'نظام إدارة الأجهزة',
        companyNameEn: newSettings.companyNameEn || 'Device Management System',
        addressAr: newSettings.addressAr || '',
        addressEn: newSettings.addressEn || '',
        phone: newSettings.phone || '',
        email: newSettings.email || '',
        website: newSettings.website || '',
        footerTextAr: newSettings.footerTextAr || '',
        footerTextEn: newSettings.footerTextEn || ''
      },
      create: {
        id: 1,
        logoUrl: newSettings.logoUrl || '',
        companyNameAr: newSettings.companyNameAr || 'نظام إدارة الأجهزة',
        companyNameEn: newSettings.companyNameEn || 'Device Management System',
        addressAr: newSettings.addressAr || '',
        addressEn: newSettings.addressEn || '',
        phone: newSettings.phone || '',
        email: newSettings.email || '',
        website: newSettings.website || '',
        footerTextAr: newSettings.footerTextAr || '',
        footerTextEn: newSettings.footerTextEn || ''
      }
    });

    return NextResponse.json(settings, { status: 201 });
  } catch (error) {
    console.error('Failed to create/update settings:', error);
    return NextResponse.json({ error: 'Failed to create/update settings' }, { status: 500 });
  }
}

export async function PUT(request: NextRequest) {
  try {
    const updatedSettings = await request.json();

    const settings = await prisma.systemSetting.upsert({
      where: { id: 1 },
      update: {
        ...(updatedSettings.logoUrl !== undefined && { logoUrl: updatedSettings.logoUrl }),
        ...(updatedSettings.companyNameAr && { companyNameAr: updatedSettings.companyNameAr }),
        ...(updatedSettings.companyNameEn && { companyNameEn: updatedSettings.companyNameEn }),
        ...(updatedSettings.addressAr !== undefined && { addressAr: updatedSettings.addressAr }),
        ...(updatedSettings.addressEn !== undefined && { addressEn: updatedSettings.addressEn }),
        ...(updatedSettings.phone !== undefined && { phone: updatedSettings.phone }),
        ...(updatedSettings.email !== undefined && { email: updatedSettings.email }),
        ...(updatedSettings.website !== undefined && { website: updatedSettings.website }),
        ...(updatedSettings.footerTextAr !== undefined && { footerTextAr: updatedSettings.footerTextAr }),
        ...(updatedSettings.footerTextEn !== undefined && { footerTextEn: updatedSettings.footerTextEn })
      },
      create: {
        id: 1,
        logoUrl: updatedSettings.logoUrl || '',
        companyNameAr: updatedSettings.companyNameAr || 'نظام إدارة الأجهزة',
        companyNameEn: updatedSettings.companyNameEn || 'Device Management System',
        addressAr: updatedSettings.addressAr || '',
        addressEn: updatedSettings.addressEn || '',
        phone: updatedSettings.phone || '',
        email: updatedSettings.email || '',
        website: updatedSettings.website || '',
        footerTextAr: updatedSettings.footerTextAr || '',
        footerTextEn: updatedSettings.footerTextEn || ''
      }
    });

    return NextResponse.json(settings);
  } catch (error) {
    console.error('Failed to update settings:', error);
    return NextResponse.json({ error: 'Failed to update settings' }, { status: 500 });
  }
}
