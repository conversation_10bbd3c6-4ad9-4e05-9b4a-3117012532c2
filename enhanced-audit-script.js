const { PrismaClient } = require('@prisma/client');
const fs = require('fs');
const path = require('path');

const prisma = new PrismaClient();

async function enhancedSystemAudit() {
  const auditResults = {
    timestamp: new Date().toISOString(),
    database: {
      connection: false,
      models: {},
      relationships: {},
      dataIntegrity: {},
      performance: {}
    },
    apis: {
      endpoints: [],
      status: {}
    },
    errors: [],
    warnings: [],
    recommendations: [],
    summary: {}
  };

  console.log('🔍 بدء الفحص الشامل المحسن للنظام...\n');

  try {
    // 1. فحص الاتصال بقاعدة البيانات
    console.log('📊 فحص الاتصال بقاعدة البيانات...');
    await prisma.$connect();
    auditResults.database.connection = true;
    console.log('✅ الاتصال بقاعدة البيانات: نجح\n');

    // 2. فحص النماذج والجداول
    console.log('🗄️ فحص النماذج والجداول...');
    const models = [
      { name: 'User', model: 'user' },
      { name: 'Warehouse', model: 'warehouse' },
      { name: 'Client', model: 'client' },
      { name: 'Supplier', model: 'supplier' },
      { name: 'Device', model: 'device' },
      { name: 'DeviceModel', model: 'deviceModel' },
      { name: 'SupplyOrder', model: 'supplyOrder' },
      { name: 'SupplyOrderItem', model: 'supplyOrderItem' },
      { name: 'Sale', model: 'sale' },
      { name: 'SaleItem', model: 'saleItem' },
      { name: 'Return', model: 'return' },
      { name: 'ReturnItem', model: 'returnItem' },
      { name: 'EvaluationOrder', model: 'evaluationOrder' },
      { name: 'EvaluationOrderItem', model: 'evaluationOrderItem' },
      { name: 'MaintenanceOrder', model: 'maintenanceOrder' },
      { name: 'MaintenanceOrderItem', model: 'maintenanceOrderItem' },
      { name: 'DeliveryOrder', model: 'deliveryOrder' },
      { name: 'DeliveryOrderItem', model: 'deliveryOrderItem' },
      { name: 'MaintenanceReceiptOrder', model: 'maintenanceReceiptOrder' },
      { name: 'MaintenanceReceiptOrderItem', model: 'maintenanceReceiptOrderItem' },
      { name: 'EmployeeRequest', model: 'employeeRequest' },
      { name: 'InternalMessage', model: 'internalMessage' },
      { name: 'AuditLog', model: 'auditLog' }
    ];

    let totalRecords = 0;
    let totalQueryTime = 0;

    for (const modelInfo of models) {
      try {
        const startTime = Date.now();
        const count = await prisma[modelInfo.model].count();
        const queryTime = Date.now() - startTime;
        
        auditResults.database.models[modelInfo.name] = {
          exists: true,
          count: count,
          queryTime: queryTime
        };
        
        totalRecords += count;
        totalQueryTime += queryTime;
        
        console.log(`✅ ${modelInfo.name}: ${count} سجل (${queryTime}ms)`);
        
      } catch (error) {
        auditResults.database.models[modelInfo.name] = {
          exists: false,
          error: error.message
        };
        auditResults.errors.push(`نموذج ${modelInfo.name}: ${error.message}`);
        console.log(`❌ ${modelInfo.name}: خطأ - ${error.message}`);
      }
    }

    auditResults.summary.totalRecords = totalRecords;
    auditResults.summary.totalQueryTime = totalQueryTime;

    // 3. فحص العلاقات بطريقة محسنة
    console.log('\n🔗 فحص العلاقات والتكامل المرجعي...');
    
    const relationshipTests = [
      {
        name: 'SupplyOrder-SupplyOrderItem',
        test: async () => {
          const ordersWithItems = await prisma.supplyOrder.findMany({
            include: { items: true },
            take: 5
          });
          return {
            ordersCount: ordersWithItems.length,
            itemsCount: ordersWithItems.reduce((sum, order) => sum + order.items.length, 0),
            isValid: true
          };
        }
      },
      {
        name: 'Sale-SaleItem',
        test: async () => {
          const salesWithItems = await prisma.sale.findMany({
            include: { items: true },
            take: 5
          });
          return {
            salesCount: salesWithItems.length,
            itemsCount: salesWithItems.reduce((sum, sale) => sum + sale.items.length, 0),
            isValid: true
          };
        }
      },
      {
        name: 'Return-ReturnItem',
        test: async () => {
          const returnsWithItems = await prisma.return.findMany({
            include: { items: true },
            take: 5
          });
          return {
            returnsCount: returnsWithItems.length,
            itemsCount: returnsWithItems.reduce((sum, ret) => sum + ret.items.length, 0),
            isValid: true
          };
        }
      }
    ];

    for (const test of relationshipTests) {
      try {
        const result = await test.test();
        auditResults.database.relationships[test.name] = result;
        console.log(`✅ ${test.name}: ${result.ordersCount || result.salesCount || result.returnsCount} أوامر، ${result.itemsCount} عنصر`);
      } catch (error) {
        auditResults.errors.push(`فحص العلاقة ${test.name}: ${error.message}`);
        console.log(`❌ فحص العلاقة ${test.name}: ${error.message}`);
      }
    }

    // 4. فحص الأداء
    console.log('\n⚡ فحص الأداء...');
    
    const performanceTests = [
      {
        name: 'Simple Count Query',
        test: async () => {
          const startTime = Date.now();
          await prisma.device.count();
          return Date.now() - startTime;
        }
      },
      {
        name: 'Complex Join Query',
        test: async () => {
          const startTime = Date.now();
          await prisma.supplyOrder.findMany({
            include: { items: true },
            take: 10
          });
          return Date.now() - startTime;
        }
      },
      {
        name: 'Aggregation Query',
        test: async () => {
          const startTime = Date.now();
          await prisma.supplyOrderItem.groupBy({
            by: ['condition'],
            _count: { id: true }
          });
          return Date.now() - startTime;
        }
      }
    ];

    for (const test of performanceTests) {
      try {
        const time = await test.test();
        auditResults.database.performance[test.name] = time;
        
        if (time < 50) {
          console.log(`✅ ${test.name}: ${time}ms (ممتاز)`);
        } else if (time < 200) {
          console.log(`⚠️ ${test.name}: ${time}ms (مقبول)`);
          auditResults.warnings.push(`${test.name}: أداء مقبول (${time}ms)`);
        } else {
          console.log(`❌ ${test.name}: ${time}ms (بطيء)`);
          auditResults.warnings.push(`${test.name}: أداء بطيء (${time}ms)`);
        }
      } catch (error) {
        auditResults.errors.push(`اختبار الأداء ${test.name}: ${error.message}`);
        console.log(`❌ اختبار الأداء ${test.name}: ${error.message}`);
      }
    }

    // 5. فحص تكامل البيانات
    console.log('\n🔍 فحص تكامل البيانات...');
    
    try {
      // فحص الأجهزة المكررة
      const duplicateDevices = await prisma.device.groupBy({
        by: ['id'],
        _count: { id: true },
        having: { id: { _count: { gt: 1 } } }
      });
      
      auditResults.database.dataIntegrity.duplicateDevices = duplicateDevices.length;
      
      if (duplicateDevices.length === 0) {
        console.log('✅ لا توجد أجهزة مكررة');
      } else {
        console.log(`⚠️ يوجد ${duplicateDevices.length} جهاز مكرر`);
        auditResults.warnings.push(`يوجد ${duplicateDevices.length} جهاز مكرر`);
      }
      
    } catch (error) {
      auditResults.errors.push(`فحص تكامل البيانات: ${error.message}`);
      console.log(`❌ فحص تكامل البيانات: ${error.message}`);
    }

    // 6. فحص APIs
    console.log('\n🌐 فحص APIs...');
    
    const apiEndpoints = [
      '/api/warehouses-simple',
      '/api/clients-simple',
      '/api/suppliers-simple',
      '/api/supply',
      '/api/sales',
      '/api/returns',
      '/api/devices',
      '/api/users'
    ];

    auditResults.apis.endpoints = apiEndpoints;
    
    for (const endpoint of apiEndpoints) {
      const exists = fs.existsSync(path.join(__dirname, 'app', 'api', endpoint.replace('/api/', ''), 'route.ts'));
      auditResults.apis.status[endpoint] = exists ? 'موجود' : 'غير موجود';
      
      if (exists) {
        console.log(`✅ ${endpoint}: موجود`);
      } else {
        console.log(`❌ ${endpoint}: غير موجود`);
        auditResults.errors.push(`API endpoint غير موجود: ${endpoint}`);
      }
    }

    // إنشاء التوصيات
    console.log('\n💡 إنشاء التوصيات...');
    
    if (auditResults.errors.length === 0) {
      auditResults.recommendations.push('✅ النظام يعمل بشكل ممتاز');
    } else {
      auditResults.recommendations.push(`❌ يوجد ${auditResults.errors.length} خطأ يحتاج للإصلاح`);
    }
    
    if (auditResults.warnings.length > 0) {
      auditResults.recommendations.push(`⚠️ يوجد ${auditResults.warnings.length} تحذير يحتاج للمراجعة`);
    }
    
    if (totalRecords > 1000) {
      auditResults.recommendations.push('💡 يُنصح بإضافة فهارس للجداول الكبيرة');
    }
    
    if (totalQueryTime > 500) {
      auditResults.recommendations.push('💡 يُنصح بتحسين الاستعلامات البطيئة');
    }

    // حفظ التقرير
    const reportPath = path.join(__dirname, 'enhanced-audit-report.json');
    fs.writeFileSync(reportPath, JSON.stringify(auditResults, null, 2));
    
    console.log(`\n✅ تم حفظ التقرير المحسن في: ${reportPath}`);
    
    return auditResults;
    
  } catch (error) {
    auditResults.errors.push(`خطأ عام في الفحص: ${error.message}`);
    console.error('❌ خطأ في الفحص الشامل:', error);
    return auditResults;
  } finally {
    await prisma.$disconnect();
  }
}

// تشغيل الفحص
enhancedSystemAudit()
  .then(results => {
    console.log('\n🎯 ملخص النتائج النهائي:');
    console.log(`📊 إجمالي السجلات: ${results.summary.totalRecords || 0}`);
    console.log(`⏱️ إجمالي وقت الاستعلام: ${results.summary.totalQueryTime || 0}ms`);
    console.log(`✅ النماذج الصحيحة: ${Object.keys(results.database.models).filter(m => results.database.models[m].exists).length}`);
    console.log(`❌ الأخطاء: ${results.errors.length}`);
    console.log(`⚠️ التحذيرات: ${results.warnings.length}`);
    console.log(`💡 التوصيات: ${results.recommendations.length}`);
    
    if (results.recommendations.length > 0) {
      console.log('\n📋 التوصيات:');
      results.recommendations.forEach(rec => console.log(`  ${rec}`));
    }
  })
  .catch(console.error);
