const fs = require('fs');
const path = require('path');

// مسار ملف store.tsx
const storeFilePath = path.join(__dirname, 'context', 'store.tsx');

// قراءة محتوى الملف
let content = fs.readFileSync(storeFilePath, 'utf8');

console.log('🔧 بدء تحديث جميع استخدامات fetch إلى apiClient...');

// استبدال جميع الاستخدامات POST
const postPatterns = [
  {
    from: /await fetch\(["']([^"']+)["'], \{\s*method: ["']POST["'],\s*headers: \{ ["']Content-Type["']: ["']application\/json["'] \},\s*body: JSON\.stringify\(([^)]+)\),?\s*\}\)/g,
    to: 'await apiClient.post("$1", $2)'
  }
];

// استبدال جميع الاستخدامات PUT
const putPatterns = [
  {
    from: /await fetch\(`([^`]+)`, \{\s*method: ["']PUT["'],\s*headers: \{ ["']Content-Type["']: ["']application\/json["'] \},\s*body: JSON\.stringify\(([^)]+)\),?\s*\}\)/g,
    to: 'await apiClient.put(`$1`, $2)'
  },
  {
    from: /await fetch\(["']([^"']+)["'], \{\s*method: ["']PUT["'],\s*headers: \{ ["']Content-Type["']: ["']application\/json["'] \},\s*body: JSON\.stringify\(([^)]+)\),?\s*\}\)/g,
    to: 'await apiClient.put("$1", $2)'
  }
];

// استبدال جميع الاستخدامات DELETE
const deletePatterns = [
  {
    from: /await fetch\(["']([^"']+)["'], \{\s*method: ["']DELETE["'],\s*headers: \{ ["']Content-Type["']: ["']application\/json["'] \},\s*body: JSON\.stringify\(([^)]+)\),?\s*\}\)/g,
    to: 'await apiClient.delete("$1", $2)'
  }
];

// تطبيق التحديثات
let changesMade = 0;

// تحديث POST
postPatterns.forEach(pattern => {
  const matches = content.match(pattern.from);
  if (matches) {
    content = content.replace(pattern.from, pattern.to);
    changesMade += matches.length;
    console.log(`✅ تم تحديث ${matches.length} POST request(s)`);
  }
});

// تحديث PUT  
putPatterns.forEach(pattern => {
  const matches = content.match(pattern.from);
  if (matches) {
    content = content.replace(pattern.from, pattern.to);
    changesMade += matches.length;
    console.log(`✅ تم تحديث ${matches.length} PUT request(s)`);
  }
});

// تحديث DELETE
deletePatterns.forEach(pattern => {
  const matches = content.match(pattern.from);
  if (matches) {
    content = content.replace(pattern.from, pattern.to);
    changesMade += matches.length;
    console.log(`✅ تم تحديث ${matches.length} DELETE request(s)`);
  }
});

// التحديثات الإضافية المطلوبة
const additionalUpdates = [
  // إضافة import إذا لم يكن موجود
  {
    check: /import.*apiClient.*from/,
    add: `import { apiClient } from '@/lib/api-client';`,
    position: 'imports'
  }
];

// فحص وإضافة import إذا لم يكن موجود
if (!content.includes('apiClient')) {
  // البحث عن آخر import
  const importLines = content.split('\n');
  let lastImportIndex = -1;
  
  for (let i = 0; i < importLines.length; i++) {
    if (importLines[i].trim().startsWith('import ') && importLines[i].includes('from')) {
      lastImportIndex = i;
    }
  }
  
  if (lastImportIndex !== -1) {
    importLines.splice(lastImportIndex + 1, 0, `import { apiClient } from '@/lib/api-client';`);
    content = importLines.join('\n');
    changesMade++;
    console.log('✅ تم إضافة import apiClient');
  }
}

// كتابة الملف المحدث
if (changesMade > 0) {
  fs.writeFileSync(storeFilePath, content, 'utf8');
  console.log(`\n🎉 تم تحديث ${changesMade} تغيير في store.tsx`);
  console.log('✅ الآن جميع طلبات API تستخدم apiClient مع headers التفويض الصحيحة');
} else {
  console.log('⚠️ لم يتم العثور على تحديثات مطلوبة');
}

console.log('\n📝 ملاحظات هامة:');
console.log('1. تأكد من أن apiClient يرسل التوكن الصحيح');
console.log('2. جميع APIs المحمية تتطلب Authorization header');
console.log('3. التوكن المستخدم: btoa("user:admin:admin")');
