// سكربت تحديث وظائف الصيانة والتسليم والاستلام
const fs = require('fs');
const path = require('path');

// مسار ملف store.tsx
const storeFilePath = path.join(__dirname, 'context', 'store.tsx');

// قراءة محتوى الملف
console.log('قراءة الملف...');
let fileContent = fs.readFileSync(storeFilePath, 'utf8');

// إنشاء نسخة احتياطية
const backupPath = path.join(__dirname, 'context', `store.tsx.backup-${Date.now()}`);
fs.writeFileSync(backupPath, fileContent, 'utf8');
console.log(`تم إنشاء نسخة احتياطية في ${backupPath}`);

console.log('تحديث وظائف الصيانة...');

// تحديد المحتوى السابق والمحتوى الجديد لوظيفة addMaintenanceOrder
// الكثير من الأكواد السابقة تم تجاوزها لأن صيغة التعبير العادي لم تنجح
// سنستخدم أسلوبًا مختلفًا باستخدام الإشارات النصية

// تحديث وظيفة addMaintenanceOrder
function updateAddMaintenanceOrder() {
  // البحث عن وظيفة addMaintenanceOrder
  const funcStart = fileContent.indexOf('const addMaintenanceOrder = async');
  if (funcStart === -1) {
    console.error('لم يتم العثور على وظيفة addMaintenanceOrder');
    return;
  }

  // البحث عن نهاية الوظيفة
  let braceCount = 0;
  let funcEnd = funcStart;
  let foundStart = false;
  
  for (let i = funcStart; i < fileContent.length; i++) {
    const char = fileContent[i];
    if (char === '{') {
      braceCount++;
      foundStart = true;
    } else if (char === '}') {
      braceCount--;
      if (foundStart && braceCount === 0) {
        funcEnd = i + 1;
        break;
      }
    }
  }

  // التحقق من أننا وجدنا نهاية الوظيفة
  if (funcEnd === funcStart) {
    console.error('لم يتم العثور على نهاية وظيفة addMaintenanceOrder');
    return;
  }

  // استبدال الوظيفة القديمة بالجديدة
  const oldFunction = fileContent.substring(funcStart, funcEnd);
  const newFunction = `const addMaintenanceOrder = async (
    order: Omit<MaintenanceOrder, "id" | "createdAt"> & {
      id?: number;
      status?: "wip" | "completed" | "draft";
    },
  ) => {
    try {
      // إذا لم يكن هناك رقم أمر، نقوم بإنشاء واحد
      if (!order.orderNumber) {
        const allExisting = [...maintenanceOrders];
        const useId =
          order.id && order.id > 0
            ? order.id
            : Math.max(0, ...allExisting.map((o) => o.id)) + 1;
        order.orderNumber = `MAINT-${useId}`;
      }
      
      // إرسال الأمر إلى API
      const response = await fetch('/api/maintenance-orders', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...order,
          status: order.status || 'wip',
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create maintenance order');
      }

      // استقبال الأمر الذي تم إنشاؤه من API
      const newOrder = await response.json();

      // تحديث حالة التطبيق
      setMaintenanceOrders((prev) => [newOrder, ...prev]);

      // إضافة نشاط
      addActivity({
        type: "maintenance",
        description: `تم إنشاء أمر صيانة ${newOrder.orderNumber}.`,
      });
      
      return newOrder;
    } catch (error) {
      console.error('Failed to add maintenance order:', error);
      addActivity({
        type: "maintenance",
        description: `⚠️ فشل في إنشاء أمر صيانة: ${error instanceof Error ? error.message : String(error)}`,
      });
      throw error;
    }
  };`;

  fileContent = fileContent.replace(oldFunction, newFunction);
}

// تحديث وظيفة updateMaintenanceOrder
function updateUpdateMaintenanceOrder() {
  // البحث عن وظيفة updateMaintenanceOrder
  const funcStart = fileContent.indexOf('const updateMaintenanceOrder = ');
  if (funcStart === -1) {
    console.error('لم يتم العثور على وظيفة updateMaintenanceOrder');
    return;
  }

  // البحث عن نهاية الوظيفة
  let braceCount = 0;
  let funcEnd = funcStart;
  let foundStart = false;
  
  for (let i = funcStart; i < fileContent.length; i++) {
    const char = fileContent[i];
    if (char === '{') {
      braceCount++;
      foundStart = true;
    } else if (char === '}') {
      braceCount--;
      if (foundStart && braceCount === 0) {
        funcEnd = i + 1;
        break;
      }
    }
  }

  // التحقق من أننا وجدنا نهاية الوظيفة
  if (funcEnd === funcStart) {
    console.error('لم يتم العثور على نهاية وظيفة updateMaintenanceOrder');
    return;
  }

  // استبدال الوظيفة القديمة بالجديدة
  const oldFunction = fileContent.substring(funcStart, funcEnd);
  const newFunction = `const updateMaintenanceOrder = async (updatedOrder: MaintenanceOrder) => {
    try {
      // إرسال الأمر إلى API
      const response = await fetch('/api/maintenance-orders', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(updatedOrder),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update maintenance order');
      }

      // استقبال الأمر المحدّث من API
      const savedOrder = await response.json();

      // تحديث حالة التطبيق
      setMaintenanceOrders((prev) =>
        prev.map((o) =>
          o.id === savedOrder.id
            ? savedOrder
            : o
        ),
      );

      // تحديث حالة الأجهزة
      const originalOrder = maintenanceOrders.find(
        (o) => o.id === updatedOrder.id,
      );
      if (originalOrder) {
        const originalDeviceIds = new Set(originalOrder.items.map((i) => i.id));
        const updatedDeviceIds = new Set(updatedOrder.items.map((i) => i.id));

        const removedDeviceIds = [...originalDeviceIds].filter(
          (id) => !updatedDeviceIds.has(id),
        );
        setDevices((prev) =>
          prev.map((device) =>
            removedDeviceIds.includes(device.id)
              ? { ...device, status: "تحتاج صيانة" }
              : device,
          ),
        );
      }

      updatedOrder.items.forEach((item) => {
        updateDeviceStatus(item.id, "قيد الإصلاح");
      });

      // إضافة نشاط
      addActivity({
        type: "maintenance",
        description: `تم تحديث أمر الصيانة ${updatedOrder.orderNumber}`,
      });
      
      return savedOrder;
    } catch (error) {
      console.error('Failed to update maintenance order:', error);
      addActivity({
        type: "maintenance",
        description: `⚠️ فشل في تحديث أمر صيانة: ${error instanceof Error ? error.message : String(error)}`,
      });
      throw error;
    }
  };`;

  fileContent = fileContent.replace(oldFunction, newFunction);
}

// تحديث وظيفة deleteMaintenanceOrder
function updateDeleteMaintenanceOrder() {
  // البحث عن وظيفة deleteMaintenanceOrder
  const funcStart = fileContent.indexOf('const deleteMaintenanceOrder = ');
  if (funcStart === -1) {
    console.error('لم يتم العثور على وظيفة deleteMaintenanceOrder');
    return;
  }

  // البحث عن نهاية الوظيفة
  let braceCount = 0;
  let funcEnd = funcStart;
  let foundStart = false;
  
  for (let i = funcStart; i < fileContent.length; i++) {
    const char = fileContent[i];
    if (char === '{') {
      braceCount++;
      foundStart = true;
    } else if (char === '}') {
      braceCount--;
      if (foundStart && braceCount === 0) {
        funcEnd = i + 1;
        break;
      }
    }
  }

  // التحقق من أننا وجدنا نهاية الوظيفة
  if (funcEnd === funcStart) {
    console.error('لم يتم العثور على نهاية وظيفة deleteMaintenanceOrder');
    return;
  }

  // استبدال الوظيفة القديمة بالجديدة
  const oldFunction = fileContent.substring(funcStart, funcEnd);
  const newFunction = `const deleteMaintenanceOrder = async (orderId: number) => {
    try {
      const orderToDelete = maintenanceOrders.find((o) => o.id === orderId);
      if (!orderToDelete) return;

      const relationCheck = checkMaintenanceOrderRelations(orderId);
      if (!relationCheck.canDelete) {
        throw new Error(
          `لا يمكن حذف أمر الصيانة: ${relationCheck.reason}${relationCheck.relatedOperations ? "\nالعمليات المرتبطة: " + relationCheck.relatedOperations.join(", ") : ""}`,
        );
      }

      // إرسال طلب الحذف إلى API
      const response = await fetch('/api/maintenance-orders', {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ id: orderId }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete maintenance order');
      }

      // تحديث حالة التطبيق
      setMaintenanceOrders((prev) => prev.filter((o) => o.id !== orderId));

      // إضافة نشاط
      addActivity({
        type: "maintenance",
        description: `تم حذف أمر الصيانة ${orderToDelete.orderNumber}`,
      });
    } catch (error) {
      console.error('Failed to delete maintenance order:', error);
      addActivity({
        type: "maintenance",
        description: `⚠️ فشل في حذف أمر صيانة: ${error instanceof Error ? error.message : String(error)}`,
      });
      throw error;
    }
  };`;

  fileContent = fileContent.replace(oldFunction, newFunction);
}

// تحديث وظائف الصيانة
updateAddMaintenanceOrder();
updateUpdateMaintenanceOrder();
updateDeleteMaintenanceOrder();

// كتابة الملف بعد التحديث
console.log('كتابة الملف المحدّث...');
fs.writeFileSync(storeFilePath, fileContent, 'utf8');

console.log('تم تحديث وظائف الصيانة بنجاح!');