const { PrismaClient } = require('@prisma/client');
const fs = require('fs');
const path = require('path');

const prisma = new PrismaClient();

async function comprehensiveSystemAudit() {
  const auditResults = {
    timestamp: new Date().toISOString(),
    database: {
      connection: false,
      models: {},
      relationships: {},
      indexes: {},
      constraints: {},
      dataIntegrity: {}
    },
    performance: {
      queryTimes: {},
      dataVolume: {},
      indexUsage: {}
    },
    apis: {
      endpoints: {},
      responseTime: {},
      dataConsistency: {}
    },
    errors: [],
    warnings: [],
    recommendations: []
  };

  console.log('🔍 بدء الفحص الشامل للنظام...\n');

  try {
    // 1. فحص الاتصال بقاعدة البيانات
    console.log('📊 فحص الاتصال بقاعدة البيانات...');
    await prisma.$connect();
    auditResults.database.connection = true;
    console.log('✅ الاتصال بقاعدة البيانات: نجح\n');

    // 2. فحص النماذج والجداول
    console.log('🗄️ فحص النماذج والجداول...');
    const models = [
      { name: 'User', model: 'user', hasItems: false },
      { name: 'Warehouse', model: 'warehouse', hasItems: false },
      { name: 'Client', model: 'client', hasItems: false },
      { name: 'Supplier', model: 'supplier', hasItems: false },
      { name: 'Device', model: 'device', hasItems: false },
      { name: 'DeviceModel', model: 'deviceModel', hasItems: false },
      { name: 'SupplyOrder', model: 'supplyOrder', hasItems: true, itemsModel: 'supplyOrderItem' },
      { name: 'Sale', model: 'sale', hasItems: true, itemsModel: 'saleItem' },
      { name: 'Return', model: 'return', hasItems: true, itemsModel: 'returnItem' },
      { name: 'EvaluationOrder', model: 'evaluationOrder', hasItems: true, itemsModel: 'evaluationOrderItem' },
      { name: 'MaintenanceOrder', model: 'maintenanceOrder', hasItems: true, itemsModel: 'maintenanceOrderItem' },
      { name: 'DeliveryOrder', model: 'deliveryOrder', hasItems: true, itemsModel: 'deliveryOrderItem' },
      { name: 'MaintenanceReceiptOrder', model: 'maintenanceReceiptOrder', hasItems: true, itemsModel: 'maintenanceReceiptOrderItem' },
      { name: 'EmployeeRequest', model: 'employeeRequest', hasItems: false },
      { name: 'InternalMessage', model: 'internalMessage', hasItems: false },
      { name: 'AuditLog', model: 'auditLog', hasItems: false }
    ];

    for (const modelInfo of models) {
      try {
        const startTime = Date.now();
        const count = await prisma[modelInfo.model].count();
        const queryTime = Date.now() - startTime;
        
        auditResults.database.models[modelInfo.name] = {
          exists: true,
          count: count,
          queryTime: queryTime
        };
        
        console.log(`✅ ${modelInfo.name}: ${count} سجل (${queryTime}ms)`);
        
        // فحص العلاقات للنماذج التي تحتوي على عناصر
        if (modelInfo.hasItems) {
          const itemsStartTime = Date.now();
          const itemsCount = await prisma[modelInfo.itemsModel].count();
          const itemsQueryTime = Date.now() - itemsStartTime;
          
          auditResults.database.models[modelInfo.name + 'Items'] = {
            exists: true,
            count: itemsCount,
            queryTime: itemsQueryTime
          };
          
          console.log(`  └─ ${modelInfo.name}Items: ${itemsCount} عنصر (${itemsQueryTime}ms)`);
        }
        
      } catch (error) {
        auditResults.database.models[modelInfo.name] = {
          exists: false,
          error: error.message
        };
        auditResults.errors.push(`نموذج ${modelInfo.name}: ${error.message}`);
        console.log(`❌ ${modelInfo.name}: خطأ - ${error.message}`);
      }
    }

    // 3. فحص العلاقات والتكامل المرجعي
    console.log('\n🔗 فحص العلاقات والتكامل المرجعي...');
    
    // فحص العلاقات بين الجداول الرئيسية والعناصر
    const relationshipTests = [
      {
        name: 'SupplyOrder-SupplyOrderItem',
        parentModel: 'supplyOrder',
        childModel: 'supplyOrderItem',
        foreignKey: 'supplyOrderId'
      },
      {
        name: 'Sale-SaleItem',
        parentModel: 'sale',
        childModel: 'saleItem',
        foreignKey: 'saleId'
      },
      {
        name: 'Return-ReturnItem',
        parentModel: 'return',
        childModel: 'returnItem',
        foreignKey: 'returnId'
      }
    ];

    for (const test of relationshipTests) {
      try {
        // فحص وجود سجلات يتيمة (orphaned records)
        const orphanedItems = await prisma.$queryRaw`
          SELECT COUNT(*) as count 
          FROM ${test.childModel} ci 
          LEFT JOIN ${test.parentModel} p ON ci.${test.foreignKey} = p.id 
          WHERE p.id IS NULL
        `;
        
        const orphanedCount = parseInt(orphanedItems[0]?.count || 0);
        
        auditResults.database.relationships[test.name] = {
          orphanedRecords: orphanedCount,
          isValid: orphanedCount === 0
        };
        
        if (orphanedCount === 0) {
          console.log(`✅ ${test.name}: لا توجد سجلات يتيمة`);
        } else {
          console.log(`⚠️ ${test.name}: ${orphanedCount} سجل يتيم`);
          auditResults.warnings.push(`${test.name}: يحتوي على ${orphanedCount} سجل يتيم`);
        }
        
      } catch (error) {
        auditResults.errors.push(`فحص العلاقة ${test.name}: ${error.message}`);
        console.log(`❌ فحص العلاقة ${test.name}: ${error.message}`);
      }
    }

    // 4. فحص الأداء والاستعلامات المعقدة
    console.log('\n⚡ فحص الأداء والاستعلامات المعقدة...');
    
    const performanceTests = [
      {
        name: 'Complex Supply Query',
        query: async () => {
          const startTime = Date.now();
          const result = await prisma.supplyOrder.findMany({
            include: { items: true },
            take: 10,
            orderBy: { id: 'desc' }
          });
          return { time: Date.now() - startTime, count: result.length };
        }
      },
      {
        name: 'Complex Sale Query',
        query: async () => {
          const startTime = Date.now();
          const result = await prisma.sale.findMany({
            include: { items: true },
            take: 10,
            orderBy: { id: 'desc' }
          });
          return { time: Date.now() - startTime, count: result.length };
        }
      }
    ];

    for (const test of performanceTests) {
      try {
        const result = await test.query();
        auditResults.performance.queryTimes[test.name] = result.time;
        
        if (result.time < 100) {
          console.log(`✅ ${test.name}: ${result.time}ms (ممتاز)`);
        } else if (result.time < 500) {
          console.log(`⚠️ ${test.name}: ${result.time}ms (مقبول)`);
        } else {
          console.log(`❌ ${test.name}: ${result.time}ms (بطيء)`);
          auditResults.warnings.push(`${test.name}: بطيء (${result.time}ms)`);
        }
        
      } catch (error) {
        auditResults.errors.push(`اختبار الأداء ${test.name}: ${error.message}`);
        console.log(`❌ اختبار الأداء ${test.name}: ${error.message}`);
      }
    }

    console.log('\n📊 إنشاء التقرير النهائي...');
    
    // إنشاء التوصيات
    if (auditResults.errors.length === 0) {
      auditResults.recommendations.push('النظام يعمل بشكل صحيح');
    }
    
    if (auditResults.warnings.length > 0) {
      auditResults.recommendations.push('يوجد تحذيرات تحتاج للمراجعة');
    }
    
    // حفظ التقرير
    const reportPath = path.join(__dirname, 'system-audit-report.json');
    fs.writeFileSync(reportPath, JSON.stringify(auditResults, null, 2));
    
    console.log(`✅ تم حفظ التقرير في: ${reportPath}`);
    
    return auditResults;
    
  } catch (error) {
    auditResults.errors.push(`خطأ عام في الفحص: ${error.message}`);
    console.error('❌ خطأ في الفحص الشامل:', error);
    return auditResults;
  } finally {
    await prisma.$disconnect();
  }
}

// تشغيل الفحص
comprehensiveSystemAudit()
  .then(results => {
    console.log('\n🎯 ملخص النتائج:');
    console.log(`✅ النماذج الصحيحة: ${Object.keys(results.database.models).filter(m => results.database.models[m].exists).length}`);
    console.log(`❌ الأخطاء: ${results.errors.length}`);
    console.log(`⚠️ التحذيرات: ${results.warnings.length}`);
    console.log(`💡 التوصيات: ${results.recommendations.length}`);
  })
  .catch(console.error);
