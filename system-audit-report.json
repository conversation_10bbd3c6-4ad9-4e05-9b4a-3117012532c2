{"timestamp": "2025-08-01T00:22:25.150Z", "database": {"connection": true, "models": {"User": {"exists": true, "count": 2, "queryTime": 61}, "Warehouse": {"exists": true, "count": 1, "queryTime": 3}, "Client": {"exists": true, "count": 1, "queryTime": 3}, "Supplier": {"exists": true, "count": 1, "queryTime": 4}, "Device": {"exists": true, "count": 1, "queryTime": 3}, "DeviceModel": {"exists": true, "count": 1, "queryTime": 3}, "SupplyOrder": {"exists": true, "count": 1, "queryTime": 13}, "SupplyOrderItems": {"exists": true, "count": 1, "queryTime": 3}, "Sale": {"exists": true, "count": 1, "queryTime": 14}, "SaleItems": {"exists": true, "count": 1, "queryTime": 6}, "Return": {"exists": true, "count": 0, "queryTime": 5}, "ReturnItems": {"exists": true, "count": 0, "queryTime": 6}, "EvaluationOrder": {"exists": true, "count": 0, "queryTime": 13}, "EvaluationOrderItems": {"exists": true, "count": 0, "queryTime": 3}, "MaintenanceOrder": {"exists": true, "count": 0, "queryTime": 8}, "MaintenanceOrderItems": {"exists": true, "count": 0, "queryTime": 3}, "DeliveryOrder": {"exists": true, "count": 0, "queryTime": 3}, "DeliveryOrderItems": {"exists": true, "count": 0, "queryTime": 8}, "MaintenanceReceiptOrder": {"exists": true, "count": 0, "queryTime": 3}, "MaintenanceReceiptOrderItems": {"exists": true, "count": 0, "queryTime": 3}, "EmployeeRequest": {"exists": true, "count": 0, "queryTime": 3}, "InternalMessage": {"exists": true, "count": 0, "queryTime": 6}, "AuditLog": {"exists": true, "count": 6, "queryTime": 5}}, "relationships": {}, "indexes": {}, "constraints": {}, "dataIntegrity": {}}, "performance": {"queryTimes": {"Complex Supply Query": 6, "Complex Sale Query": 6}, "dataVolume": {}, "indexUsage": {}}, "apis": {"endpoints": {}, "responseTime": {}, "dataConsistency": {}}, "errors": ["فحص العلاقة SupplyOrder-SupplyOrderItem: \nInvalid `prisma.$queryRaw()` invocation:\n\n\nRaw query failed. Code: `42601`. Message: `ERROR: syntax error at or near \"$1\"`", "فحص العلاقة Sale-SaleItem: \nInvalid `prisma.$queryRaw()` invocation:\n\n\nRaw query failed. Code: `42601`. Message: `ERROR: syntax error at or near \"$1\"`", "فحص العلاقة Return-ReturnItem: \nInvalid `prisma.$queryRaw()` invocation:\n\n\nRaw query failed. Code: `42601`. Message: `ERROR: syntax error at or near \"$1\"`"], "warnings": [], "recommendations": []}