const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testMaintenanceTransferFix() {
  console.log('🔧 اختبار إصلاح صفحة maintenance-transfer...\n');

  try {
    // اختبار تحميل أوامر الصيانة مع العناصر
    console.log('📋 اختبار تحميل أوامر الصيانة...');
    const maintenanceOrders = await prisma.maintenanceOrder.findMany({
      include: { items: true },
      take: 3
    });
    
    console.log(`✅ تم تحميل ${maintenanceOrders.length} أوامر صيانة`);
    
    // التحقق من أن كل أمر له عناصر
    maintenanceOrders.forEach((order, index) => {
      const itemsCount = Array.isArray(order.items) ? order.items.length : 0;
      console.log(`   - الأمر ${index + 1}: ${order.orderNumber} - ${itemsCount} عنصر`);
    });

    // اختبار تحميل أوامر استلام الصيانة مع العناصر
    console.log('\n📥 اختبار تحميل أوامر استلام الصيانة...');
    const maintenanceReceipts = await prisma.maintenanceReceiptOrder.findMany({
      include: { items: true },
      take: 3
    });
    
    console.log(`✅ تم تحميل ${maintenanceReceipts.length} أوامر استلام صيانة`);
    
    // التحقق من أن كل أمر له عناصر
    maintenanceReceipts.forEach((receipt, index) => {
      const itemsCount = Array.isArray(receipt.items) ? receipt.items.length : 0;
      console.log(`   - الإيصال ${index + 1}: ${receipt.receiptNumber} - ${itemsCount} عنصر`);
    });

    // محاكاة العمليات التي كانت تسبب الأخطاء
    console.log('\n🧪 اختبار العمليات المُصلحة...');
    
    // اختبار فلترة الأوامر الصادرة
    const outgoingOrders = maintenanceOrders.filter(order => order.source === 'warehouse')
      .map(order => {
        const items = Array.isArray(order.items) ? order.items : [];
        const allItemsReceived = items.every(item => item.status !== 'بانتظار استلام في الصيانة');
        return {
          ...order,
          items,
          statusText: allItemsReceived ? 'تم الاستلام' : 'بانتظار الاستلام'
        };
      });
    
    console.log(`✅ تم معالجة ${outgoingOrders.length} أوامر صادرة بنجاح`);

    // اختبار ملخص التقدم
    const progressSummary = outgoingOrders.map(order => {
      const orderItems = Array.isArray(order.items) ? order.items : [];
      const returnedDevices = maintenanceReceipts
        .flatMap(receipt => Array.isArray(receipt.items) ? receipt.items.map(item => item.id) : [])
        .filter(deviceId => orderItems.some(orderItem => orderItem.id === deviceId));
      
      const returnedCount = new Set(returnedDevices).size;
      const remainingCount = orderItems.length - returnedCount;
      
      return {
        order,
        returnedCount,
        remainingCount,
        totalCount: orderItems.length
      };
    });

    console.log(`✅ تم إنشاء ملخص التقدم لـ ${progressSummary.length} أوامر`);
    
    progressSummary.forEach((summary, index) => {
      console.log(`   - الأمر ${index + 1}: ${summary.totalCount} إجمالي، ${summary.returnedCount} مُستلم، ${summary.remainingCount} متبقي`);
    });

    console.log('\n🎉 جميع الاختبارات نجحت! صفحة maintenance-transfer تعمل بشكل صحيح');
    return true;

  } catch (error) {
    console.error('❌ خطأ في الاختبار:', error);
    return false;
  } finally {
    await prisma.$disconnect();
  }
}

if (require.main === module) {
  testMaintenanceTransferFix()
    .then((success) => {
      process.exit(success ? 0 : 1);
    })
    .catch((error) => {
      console.error('💥 فشل الاختبار:', error);
      process.exit(1);
    });
}

module.exports = { testMaintenanceTransferFix };
