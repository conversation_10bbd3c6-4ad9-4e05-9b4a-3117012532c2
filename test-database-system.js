// اختبار شامل لنظام قواعد البيانات
// إنشاء bearer token بالصيغة المطلوبة: user:username:role
const userToken = Buffer.from('user:admin:admin').toString('base64');

async function testDatabaseSystem() {
  console.log('🧪 اختبار النظام الشامل لقواعد البيانات...\n');

  try {
    // 1. اختبار جلب الاتصالات
    console.log('1️⃣ اختبار جلب الاتصالات...');
    const connectionsResponse = await fetch('http://localhost:9005/api/database/connections', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${userToken}`,
        'Content-Type': 'application/json'
      }
    });
    console.log(`   GET /api/database/connections: ${connectionsResponse.status}`);
    
    let connections = [];
    if (connectionsResponse.ok) {
      const data = await connectionsResponse.json();
      connections = data.connections || [];
      console.log(`   ✅ تم العثور على ${connections.length} اتصال`);
      if (connections.length > 0) {
        console.log(`   الاتصال الافتراضي: ${connections.find(c => c.isDefault)?.name || 'غير محدد'}`);
      }
    } else {
      console.log(`   ❌ فشل: ${await connectionsResponse.text()}`);
      return;
    }

    // 2. اختبار جلب النسخ الاحتياطية
    console.log('\n2️⃣ اختبار جلب النسخ الاحتياطية...');
    const backupsResponse = await fetch('http://localhost:9005/api/database/backup', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${userToken}`,
        'Content-Type': 'application/json'
      }
    });
    console.log(`   GET /api/database/backup: ${backupsResponse.status}`);
    
    if (backupsResponse.ok) {
      const backupData = await backupsResponse.json();
      const backups = backupData.backups || [];
      console.log(`   ✅ تم العثور على ${backups.length} نسخة احتياطية`);
    } else {
      console.log(`   ❌ فشل: ${await backupsResponse.text()}`);
    }

    // 3. اختبار إنشاء نسخة احتياطية (إذا وجد اتصال)
    if (connections.length > 0) {
      console.log('\n3️⃣ اختبار إنشاء نسخة احتياطية...');
      const defaultConnection = connections.find(c => c.isDefault) || connections[0];
      
      const backupResponse = await fetch('http://localhost:9005/api/database/backup', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${userToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          connectionId: defaultConnection.id,
          name: `نسخة تجريبية - ${new Date().toLocaleString('ar-SA')}`,
          description: 'نسخة احتياطية للاختبار',
          createdBy: 'admin'
        })
      });
      
      console.log(`   POST /api/database/backup: ${backupResponse.status}`);
      if (backupResponse.ok) {
        const result = await backupResponse.json();
        console.log(`   ✅ تم إنشاء النسخة الاحتياطية بنجاح`);
      } else {
        const error = await backupResponse.text();
        console.log(`   ❌ فشل: ${error}`);
      }
    }

    // 4. اختبار إضافة اتصال جديد
    console.log('\n4️⃣ اختبار إضافة اتصال جديد...');
    const newConnection = {
      name: `اتصال تجريبي - ${Date.now()}`,
      host: 'localhost',
      port: 5432,
      database: 'test_db',
      username: 'test_user',
      password: 'test_pass',
      isDefault: false
    };

    const addConnectionResponse = await fetch('http://localhost:9005/api/database/connections', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${userToken}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(newConnection)
    });
    
    console.log(`   POST /api/database/connections: ${addConnectionResponse.status}`);
    if (addConnectionResponse.ok) {
      const result = await addConnectionResponse.json();
      console.log(`   ✅ تم إضافة الاتصال بنجاح: ${result.connection?.name}`);
    } else {
      const error = await addConnectionResponse.text();
      console.log(`   ❌ فشل: ${error}`);
    }

    console.log('\n🎉 انتهى الاختبار الشامل!');

  } catch (error) {
    console.error('❌ خطأ في الاختبار:', error.message);
  }
}

testDatabaseSystem();
