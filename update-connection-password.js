const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function updateConnectionPassword() {
  try {
    console.log('🔄 تحديث كلمة مرور الاتصال الافتراضي...');
    
    const updated = await prisma.databaseConnection.updateMany({
      where: { isDefault: true },
      data: {
        database: 'deviceflow_db', // تصحيح اسم قاعدة البيانات
        password: 'om772828'       // كلمة المرور الأصلية
      }
    });

    console.log('✅ تم تحديث', updated.count, 'اتصال');
    
    // التحقق من التحديث
    const conn = await prisma.databaseConnection.findFirst({
      where: { isDefault: true }
    });
    
    console.log('الاتصال المحدث:', {
      name: conn?.name,
      database: conn?.database,
      password: conn?.password?.substring(0, 5) + '...'
    });

  } catch (error) {
    console.error('❌ خطأ في التحديث:', error);
  } finally {
    await prisma.$disconnect();
  }
}

updateConnectionPassword();
