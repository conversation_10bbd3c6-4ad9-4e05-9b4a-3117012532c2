const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testApiEndpoints() {
  console.log('🧪 اختبار جميع API endpoints...\n');

  const tests = [
    {
      name: 'Supply Orders',
      description: 'أوامر التوريد',
      test: async () => {
        const orders = await prisma.supplyOrder.findMany({
          include: { items: true },
          take: 1
        });
        return { count: orders.length, hasItems: orders.length > 0 && orders[0].items !== undefined };
      }
    },
    {
      name: 'Sales',
      description: 'المبيعات',
      test: async () => {
        const sales = await prisma.sale.findMany({
          include: { items: true },
          take: 1
        });
        return { count: sales.length, hasItems: sales.length > 0 && sales[0].items !== undefined };
      }
    },
    {
      name: 'Returns',
      description: 'المرتجعات',
      test: async () => {
        const returns = await prisma.return.findMany({
          include: { items: true },
          take: 1
        });
        return { count: returns.length, hasItems: returns.length > 0 && returns[0].items !== undefined };
      }
    },
    {
      name: 'Evaluations',
      description: 'أوامر التقييم',
      test: async () => {
        const evaluations = await prisma.evaluationOrder.findMany({
          include: { items: true },
          take: 1
        });
        return { count: evaluations.length, hasItems: evaluations.length > 0 && evaluations[0].items !== undefined };
      }
    },
    {
      name: 'Maintenance Orders',
      description: 'أوامر الصيانة',
      test: async () => {
        const orders = await prisma.maintenanceOrder.findMany({
          include: { items: true },
          take: 1
        });
        return { count: orders.length, hasItems: orders.length > 0 && orders[0].items !== undefined };
      }
    },
    {
      name: 'Delivery Orders',
      description: 'أوامر التسليم',
      test: async () => {
        const orders = await prisma.deliveryOrder.findMany({
          include: { items: true },
          take: 1
        });
        return { count: orders.length, hasItems: orders.length > 0 && orders[0].items !== undefined };
      }
    },
    {
      name: 'Maintenance Receipts',
      description: 'أوامر استلام الصيانة',
      test: async () => {
        const receipts = await prisma.maintenanceReceiptOrder.findMany({
          include: { items: true },
          take: 1
        });
        return { count: receipts.length, hasItems: receipts.length > 0 && receipts[0].items !== undefined };
      }
    }
  ];

  let allPassed = true;

  for (const test of tests) {
    try {
      const result = await test.test();
      const status = result.hasItems ? '✅' : (result.count > 0 ? '⚠️' : '✅');
      console.log(`${status} ${test.name} (${test.description}): ${result.count} سجل، العلاقات: ${result.hasItems ? 'تعمل' : 'غير مختبرة'}`);
      
      if (result.count > 0 && !result.hasItems) {
        allPassed = false;
      }
    } catch (error) {
      console.log(`❌ ${test.name} (${test.description}): خطأ - ${error.message}`);
      allPassed = false;
    }
  }

  // اختبار إنشاء سجل جديد
  console.log('\n🆕 اختبار إنشاء سجل جديد...');
  try {
    const testOrder = await prisma.supplyOrder.create({
      data: {
        supplyOrderId: `API-TEST-${Date.now()}`,
        supplierId: 1,
        supplyDate: new Date().toISOString(),
        warehouseId: 1,
        employeeName: 'اختبار API',
        notes: 'اختبار API endpoints',
        items: {
          create: [
            {
              imei: `API-TEST-${Date.now()}`,
              model: 'Test Model',
              manufacturer: 'Test Manufacturer',
              condition: 'جديد'
            }
          ]
        }
      },
      include: { items: true }
    });

    console.log(`✅ تم إنشاء أمر توريد اختباري: ${testOrder.supplyOrderId} مع ${testOrder.items.length} عنصر`);

    // حذف السجل الاختباري
    await prisma.supplyOrderItem.deleteMany({
      where: { supplyOrderId: testOrder.id }
    });
    await prisma.supplyOrder.delete({
      where: { id: testOrder.id }
    });
    console.log(`✅ تم حذف السجل الاختباري`);

  } catch (error) {
    console.log(`❌ فشل في اختبار الإنشاء: ${error.message}`);
    allPassed = false;
  }

  if (allPassed) {
    console.log('\n🎉 جميع API endpoints تعمل بشكل صحيح!');
    console.log('✅ النظام جاهز للاستخدام مع الهيكل الجديد');
  } else {
    console.log('\n⚠️ هناك بعض المشاكل في API endpoints');
  }

  await prisma.$disconnect();
  return allPassed;
}

if (require.main === module) {
  testApiEndpoints()
    .then((success) => {
      process.exit(success ? 0 : 1);
    })
    .catch((error) => {
      console.error('💥 خطأ في اختبار APIs:', error);
      process.exit(1);
    });
}

module.exports = { testApiEndpoints };
