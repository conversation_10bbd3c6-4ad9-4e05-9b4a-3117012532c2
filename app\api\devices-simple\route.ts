import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

export async function GET(request: NextRequest) {
  try {
    const devices = await prisma.device.findMany({
      orderBy: { id: 'asc' }
    });
    
    return NextResponse.json(devices);
  } catch (error) {
    console.error('Failed to fetch devices:', error);
    return NextResponse.json({ error: 'Failed to fetch devices' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const newDevice = await request.json();

    // Basic validation
    if (!newDevice.id || !newDevice.model) {
      return NextResponse.json(
        { message: 'Device ID and model are required' },
        { status: 400 }
      );
    }

    // Check if device already exists
    const existingDevice = await prisma.device.findUnique({
      where: { id: newDevice.id }
    });

    if (existingDevice) {
      return NextResponse.json(
        { message: 'Device already exists' },
        { status: 409 }
      );
    }

    const device = await prisma.device.create({
      data: {
        id: newDevice.id,
        model: newDevice.model,
        status: newDevice.status || 'متاح للبيع',
        storage: newDevice.storage || '',
        price: newDevice.price || 0,
        condition: newDevice.condition || 'جديد',
        warehouseId: newDevice.warehouseId || null,
        supplierId: newDevice.supplierId || null,
        replacementInfo: newDevice.replacementInfo || null
      }
    });

    return NextResponse.json(device, { status: 201 });
  } catch (error) {
    console.error('Failed to create device:', error);
    return NextResponse.json({ error: 'Failed to create device' }, { status: 500 });
  }
}

export async function PUT(request: NextRequest) {
  try {
    const updatedDevice = await request.json();

    if (!updatedDevice.id) {
      return NextResponse.json(
        { message: 'Device ID is required' },
        { status: 400 }
      );
    }

    const device = await prisma.device.update({
      where: { id: updatedDevice.id },
      data: {
        ...(updatedDevice.model && { model: updatedDevice.model }),
        ...(updatedDevice.status && { status: updatedDevice.status }),
        ...(updatedDevice.storage !== undefined && { storage: updatedDevice.storage }),
        ...(updatedDevice.price !== undefined && { price: updatedDevice.price }),
        ...(updatedDevice.condition && { condition: updatedDevice.condition }),
        ...(updatedDevice.warehouseId !== undefined && { warehouseId: updatedDevice.warehouseId }),
        ...(updatedDevice.supplierId !== undefined && { supplierId: updatedDevice.supplierId }),
        ...(updatedDevice.replacementInfo !== undefined && { replacementInfo: updatedDevice.replacementInfo })
      }
    });

    return NextResponse.json(device);
  } catch (error) {
    console.error('Failed to update device:', error);
    return NextResponse.json({ error: 'Failed to update device' }, { status: 500 });
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { id } = await request.json();

    if (!id) {
      return NextResponse.json(
        { message: 'Device ID is required' },
        { status: 400 }
      );
    }

    await prisma.device.delete({
      where: { id }
    });

    return NextResponse.json({ message: 'Device deleted successfully' });
  } catch (error) {
    console.error('Failed to delete device:', error);
    return NextResponse.json({ error: 'Failed to delete device' }, { status: 500 });
  }
}
