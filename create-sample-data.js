const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function createSampleData() {
  try {
    console.log('🔄 إنشاء بيانات تجريبية...\n');

    // إنشاء طلبات موظفين تجريبية
    console.log('📝 إنشاء طلبات موظفين...');
    
    const sampleRequests = [
      {
        requestNumber: 'REQ-001',
        requestType: 'إجازة',
        priority: 'عادي',
        notes: 'طلب إجازة لمدة أسبوع',
        employeeName: 'أحمد محمد',
        employeeId: 1,
        status: 'قيد المراجعة',
        requestDate: new Date().toISOString()
      },
      {
        requestNumber: 'REQ-002',
        requestType: 'صيانة',
        priority: 'طاريء',
        notes: 'طلب صيانة عاجلة للجهاز',
        employeeName: 'فاطمة علي',
        employeeId: 2,
        status: 'قيد المراجعة',
        requestDate: new Date().toISOString()
      },
      {
        requestNumber: 'REQ-003',
        requestType: 'تحويل مخزني',
        priority: 'عادي',
        notes: 'طلب تحويل أجهزة بين المخازن',
        employeeName: 'محمد أحمد',
        employeeId: 3,
        status: 'تم التنفيذ',
        requestDate: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
        adminNotes: 'تم التنفيذ بنجاح'
      }
    ];

    for (const request of sampleRequests) {
      try {
        const existingRequest = await prisma.employeeRequest.findFirst({
          where: { requestNumber: request.requestNumber }
        });

        if (!existingRequest) {
          await prisma.employeeRequest.create({ data: request });
          console.log(`✅ تم إنشاء طلب: ${request.requestNumber}`);
        } else {
          console.log(`⏭️ الطلب ${request.requestNumber} موجود بالفعل`);
        }
      } catch (error) {
        console.error(`❌ خطأ في إنشاء الطلب ${request.requestNumber}:`, error.message);
      }
    }

    // إنشاء أجهزة إضافية
    console.log('\n📱 إنشاء أجهزة إضافية...');
    
    const sampleDevices = [
      {
        id: '111111111111111',
        model: 'Apple iPhone 14 128GB',
        status: 'متاح للبيع',
        condition: 'جديد',
        warehouseId: 1,
        storage: '128GB',
        price: 1000
      },
      {
        id: '333333333333333',
        model: 'Samsung Galaxy S23 256GB',
        status: 'متاح للبيع',
        condition: 'جديد',
        warehouseId: 1,
        storage: '256GB',
        price: 900
      },
      {
        id: '444444444444444',
        model: 'Apple iPhone 13 128GB',
        status: 'مباع',
        condition: 'مستخدم',
        warehouseId: 1,
        storage: '128GB',
        price: 750
      },
      {
        id: '555555555555555',
        model: 'Google Pixel 7 128GB',
        status: 'تحتاج صيانة',
        condition: 'مستخدم',
        warehouseId: 1,
        storage: '128GB',
        price: 650
      }
    ];

    for (const device of sampleDevices) {
      try {
        const existingDevice = await prisma.device.findUnique({
          where: { id: device.id }
        });

        if (!existingDevice) {
          await prisma.device.create({ data: device });
          console.log(`✅ تم إنشاء جهاز: ${device.id} - ${device.model}`);
        } else {
          console.log(`⏭️ الجهاز ${device.id} موجود بالفعل`);
        }
      } catch (error) {
        console.error(`❌ خطأ في إنشاء الجهاز ${device.id}:`, error.message);
      }
    }

    // إنشاء موديلات إضافية
    console.log('\n📋 إنشاء موديلات إضافية...');
    
    const sampleModels = [
      {
        name: 'iPhone 14 128GB',
        manufacturerId: BigInt(1), // Apple
        category: 'هاتف ذكي'
      },
      {
        name: 'Galaxy S23 256GB',
        manufacturerId: BigInt(2), // Samsung
        category: 'هاتف ذكي'
      },
      {
        name: 'iPhone 13 128GB',
        manufacturerId: BigInt(1), // Apple
        category: 'هاتف ذكي'
      },
      {
        name: 'Pixel 7 128GB',
        manufacturerId: BigInt(3), // Google
        category: 'هاتف ذكي'
      }
    ];

    for (const model of sampleModels) {
      try {
        const existingModel = await prisma.deviceModel.findFirst({
          where: { 
            name: model.name,
            manufacturerId: model.manufacturerId
          }
        });

        if (!existingModel) {
          await prisma.deviceModel.create({ data: model });
          console.log(`✅ تم إنشاء موديل: ${model.name}`);
        } else {
          console.log(`⏭️ الموديل ${model.name} موجود بالفعل`);
        }
      } catch (error) {
        console.error(`❌ خطأ في إنشاء الموديل ${model.name}:`, error.message);
      }
    }

    // التحقق من النتائج النهائية
    console.log('\n📊 النتائج النهائية:');
    const totalRequests = await prisma.employeeRequest.count();
    const totalDevices = await prisma.device.count();
    const totalModels = await prisma.deviceModel.count();
    
    console.log(`📝 إجمالي طلبات الموظفين: ${totalRequests}`);
    console.log(`📱 إجمالي الأجهزة: ${totalDevices}`);
    console.log(`📋 إجمالي الموديلات: ${totalModels}`);

  } catch (error) {
    console.error('❌ خطأ في إنشاء البيانات التجريبية:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createSampleData();
