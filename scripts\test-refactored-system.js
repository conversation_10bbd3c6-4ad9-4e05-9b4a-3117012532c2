const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testRefactoredSystem() {
  console.log('🧪 بدء اختبار النظام المُعاد هيكلته...\n');

  try {
    // 1. اختبار SupplyOrder
    console.log('📦 اختبار أوامر التوريد...');
    const supplyOrders = await prisma.supplyOrder.findMany({
      include: { items: true },
      take: 3
    });
    
    console.log(`✅ تم العثور على ${supplyOrders.length} أوامر توريد`);
    if (supplyOrders.length > 0) {
      const firstOrder = supplyOrders[0];
      console.log(`   - الأمر الأول: ${firstOrder.supplyOrderId} يحتوي على ${firstOrder.items.length} عنصر`);
      if (firstOrder.items.length > 0) {
        console.log(`   - العنصر الأول: ${firstOrder.items[0].manufacturer} ${firstOrder.items[0].model}`);
      }
    }

    // 2. اختبار Sale
    console.log('\n💰 اختبار المبيعات...');
    const sales = await prisma.sale.findMany({
      include: { items: true },
      take: 3
    });
    
    console.log(`✅ تم العثور على ${sales.length} مبيعات`);
    if (sales.length > 0) {
      const firstSale = sales[0];
      console.log(`   - المبيعة الأولى: ${firstSale.soNumber} تحتوي على ${firstSale.items.length} عنصر`);
      if (firstSale.items.length > 0) {
        console.log(`   - العنصر الأول: ${firstSale.items[0].model} بسعر ${firstSale.items[0].price}`);
      }
    }

    // 3. اختبار Return
    console.log('\n↩️ اختبار المرتجعات...');
    const returns = await prisma.return.findMany({
      include: { items: true },
      take: 3
    });
    
    console.log(`✅ تم العثور على ${returns.length} مرتجعات`);
    if (returns.length > 0) {
      const firstReturn = returns[0];
      console.log(`   - المرتجع الأول: ${firstReturn.roNumber} يحتوي على ${firstReturn.items.length} عنصر`);
      if (firstReturn.items.length > 0) {
        console.log(`   - العنصر الأول: ${firstReturn.items[0].model} سبب الإرجاع: ${firstReturn.items[0].returnReason}`);
      }
    }

    // 4. اختبار EvaluationOrder
    console.log('\n📊 اختبار أوامر التقييم...');
    const evaluations = await prisma.evaluationOrder.findMany({
      include: { items: true },
      take: 3
    });
    
    console.log(`✅ تم العثور على ${evaluations.length} أوامر تقييم`);
    if (evaluations.length > 0) {
      const firstEval = evaluations[0];
      console.log(`   - التقييم الأول: ${firstEval.orderId} يحتوي على ${firstEval.items.length} عنصر`);
      if (firstEval.items.length > 0) {
        console.log(`   - العنصر الأول: ${firstEval.items[0].model} التقييم النهائي: ${firstEval.items[0].finalGrade}`);
      }
    }

    // 5. اختبار MaintenanceOrder
    console.log('\n🔧 اختبار أوامر الصيانة...');
    const maintenanceOrders = await prisma.maintenanceOrder.findMany({
      include: { items: true },
      take: 3
    });
    
    console.log(`✅ تم العثور على ${maintenanceOrders.length} أوامر صيانة`);
    if (maintenanceOrders.length > 0) {
      const firstMaintenance = maintenanceOrders[0];
      console.log(`   - أمر الصيانة الأول: ${firstMaintenance.orderNumber} يحتوي على ${firstMaintenance.items.length} عنصر`);
      if (firstMaintenance.items.length > 0) {
        console.log(`   - العنصر الأول: ${firstMaintenance.items[0].model} العطل: ${firstMaintenance.items[0].fault || 'غير محدد'}`);
      }
    }

    // 6. اختبار DeliveryOrder
    console.log('\n🚚 اختبار أوامر التسليم...');
    const deliveryOrders = await prisma.deliveryOrder.findMany({
      include: { items: true },
      take: 3
    });
    
    console.log(`✅ تم العثور على ${deliveryOrders.length} أوامر تسليم`);
    if (deliveryOrders.length > 0) {
      const firstDelivery = deliveryOrders[0];
      console.log(`   - أمر التسليم الأول: ${firstDelivery.deliveryOrderNumber} يحتوي على ${firstDelivery.items.length} عنصر`);
      if (firstDelivery.items.length > 0) {
        console.log(`   - العنصر الأول: ${firstDelivery.items[0].model} النتيجة: ${firstDelivery.items[0].result}`);
      }
    }

    // 7. اختبار MaintenanceReceiptOrder
    console.log('\n📋 اختبار أوامر استلام الصيانة...');
    const maintenanceReceipts = await prisma.maintenanceReceiptOrder.findMany({
      include: { items: true },
      take: 3
    });
    
    console.log(`✅ تم العثور على ${maintenanceReceipts.length} أوامر استلام صيانة`);
    if (maintenanceReceipts.length > 0) {
      const firstReceipt = maintenanceReceipts[0];
      console.log(`   - أمر الاستلام الأول: ${firstReceipt.receiptNumber} يحتوي على ${firstReceipt.items.length} عنصر`);
      if (firstReceipt.items.length > 0) {
        console.log(`   - العنصر الأول: ${firstReceipt.items[0].model} النتيجة: ${firstReceipt.items[0].result}`);
      }
    }

    // 8. اختبار إنشاء أمر توريد جديد
    console.log('\n🆕 اختبار إنشاء أمر توريد جديد...');
    const testSupplyOrder = await prisma.supplyOrder.create({
      data: {
        supplyOrderId: `TEST-${Date.now()}`,
        supplierId: 1,
        supplyDate: new Date().toISOString(),
        warehouseId: 1,
        employeeName: 'اختبار النظام',
        notes: 'اختبار النظام المُعاد هيكلته',
        items: {
          create: [
            {
              imei: `TEST${Date.now()}`,
              model: 'iPhone Test',
              manufacturer: 'Apple',
              condition: 'جديد'
            }
          ]
        }
      },
      include: { items: true }
    });

    console.log(`✅ تم إنشاء أمر توريد اختباري: ${testSupplyOrder.supplyOrderId}`);
    console.log(`   - يحتوي على ${testSupplyOrder.items.length} عنصر`);

    // حذف الأمر الاختباري
    await prisma.supplyOrderItem.deleteMany({
      where: { supplyOrderId: testSupplyOrder.id }
    });
    await prisma.supplyOrder.delete({
      where: { id: testSupplyOrder.id }
    });
    console.log(`✅ تم حذف الأمر الاختباري`);

    console.log('\n🎉 جميع الاختبارات نجحت! النظام يعمل بشكل صحيح مع الهيكل الجديد.');

  } catch (error) {
    console.error('❌ خطأ في الاختبار:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// تشغيل الاختبار
if (require.main === module) {
  testRefactoredSystem()
    .then(() => {
      console.log('\n✨ انتهت الاختبارات بنجاح!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 فشلت الاختبارات:', error);
      process.exit(1);
    });
}

module.exports = { testRefactoredSystem };
