const fs = require('fs');
const path = require('path');
const glob = require('glob');

function fixRemainingSyntaxIssues() {
  console.log('🔧 إصلاح المشاكل المتبقية في الصيغة...\n');

  // البحث عن جميع ملفات TypeScript/JavaScript
  const files = glob.sync('app/**/*.{ts,tsx}', { 
    ignore: ['node_modules/**', 'dist/**', '.next/**'] 
  });

  console.log(`📁 فحص ${files.length} ملف...\n`);

  let totalFixed = 0;
  let filesFixed = 0;

  files.forEach(filePath => {
    try {
      let content = fs.readFileSync(filePath, 'utf8');
      let hasChanges = false;
      const fileName = path.relative('.', filePath);
      const originalContent = content;

      // 1. إصلاح استخدام order.items بدلاً من المتغير الصحيح
      content = content.replace(/\(Array\.isArray\(([^)]+)\.items\)\s*\?\s*order\.items\.length\s*:\s*0\)/g, 
        '(Array.isArray($1.items) ? $1.items.length : 0)');

      // 2. إصلاح التعبيرات الشرطية المعطلة
      content = content.replace(/if\s*\(\s*\(Array\.isArray\([^)]+\)\s*\?\s*[^:]+:\s*0\)\s*===\s*0\)\s*{/g, 
        (match) => {
          // إصلاح الأقواس في التعبير الشرطي
          return match.replace(/\?\s*([^:]+):\s*0\)\s*===\s*0/, '? $1 : 0) === 0');
        });

      // 3. إصلاح if statements مع return
      content = content.replace(/if\s*\(([^)]+)\)\s+return\s*;/g, 'if ($1) return;');
      content = content.replace(/if\s*\(([^)]+)\)\s+return\s+([^;]+);/g, 'if ($1) return $2;');

      // 4. إصلاح permissions غير معرف
      if (content.includes('permissions') && !content.includes('const permissions =')) {
        // البحث عن مكان مناسب لإضافة التعريف
        const useEffectMatch = content.match(/useEffect\(\(\) => \{[\s\S]*?\}, \[\]\);/);
        if (useEffectMatch) {
          content = content.replace(
            useEffectMatch[0],
            useEffectMatch[0] + '\n\n  // Temporary permissions (remove when auth system is implemented)\n  const permissions = { create: true, edit: true, delete: true, view: true, viewAll: true };'
          );
          hasChanges = true;
        }
      }

      // 5. إصلاح currentUser غير معرف
      if (content.includes('currentUser') && !content.includes('useStore') && !content.includes('const { currentUser }')) {
        // إضافة import useStore
        if (!content.includes('useStore')) {
          content = content.replace(
            /import.*from.*@\/hooks\/use-toast.*/,
            `$&\nimport { useStore } from '@/context/store';`
          );
        }

        // إضافة currentUser من useStore
        const permissionsLine = content.match(/const permissions = .*/);
        if (permissionsLine) {
          content = content.replace(
            permissionsLine[0],
            `  // Get currentUser from store\n  const { currentUser } = useStore();\n\n${permissionsLine[0]}`
          );
          hasChanges = true;
        }
      }

      // 6. إصلاح استخدام .items غير الآمن
      const unsafeItemsPattern = /(\w+)\.items\.(map|filter|find|some|every|length)/g;
      content = content.replace(unsafeItemsPattern, (match, varName, method) => {
        // التحقق من وجود فحص Array.isArray قبل الاستخدام
        const beforeMatch = content.substring(Math.max(0, content.indexOf(match) - 100), content.indexOf(match));
        if (!beforeMatch.includes('Array.isArray') && !beforeMatch.includes('?.')) {
          return `(Array.isArray(${varName}.items) ? ${varName}.items.${method} : [].${method})`;
        }
        return match;
      });

      // 7. إصلاح أقواس غير متطابقة
      const openParens = (content.match(/\(/g) || []).length;
      const closeParens = (content.match(/\)/g) || []).length;
      if (openParens > closeParens) {
        // إضافة أقواس إغلاق مفقودة في نهاية الدوال
        content = content.replace(/}\s*$/, '})');
      }

      if (content !== originalContent) {
        hasChanges = true;
      }

      if (hasChanges) {
        fs.writeFileSync(filePath, content);
        console.log(`✅ ${fileName}: تم إصلاح المشاكل`);
        filesFixed++;
        totalFixed++;
      } else {
        // فحص للمشاكل المحتملة
        const issues = [];
        
        if (content.includes('order.items') && !fileName.includes('order')) {
          issues.push('استخدام order.items في سياق خاطئ');
        }
        
        if (content.includes('permissions') && !content.includes('const permissions =')) {
          issues.push('permissions غير معرف');
        }

        if (issues.length > 0) {
          console.log(`⚠️ ${fileName}: ${issues.join(', ')}`);
        }
      }

    } catch (error) {
      console.log(`❌ خطأ في معالجة ${filePath}: ${error.message}`);
    }
  });

  console.log(`\n📊 ملخص الإصلاح:`);
  console.log(`   - إجمالي الملفات المفحوصة: ${files.length}`);
  console.log(`   - ملفات تم إصلاحها: ${filesFixed}`);
  console.log(`   - إجمالي المشاكل المُصلحة: ${totalFixed}`);

  if (totalFixed > 0) {
    console.log(`\n🎉 تم إصلاح ${totalFixed} مشكلة في ${filesFixed} ملف!`);
    return true;
  } else {
    console.log(`\nℹ️ لا توجد مشاكل إضافية تحتاج إصلاح`);
    return true;
  }
}

// دالة لفحص الملفات بحثاً عن مشاكل محددة
function checkSpecificIssues() {
  console.log('\n🔍 فحص المشاكل المحددة...\n');

  const problematicPatterns = [
    {
      name: 'استخدام order.items في سياق خاطئ',
      pattern: /order\.items/g,
      files: ['sales', 'supply', 'returns']
    },
    {
      name: 'permissions غير معرف',
      pattern: /permissions\?\./g,
      exclude: /const permissions =/
    },
    {
      name: 'currentUser غير معرف',
      pattern: /currentUser\./g,
      exclude: /useStore|const.*currentUser/
    },
    {
      name: 'استخدام .items غير آمن',
      pattern: /\w+\.items\.(map|filter|find)/g,
      exclude: /Array\.isArray/
    }
  ];

  const files = glob.sync('app/(main)/*/page.tsx');
  
  files.forEach(filePath => {
    const content = fs.readFileSync(filePath, 'utf8');
    const fileName = path.basename(path.dirname(filePath));
    
    problematicPatterns.forEach(pattern => {
      const matches = content.match(pattern.pattern);
      if (matches && (!pattern.exclude || !pattern.exclude.test(content))) {
        console.log(`⚠️ ${fileName}: ${pattern.name} (${matches.length} مرة)`);
      }
    });
  });
}

if (require.main === module) {
  const success = fixRemainingSyntaxIssues();
  checkSpecificIssues();
  process.exit(success ? 0 : 1);
}

module.exports = { fixRemainingSyntaxIssues, checkSpecificIssues };
