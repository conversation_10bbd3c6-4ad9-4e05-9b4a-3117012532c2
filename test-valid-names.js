// اختبار سريع للأسماء الصالحة
const userToken = Buffer.from('user:admin:admin').toString('base64');

async function testValidNames() {
  console.log('🧪 اختبار الأسماء الصالحة بعد إصلاح الصلاحيات...\n');

  try {
    // جلب الاتصال الافتراضي
    const connectionsResponse = await fetch('http://localhost:9005/api/database/connections', {
      headers: { 'Authorization': `Bearer ${userToken}` }
    });
    const { connections } = await connectionsResponse.json();
    const defaultConnection = connections.find(c => c.isDefault);

    // اختبار اسم صالح
    const testName = `valid_test_${Date.now()}`;
    console.log(`📝 اختبار إنشاء: ${testName}`);
    
    const createResponse = await fetch('http://localhost:9005/api/database/create', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${userToken}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        connectionId: defaultConnection.id,
        name: testName,
        owner: 'deviceflow_user'
      })
    });

    console.log(`الحالة: ${createResponse.status}`);
    
    if (createResponse.ok) {
      const result = await createResponse.json();
      console.log(`✅ نجح: ${result.message}`);
    } else {
      const error = await createResponse.json();
      console.log(`❌ فشل: ${error.error}`);
    }

  } catch (error) {
    console.error('خطأ:', error.message);
  }
}

testValidNames();
