import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

export async function GET(request: NextRequest) {
  try {
    const supplyOrders = await prisma.supplyOrder.findMany({
      include: {
        items: true
      },
      orderBy: { id: 'desc' }
    });

    return NextResponse.json(supplyOrders);
  } catch (error) {
    console.error('Failed to fetch supply orders:', error);
    return NextResponse.json({ error: 'Failed to fetch supply orders' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const newSupplyOrder = await request.json();

    // Basic validation
    if (!newSupplyOrder.supplierId || !newSupplyOrder.items || newSupplyOrder.items.length === 0) {
      return NextResponse.json(
        { error: 'Supplier ID and items are required' },
        { status: 400 }
      );
    }

    const supplyOrder = await prisma.supplyOrder.create({
      data: {
        orderId: newSupplyOrder.orderId,
        supplierId: newSupplyOrder.supplierId,
        warehouseId: newSupplyOrder.warehouseId || null,
        totalAmount: newSupplyOrder.totalAmount || 0,
        notes: newSupplyOrder.notes || null,
        supplyDate: new Date(newSupplyOrder.supplyDate || Date.now()),
        employeeName: newSupplyOrder.employeeName || 'غير محدد',
        items: {
          create: newSupplyOrder.items.map((item: any) => ({
            imei: item.imei,
            model: item.model,
            manufacturer: item.manufacturer || '',
            condition: item.condition || 'جديد',
            purchasePrice: item.purchasePrice || 0,
            sellingPrice: item.sellingPrice || 0,
            notes: item.notes || null
          }))
        }
      },
      include: {
        items: true
      }
    });

    return NextResponse.json(supplyOrder, { status: 201 });
  } catch (error) {
    console.error('Failed to create supply order:', error);
    return NextResponse.json({ error: 'Failed to create supply order' }, { status: 500 });
  }
}

export async function PUT(request: NextRequest) {
  try {
    const updatedSupplyOrder = await request.json();

    if (!updatedSupplyOrder.id) {
      return NextResponse.json(
        { error: 'Supply order ID is required' },
        { status: 400 }
      );
    }

    const supplyOrder = await prisma.supplyOrder.update({
      where: { id: updatedSupplyOrder.id },
      data: {
        ...(updatedSupplyOrder.supplierId && { supplierId: updatedSupplyOrder.supplierId }),
        ...(updatedSupplyOrder.warehouseId !== undefined && { warehouseId: updatedSupplyOrder.warehouseId }),
        ...(updatedSupplyOrder.totalAmount !== undefined && { totalAmount: updatedSupplyOrder.totalAmount }),
        ...(updatedSupplyOrder.notes !== undefined && { notes: updatedSupplyOrder.notes }),
        ...(updatedSupplyOrder.supplyDate && { supplyDate: new Date(updatedSupplyOrder.supplyDate) }),
        ...(updatedSupplyOrder.employeeName && { employeeName: updatedSupplyOrder.employeeName })
      },
      include: {
        items: true
      }
    });

    return NextResponse.json(supplyOrder);
  } catch (error) {
    console.error('Failed to update supply order:', error);
    return NextResponse.json({ error: 'Failed to update supply order' }, { status: 500 });
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { id } = await request.json();

    if (!id) {
      return NextResponse.json(
        { error: 'Supply order ID is required' },
        { status: 400 }
      );
    }

    // Delete supply order items first, then the order
    await prisma.supplyOrderItem.deleteMany({
      where: { supplyOrderId: id }
    });

    await prisma.supplyOrder.delete({
      where: { id }
    });

    return NextResponse.json({ message: 'Supply order deleted successfully' });
  } catch (error) {
    console.error('Failed to delete supply order:', error);
    return NextResponse.json({ error: 'Failed to delete supply order' }, { status: 500 });
  }
}
